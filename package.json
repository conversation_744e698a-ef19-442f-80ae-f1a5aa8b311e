{"name": "antd-demo", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.4.0", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@supabase/supabase-js": "^2.50.0", "@tinymce/tinymce-react": "^6.2.1", "@xyflow/react": "^12.4.3", "ace-builds": "^1.35.4", "antd": "^5.22.6", "appwrite": "^18.1.1", "classnames": "^2.5.1", "dayjs": "^1.11.12", "echarts": "^5.6.0", "hotkeys-js": "^3.13.9", "immer": "^10.1.1", "lodash-es": "^4.17.21", "normalize.css": "^8.0.1", "react": "^19.1.0", "react-countup": "^6.5.3", "react-dom": "^19.1.0", "react-quill-new": "^3.4.6", "react-router-dom": "^6.26.1", "rxjs": "^7.8.1", "signature_pad": "^5.0.4", "url-pattern": "^1.0.3", "use-immer": "^0.10.0", "uuid": "^10.0.0", "zustand": "^4.5.5"}, "devDependencies": {"@bpmn-io/properties-panel": "^3.26.1", "@types/lodash-es": "^4.17.12", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/tinymce": "^4.6.9", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react-swc": "^3.5.0", "axios": "^1.7.9", "bpmn-js": "^18.3.0", "bpmn-js-properties-panel": "^5.32.0", "camunda-bpmn-moddle": "^7.0.1", "crypto-js": "^4.2.0", "dompurify": "^3.2.3", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "html5-qrcode": "^2.3.8", "less": "^4.2.0", "nanoid": "^5.0.9", "plyr": "^3.7.8", "typescript": "^5.2.2", "video.js": "^8.21.0", "vite": "^5.0.8", "zeebe-bpmn-moddle": "^1.9.0"}}