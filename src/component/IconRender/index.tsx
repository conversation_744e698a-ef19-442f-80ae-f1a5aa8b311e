import React from 'react';
import { Image } from 'antd';
import * as Icons from '@ant-design/icons';
import type { IconComponentProps } from '@ant-design/icons/lib/components/Icon';

export type IconSource = 'antd' | 'url' | 'custom';

interface IconRenderProps {
  iconName: string;
  iconSource?: IconSource;
  customIcons?: Array<{
    name: string;
    icon: React.ReactNode;
  }>;
  style?: React.CSSProperties;
}

const IconRender: React.FC<IconRenderProps> = ({
  iconName,
  iconSource = 'antd',
  customIcons = [],
  style = { width: 24, height: 24 }
}) => {
  if (!iconName) return null;

  switch (iconSource) {
    case 'antd':
      const IconComponent = Icons[iconName as keyof typeof Icons] as React.ComponentType<IconComponentProps>;
      return IconComponent ? <IconComponent  /> : null;
    
    case 'url':
      return <Image src={iconName} alt="icon" style={style} />;
    
    case 'custom':
      const customIcon = customIcons.find(icon => icon.name === iconName);
      return customIcon?.icon || null;
    
    default:
      return null;
  }
};

export default IconRender; 