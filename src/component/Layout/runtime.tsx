import React, { useEffect, useState } from "react";
import {
  Menu,
  Layout,
  But<PERSON>,
  theme,
  Dropdown,
  Avatar,
  Space,
  Flex,
} from "antd";
import type { MenuProps } from 'antd';
import type { ItemType, MenuItemType, SubMenuType, MenuDividerType, MenuItemGroupType } from 'antd/lib/menu/interface';
import {
  AppstoreOutlined,
  MailOutlined,
  ProductOutlined,
  UserOutlined,
  LogoutOutlined,
} from "@ant-design/icons";
import IconRender from '@/component/IconRender';

import { message } from "@/store/hooks";

import { useParams, useNavigate, Link } from "react-router-dom";

import { getClient } from '@/client';

import useStore from "@/store";

import logo from "/logo.png";

const { Sider, Content, Header, Footer } = Layout;

const client = getClient();

interface MenuItem {
  id: string;
  name: string;
  code: string;
  path: string;
  icon: string;
  type: string;
  menu_type: string;
  parent_id: string | null;
  sort: number;
  status: 0 | 1;
  created_at: string;
  updated_at: string;
  children?: MenuItem[];
  page_code?: string;
}

// 定义自定义 Ant Design Menu Item Types
interface CustomMenuItemType extends MenuItemType {
  path?: string;
  page_code?: string;
}

interface CustomSubMenuType extends Omit<SubMenuType, 'children'> {
  children: CustomItemType[];
  path?: string;
  page_code?: string;
}

type CustomItemType = CustomMenuItemType | CustomSubMenuType | MenuDividerType | MenuItemGroupType;

const rightItems: CustomItemType[] = [
  {
    label: (
      <Link to={`/dashboard`}>
        应用管理
      </Link>
    ),
    key: "app",
    icon: <ProductOutlined />,
  },
  {
    type: "divider",
  },
  {
    label: (
      <Link to={`/profile`}>
        用户中心
      </Link>
    ),
    key: "user",
    icon: <UserOutlined />,
  },
  {
    type: "divider",
  },
  {
    label: "退出登录",
    key: "login",
    icon: <LogoutOutlined />,
    onClick: async () => { await client.auth.signOut(); }
  },
];

const initialMenuItems: CustomItemType[] = [
  {
    key: "sub1",
    label: "邮箱",
    icon: <MailOutlined />,
    children: [
      {
        key: "g1",
        label: "Item 1",
        type: "group",
        children: [
          { key: "1", label: "Option 1" },
          { key: "2", label: "Option 2" },
        ],
      },
      {
        key: "g2",
        label: "Item 2",
        type: "group",
        children: [
          { key: "3", label: "Option 3" },
          { key: "4", label: "Option 4" },
        ],
      },
    ],
  },
  {
    key: "sub2",
    label: "应用",
    icon: <AppstoreOutlined />,
    children: [
      { key: "5", label: "Option 5" },
      { key: "6", label: "Option 6" },
      {
        key: "sub3",
        label: "Submenu",
        children: [
          { key: "7", label: "Option 7" },
          { key: "8", label: "Option 8" },
        ],
      },
    ],
  },
];

const headerStyle: React.CSSProperties = {
  textAlign: "center",
  color: "#fff",
  height: 64,
  paddingInline: 48,
  lineHeight: "64px",
  backgroundColor: "#ff6f00",
  display: "flex",
  alignItems: "center",
};

const contentStyle: React.CSSProperties = {
  color: "#fff",
  margin: 24,
};

const siderStyle: React.CSSProperties = {
  lineHeight: "120px",
  color: "#fff",
  height: "100vh",
  overflow: "auto",
  position: "sticky",
  insetInlineStart: 0,
  top: 0,
  bottom: 0,
  scrollbarWidth: "thin",
  scrollbarGutter: "stable",
};

const layoutStyle = {
  width: "100vw",
  maxWidth: "100vw",
  height: "100vh",
  overflow: "auto",
};

const RuntimeLayout = ({ children }: React.PropsWithChildren<{}>) => {
  const {
    token: { colorBgContainer, colorText, borderRadiusLG },
  } = theme.useToken();

  const { params: paramsStore, updateParams } = useStore();

  const [menuItems, setMenuItems] = useState<CustomItemType[]>(initialMenuItems);
  const [collapsed, setCollapsed] = useState(false);
  const [defaultSelectedKeys, setDefaultSelectedKeys] = useState<string[]>([]);
  const [openKeys, setOpenKeys] = useState<string[]>([]);
  const { appId, pageId } = useParams();
  const naviagate = useNavigate();

  // 辅助函数：通过 key 查找菜单项
  const findMenuItemByKey = (key: string, items: ItemType[]): CustomMenuItemType | CustomSubMenuType | undefined => {
    for (const item of items) {
      // 过滤掉 null/undefined items，并确保它们是对象类型且有 'key' 属性
      if (item === null || item === undefined || typeof item !== 'object' || !('key' in item)) {
        continue;
      }

      if (item.key === key) {
        // 找到匹配项，强制转换为我们自定义的类型
        return item as CustomMenuItemType | CustomSubMenuType;
      }

      // 检查 item 是否有 children 属性并且是数组
      if ('children' in item && Array.isArray(item.children)) {
        // 递归调用，并确保 children 是 ItemType[] 类型
        const found = findMenuItemByKey(key, item.children as ItemType[]);
        if (found) {
          return found;
        }
      }
    }
    return undefined;
  };

  useEffect(() => {
    getMenu();
    updateParams({ ...paramsStore, mode: "preview" });
  }, [appId]);

  // 新增：根据 is_home 自动选中并跳转首页，并自动展开父节点
  useEffect(() => {
    if (menuItems && menuItems.length > 0) {
      // 递归查找 is_home 为 true 的菜单项
      const findHome = (items: CustomItemType[], parents: string[] = []): { item?: CustomMenuItemType, parents: string[] } => {
        for (const item of items) {
          if (item && typeof item === 'object' && 'is_home' in item && (item as any).is_home) {
            return { item: item as CustomMenuItemType, parents };
          }
          if ('children' in item && Array.isArray(item.children)) {
            const found = findHome(item.children as CustomItemType[], [...parents, item.key as string]);
            if (found.item) return found;
          }
        }
        return { parents: [] };
      };
      const { item: homeItem, parents } = findHome(menuItems);
      if (homeItem) {
        setDefaultSelectedKeys([homeItem.key as string]);
        setOpenKeys(parents);
        if (homeItem.page_code) {
          naviagate(`/app/${appId}/page/${homeItem.page_code}/runtime`);
        }
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [menuItems]);

  const getMenu = async () => {
    try {
      const menus: MenuItem[] = await client.database.read('menu');
      // 构建树形结构
      const buildTree = (items: MenuItem[]): MenuItem[] => {
        const itemMap: { [key: string]: MenuItem } = {};
        const result: MenuItem[] = [];

        // 创建映射表
        items.forEach(item => {
          itemMap[item.id] = { ...item, children: [] };
        });

        // 构建树
        items.forEach(item => {
          const node = itemMap[item.id];
          if (item.parent_id && itemMap[item.parent_id]) {
            itemMap[item.parent_id].children?.push(node);
          } else {
            result.push(node);
          }
        });

        // 递归排序子节点
        const sortChildren = (nodes: MenuItem[]): MenuItem[] => {
          return nodes.map(node => {
            const sortedNode = { ...node };
            if (sortedNode.children && sortedNode.children.length > 0) {
              sortedNode.children.sort((a: MenuItem, b: MenuItem) => a.sort - b.sort);
              sortedNode.children = sortChildren(sortedNode.children);
            }
            return sortedNode;
          });
        };

        const sortedResult = sortChildren(result);
        sortedResult.sort((a: MenuItem, b: MenuItem) => a.sort - b.sort);
        return sortedResult;
      };

      const tree = buildTree(menus);

      // 转换菜单格式
      const convertMenu = (items: MenuItem[]): CustomItemType[] => {
        return items.map(item => {
          if (item.menu_type === 'directory') {
            // 目录类型始终有一个 children 数组，即使为空
            const children = item.children ? convertMenu(item.children) : [];
            return {
              key: item.code,
              label: item.name,
              icon: item.icon ? getIconComponent(item.icon) : null,
              path: item.path,
              page_code: item.page_code,
              is_home: (item as any).is_home,
              children: children
            } as CustomSubMenuType;
          } else {
            // 菜单类型没有 children
            return {
              key: item.code,
              label: item.name,
              icon: item.icon ? getIconComponent(item.icon) : null,
              path: item.path,
              page_code: item.page_code,
              is_home: (item as any).is_home,
            } as CustomMenuItemType;
          }
        });
      };

      const menuTree = convertMenu(tree);

      // 设置默认选中项
      menus.forEach((item) => {
        if (item?.page_code === pageId) {
          setDefaultSelectedKeys([item.code]);
        }
      });

      setMenuItems(menuTree);
    } catch (error) {
      message.error('获取菜单列表失败');
      console.error(error);
    }
  };

  // 获取图标组件
  const getIconComponent = (iconName: string): React.ReactNode | null => {
    if (!iconName) return null;
    return <IconRender iconName={iconName} />;
  };

  // 处理路由跳转
  const onClick: MenuProps['onClick'] = (info) => {
    setDefaultSelectedKeys([info.key]); // 切换菜单时高亮同步变化
    const clickedItem = findMenuItemByKey(info.key, menuItems); // 使用辅助函数查找菜单项
    if (clickedItem && 'page_code' in clickedItem) {
      const { page_code } = clickedItem;
      if (page_code) {
        naviagate(`/app/${appId}/page/${page_code}/runtime`);
      } else {
        console.warn("page_code 或者 当前项为目录非菜单")
      }
    } else {
      console.warn("未找到菜单项或该项不是有效的页面链接");
    }
  };

  // if (defaultSelectedKeys.length === 0) {
  //   console.error("请检查是否有初始化菜单")
  //   return null; // 初始化时有选中的菜单时才渲染
  // }

  return (
    <Layout style={layoutStyle} hasSider>
      <Sider
        width={collapsed ? 80 : 256}
        style={siderStyle}
        collapsible
        collapsed={collapsed}
        theme="light"
        onCollapse={(value) => setCollapsed(value)}
      >
        <div className="winbase-logo">
          <img src={logo} height={36} width={33} alt="" />
          {collapsed ? (
            ""
          ) : (
            <span
              className="winbase-name"
              style={{
                color: colorText,
              }}
            >
              ASTX
            </span>
          )}
        </div>

        <Menu
          onClick={onClick}
          mode="inline"
          items={menuItems}
          selectedKeys={defaultSelectedKeys}
          openKeys={openKeys}
          onOpenChange={setOpenKeys}
        />
      </Sider>
      <Layout>
        <Header
          style={{
            padding: 0,
            background: colorBgContainer,
            position: "sticky",
            top: 0,
            zIndex: 1,
            width: "100%",
            borderBottom: "1px solid #eee",
          }}
        >
          <Flex justify="space-between">
            <span></span>
            <Flex justify="flex-end">
              <Space>
                <Dropdown menu={{ items: rightItems }}>
                  <Button
                    type="text"
                    size="large"
                    icon={
                      <Avatar
                        size={24}
                        shape="square"
                        src={<img src={logo} alt="avatar" />}
                      />
                    }
                  >
                    winyh
                  </Button>
                </Dropdown>
              </Space>
            </Flex>
          </Flex>
        </Header>
        <Content style={contentStyle}>{children}</Content>
        <Footer style={{ textAlign: 'center' }}>
          winyh ©{new Date().getFullYear()} xdash
        </Footer>
      </Layout>
    </Layout>
  );
};

export default RuntimeLayout;
