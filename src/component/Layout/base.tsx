import { useState, useEffect, ReactNode } from 'react';
import { useNavigate, useLocation, Outlet } from "react-router-dom";
import {
    DashboardOutlined,
    FilePptOutlined,
    ControlOutlined,
    MoonOutlined,
    SunOutlined,
    UserOutlined,
    LogoutOutlined,
    FontColorsOutlined,
    SettingOutlined,
    SafetyCertificateOutlined
} from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { Flex, Layout, Menu, theme, Space, Tooltip, Button, Avatar, Dropdown, Divider } from 'antd';
import useStore from "@/store/index";
import { Storage } from "@/utils/storage";
import winbaseLogo from "/logo.png";
import "./index.css";

const { Header, Content, Footer, Sider } = Layout;
const basename = import.meta.env.BASE_URL;

interface AppBaseProps {
    children?: ReactNode;
}

const AppBase = ({ children }: AppBaseProps) => {
    const [collapsed, setCollapsed] = useState(false);
    const themeMode = useStore((state) => state.themeMode);
    const [mode, setMode] = useState<any>(themeMode);
    const {
        token: { colorBgContainer, borderRadiusLG, colorText },
    } = theme.useToken();
    const toggleTheme = useStore((state) => state.toggleTheme);
    const [selectedKeys, setSelectedKeys] = useState<any>([]);
    const [items, setItems] = useState<any>([]);
    const { pathname, search } = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        const currentPath = pathname.split('/')[1] || 'dashboard';
        setSelectedKeys([currentPath]);

        setItems([
            {
                label: "控制台",
                key: "dashboard",
                icon: <DashboardOutlined />,
            },
            {
                label: "页面管理",
                key: "page",
                icon: <FilePptOutlined />,
            },
            {
                label: "导航管理",
                key: "menu",
                icon: <ControlOutlined />
            },
            {
                label: "权限管理",
                key: "permission",
                icon: <SafetyCertificateOutlined />
            },
            {
                label: "应用设置",
                key: "setting",
                icon: <SettingOutlined />
            }
        ]);
    }, [pathname]);

    const onMenuClick = ({ key }: { key: string }) => {
        navigate(`${basename}${key}`);
    };


    const onSwitchMode = () => {
        let newMode = mode === "light" ? "dark" : "light";
        Storage.setItem("themeMode", newMode);
        setMode(newMode);
        toggleTheme(newMode);
    };

    const onLogout = () => {
        Storage.removeItem("token");
        navigate(`/login?redirect=${pathname}${search}`);
    };

    const goProfile = () => {
        navigate(`/profile`);
    };

    const AvatarItems: MenuProps['items'] = [
        {
            key: '1',
            label: "个人中心",
            icon: <UserOutlined />,
            onClick: goProfile,
        },
        {
            type: "divider",
        },
        {
            key: '2',
            label: "退出登录",
            icon: <LogoutOutlined />,
            onClick: onLogout,
        }
    ];

    const LanguageItems: MenuProps['items'] = [
        {
            key: '1',
            label: "中文",
        },
        {
            key: '2',
            label: "English",
        }
    ];

    const goHome = () => {
        navigate("/dashboard")
    }

    return (
        <Layout style={{ minHeight: '100vh' }}>
            <Sider theme={mode} collapsible collapsed={collapsed} onCollapse={(value) => setCollapsed(value)}>
                <div className="winbase-logo" onClick={goHome}>
                    <img src={winbaseLogo} height={36} width={33} alt="" />{" "}
                    {collapsed ? (
                        ""
                    ) : (
                        <span
                            className="winbase-name"
                            style={{
                                color: colorText,
                            }}
                        >
                            winbase
                        </span>
                    )}
                </div>
                <Menu theme={mode} items={items} selectedKeys={selectedKeys} onSelect={onMenuClick} />
            </Sider>
            <Layout>
                <Header style={{ padding: '0 24px', background: colorBgContainer }}>
                    <Flex justify="flex-end" align="center">
                        <Space split={<Divider type="vertical" />}>
                            <Space>
                                <Tooltip title="切换模式">
                                    <Button
                                        type="text"
                                        icon={mode === "light" ? <MoonOutlined /> : <SunOutlined />}
                                        onClick={onSwitchMode}
                                    ></Button>
                                </Tooltip>

                                <Dropdown menu={{ items: LanguageItems }} placement="bottom">
                                    <Button
                                        type="text"
                                        icon={<FontColorsOutlined />}
                                    ></Button>
                                </Dropdown>
                            </Space>

                            <Dropdown menu={{ items: AvatarItems }} placement="bottom">
                                <Space style={{ cursor: "pointer" }}>
                                    <Avatar size={32} style={{ padding: 6 }} src={winbaseLogo} shape="square" />
                                    <span>winyh</span>
                                </Space>
                            </Dropdown>
                        </Space>
                    </Flex>
                </Header>
                <Content style={{ margin: 16, padding: 16, borderRadius: borderRadiusLG, background: colorBgContainer }}>
                    {children ? children : <Outlet />}
                </Content>
                <Footer style={{ textAlign: 'center' }}>
                    winyh ©{new Date().getFullYear()} xdash
                </Footer>
            </Layout>
        </Layout>
    );
};

export default AppBase;