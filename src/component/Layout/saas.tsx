import { useState, useEffect, ReactNode } from 'react';
import { useNavigate, useLocation, Outlet } from "react-router-dom";
import {
    DashboardOutlined,
    AppstoreOutlined,
    TeamOutlined,
    SettingOutlined,
    UserOutlined,
    LogoutOutlined,
    FontColorsOutlined,
    MoonOutlined,
    SunOutlined,
    SafetyCertificateOutlined,
    BarChartOutlined
} from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { Flex, Layout, Menu, theme, Space, Tooltip, Button, Avatar, Dropdown, Divider } from 'antd';
import useStore from "@/store/index";
import { Storage } from "@/utils/storage";
import winbaseLogo from "/logo.png";
import "../Layout/index.css";

const { Header, Content, Footer, Sider } = Layout;
const basename = import.meta.env.BASE_URL;

interface SaaSLayoutProps {
    children?: ReactNode;
}

const SaaSLayout = ({ children }: SaaSLayoutProps) => {
    const navigate = useNavigate();
    const location = useLocation();
    const { pathname, search } = location;
    
    const [collapsed, setCollapsed] = useState(false);
    const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
    const [items, setItems] = useState<MenuProps['items']>([]);
    
    const { mode, setMode, toggleTheme } = useStore();
    const {
        token: { colorBgContainer, colorText, borderRadiusLG },
    } = theme.useToken();

    useEffect(() => {
        // 根据当前路径设置选中的菜单项
        const pathSegments = pathname.split('/').filter(Boolean);
        const currentPath = pathSegments[1] || 'dashboard'; // saas/dashboard -> dashboard
        setSelectedKeys([currentPath]);

        // 设置SaaS平台菜单项
        setItems([
            {
                label: "数据分析",
                key: "dashboard",
                icon: <DashboardOutlined />,
            },
            {
                label: "应用管理",
                key: "apps",
                icon: <AppstoreOutlined />,
            },
            {
                label: "租户管理",
                key: "tenant",
                icon: <TeamOutlined />
            },
            {
                label: "数据分析",
                key: "analysis",
                icon: <BarChartOutlined />
            },
            {
                label: "平台权限",
                key: "authority",
                icon: <SafetyCertificateOutlined />
            },
            {
                label: "系统配置",
                key: "system",
                icon: <SettingOutlined />
            },
            {
                label: "平台账户",
                key: "user",
                icon: <UserOutlined />
            }
        ]);
    }, [pathname]);

    const onMenuClick = ({ key }: { key: string }) => {
        navigate(`/saas/${key}`);
    };

    const onSwitchMode = () => {
        let newMode = mode === "light" ? "dark" : "light";
        Storage.setItem("themeMode", newMode);
        setMode(newMode);
        toggleTheme(newMode);
    };

    const onLogout = () => {
        Storage.removeItem("token");
        navigate(`/login?redirect=${pathname}${search}`);
    };

    const goProfile = () => {
        navigate(`/profile`);
    };

    const AvatarItems: MenuProps['items'] = [
        {
            key: '1',
            label: "个人中心",
            icon: <UserOutlined />,
            onClick: goProfile,
        },
        {
            type: "divider",
        },
        {
            key: '2',
            label: "退出登录",
            icon: <LogoutOutlined />,
            onClick: onLogout,
        }
    ];

    const LanguageItems: MenuProps['items'] = [
        {
            key: '1',
            label: "中文",
        },
        {
            key: '2',
            label: "English",
        }
    ];

    const goHome = () => {
        navigate("/saas/dashboard")
    }

    return (
        <Layout style={{ minHeight: '100vh' }}>
            <Sider theme={mode} collapsible collapsed={collapsed} onCollapse={(value) => setCollapsed(value)}>
                <div className="winbase-logo" onClick={goHome}>
                    <img src={winbaseLogo} height={36} width={33} alt="" />{" "}
                    {collapsed ? (
                        ""
                    ) : (
                        <span
                            className="winbase-name"
                            style={{
                                color: colorText,
                            }}
                        >
                            WinDash SaaS
                        </span>
                    )}
                </div>
                <Menu theme={mode} items={items} selectedKeys={selectedKeys} onSelect={onMenuClick} />
            </Sider>
            <Layout>
                <Header style={{ padding: '0 24px', background: colorBgContainer }}>
                    <Flex justify="flex-end" align="center">
                        <Space split={<Divider type="vertical" />}>
                            <Space>
                                <Tooltip title="切换模式">
                                    <Button
                                        type="text"
                                        icon={mode === "light" ? <MoonOutlined /> : <SunOutlined />}
                                        onClick={onSwitchMode}
                                    ></Button>
                                </Tooltip>

                                <Dropdown menu={{ items: LanguageItems }} placement="bottom">
                                    <Button
                                        type="text"
                                        icon={<FontColorsOutlined />}
                                    ></Button>
                                </Dropdown>
                            </Space>

                            <Dropdown menu={{ items: AvatarItems }} placement="bottom">
                                <Space style={{ cursor: "pointer" }}>
                                    <Avatar size={32} style={{ padding: 6 }} src={winbaseLogo} shape="square" />
                                    <span>管理员</span>
                                </Space>
                            </Dropdown>
                        </Space>
                    </Flex>
                </Header>
                <Content style={{ margin: 16, padding: 16, borderRadius: borderRadiusLG, background: colorBgContainer }}>
                    {children ? children : <Outlet />}
                </Content>
                <Footer style={{ textAlign: 'center' }}>
                    WinDash SaaS Platform ©{new Date().getFullYear()} Created by winyh
                </Footer>
            </Layout>
        </Layout>
    );
};

export default SaaSLayout;
