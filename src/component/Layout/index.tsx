import { useEffect } from "react";
import { Outlet, useParams, useLocation } from "react-router-dom";
import { Storage } from "@/utils/storage";
import useStore from "@/store/index";
import { getClient } from '@/client';

const client = getClient();
import "./index.css";

const LayoutBase = () => {
  const location = useLocation();
  const params = useParams();

  const {
    updateLoading,
    updateJsonSchema,
    updateParams,
    params: paramsStore,
  } = useStore();

  let mode = "design";
  if (!location.pathname.includes("design")) {
    mode = "preview";
  }

  useEffect(() => {
    updateParams({ ...paramsStore, mode });
  }, [params.pageId]);

  useEffect(() => {
    getCurrentPage();
  }, [params.pageId]);

  useEffect(() => {
    // console.log(`路由变化监听：${location.pathname}`);
    onRouteChange();
  }, [location.pathname]);

  const onRouteChange = async () => { };

  const getCurrentPage = async () => {
    updateLoading(true);
    try {
      // 查询记录
      const page = await client.database.readByUniqueKey('page', "code",  params?.pageId);
      if (page) {
        updateJsonSchema(page.schema || []);
        Storage.setItem("page", page);
      }
    } catch (error) {
      updateLoading(false);
    } finally {
      updateLoading(false);
    }
  };

  return (
    <>
      <Outlet />
    </>
  );
};

export default LayoutBase;
