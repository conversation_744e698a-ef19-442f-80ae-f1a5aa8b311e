import { useState, useEffect } from "react";
import { But<PERSON>, Dropdown, Config<PERSON><PERSON>ider, Popover } from "antd";
import hotkeys from "hotkeys-js";
import {
  CopyOutlined,
  DeleteOutlined,
  VerticalAlignTopOutlined,
} from "@ant-design/icons";

import { message } from "@/store/hooks";

import {
  getObjectById,
  copyElementByUuid,
  removeElementByUuid,
  getFatherOrSiblingByUuid,
  getFatherByUuid,
  getParentPathByUuid,
} from "@/utils/index";

import useStore from "@/store";

import "./index.css";

/* 高亮选中的 dnd 包裹元素并加工具条 */
const SelectedBar = ({ item, open, children }) => {
  const [parentDisabled, setParentDisabled] = useState(false);

  const { selectedId, jsonSchema, updateJsonSchema, updateSelectedId } =
    useStore();

  const { uuid } = item;

  const selectedPath = getParentPathByUuid(jsonSchema, selectedId);

  const transPath = (selectedPath = []) => {
    let parent = [];
    selectedPath.map((item, index) => {
      if (index < selectedPath.length - 1) {
        parent.push({
          label: item.componentName,
          key: item.uuid,
          onClick: () => onSomeContainer(item),
        });
      }
    });
    return parent;
  };

  const menuItems = [...transPath(selectedPath).reverse()];

  useEffect(() => {
    hotkeys("shift+Delete, shift+Backspace", () => {
      const newSchema = removeElementByUuid(jsonSchema, selectedId);
      updateJsonSchema(newSchema);
      return false;
    });

    hotkeys("shift+c, shift+c", () => {
      let newSchema = copyElementByUuid(jsonSchema, selectedId);
      updateJsonSchema(newSchema);
      return false;
    });
  }, [selectedId]);

  const onSomeContainer = (item) => {
    let selected = getObjectById(jsonSchema, item.uuid);
    updateSelectedId(`${selected.uuid}`);
  };

  const onParent = (event) => {
    event.stopPropagation();
    let father = getFatherByUuid(jsonSchema, uuid);

    if (father) {
      updateSelectedId(`${father.uuid}`);
      setParentDisabled(false);
    } else {
      setParentDisabled(true);
      message.warning("当前元素无上级容器");
    }
  };

  const onCopy = (event) => {
    event.stopPropagation();
    const newSchema = copyElementByUuid(jsonSchema, uuid);
    updateJsonSchema(newSchema);
  };

  const onDelete = (event) => {
    event.stopPropagation();
    const nextComponent = getFatherOrSiblingByUuid(jsonSchema, uuid);
    if (nextComponent?.sibling?.length === 0) {
      setParentDisabled(true);
    }
    if (nextComponent) {
      updateSelectedId(`${nextComponent?.uuid}`);
    }
    const newSchema = removeElementByUuid(jsonSchema, uuid);
    updateJsonSchema(newSchema);
  };

  return (
    <ConfigProvider
      theme={{
        components: {
          Button: {
            fontSize: 13,
          },
        },
      }}
    >
      <Popover
        open={open}
        arrow={false}
        placement="topRight"
        autoAdjustOverflow
        trigger="click"
        color="#ffa500"
        styles={{ body: { padding: 4 } }}
        content={
          <div>
            <Dropdown
              menu={{ items: menuItems }}
              disabled={menuItems.length === 0}
            >
              <Button
                type="text"
                size="small"
                icon={<VerticalAlignTopOutlined />}
                style={{ padding: "0.5px 4px" }}
                onClick={onParent}
                disabled={parentDisabled || menuItems.length === 0}
              >
                容器
              </Button>
            </Dropdown>

            <Button
              type="text"
              size="small"
              icon={<CopyOutlined />}
              style={{ padding: "0.5px 4px" }}
              onClick={onCopy}
            >
              复制
            </Button>
            <Button
              type="text"
              size="small"
              icon={<DeleteOutlined />}
              style={{ padding: "0.5px 4px" }}
              onClick={onDelete}
            >
              删除
            </Button>
          </div>
        }
      >
        {children}
      </Popover>
    </ConfigProvider>
  );
};

export { SelectedBar };

export default SelectedBar;
