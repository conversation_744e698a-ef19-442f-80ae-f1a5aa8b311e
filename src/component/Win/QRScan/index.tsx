import React, { useEffect, useRef, useState } from "react";
import { Html5Qrcode } from "html5-qrcode";

const QRScan = () => {
  const [decodedText, setDecodedText] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [isScannerRunning, setIsScannerRunning] = useState(false);
  const html5QrCodeRef = useRef(null);
  const readerRef = useRef(null);

  useEffect(() => {
    // 创建 Html5Qrcode 实例
    html5QrCodeRef.current = new Html5Qrcode(readerRef.current.id);

    // 启动扫描
    const startScanning = async () => {
      const cameras = await Html5Qrcode.getCameras();
      if (cameras && cameras.length) {
        setIsScannerRunning(true);
        const cameraId = cameras[0].id;
        html5QrCodeRef.current
          .start(
            cameraId,
            {
              fps: 10,
              qrbox: { width: 250, height: 250 },
            },
            (decodedText) => {
              setDecodedText(decodedText);
            },
            (errorMessage) => {
              setErrorMessage(errorMessage);
            }
          )
          .catch((err) => {
            setIsScannerRunning(false);
            console.error("扫描失败:", err);
          });
      }
    };

    startScanning();

    // 清理
    return () => {
      if (isScannerRunning) {
        html5QrCodeRef.current
          .stop()
          .then(() => setIsScannerRunning(false))
          .catch((err) => console.error("停止失败:", err));
      }
    };
  }, []);

  // 处理文件上传
  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      html5QrCodeRef.current
        .scanFile(file, true)
        .then((decodedText) => {
          setDecodedText(decodedText);
        })
        .catch((err) => {
          setErrorMessage(`文件扫描失败: ${err}`);
        });
    }
  };

  return (
    <div>
      <div
        id="reader"
        ref={readerRef}
        style={{ width: "600px", height: "600px" }}
      />
      <input type="file" accept="image/*" onChange={handleFileChange} />
      {decodedText && <div>解码结果: {decodedText}</div>}
      {errorMessage && <div style={{ color: "red" }}>错误: {errorMessage}</div>}
    </div>
  );
};

export default QRScan;
