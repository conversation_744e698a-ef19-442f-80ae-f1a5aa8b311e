const timePickerSchema = {
  version: "0.0.1",
  css: "",
  i18n: {},
  componentName: "TimePicker",
  title: "时间选择",
  description: "用于选择或输入时间，支持自定义时间格式、禁用部分时间等。",
  docUrl: "https://ant-design.antgroup.com/components/time-picker-cn",
  screenshot: "",
  icon: "",
  keywords: "时间选择器，时间选择框",
  snippets: [],
  group: "common",
  category: "basic", // 基础组件
  priority: 0,
  sort: 0, // 排序
  children: "", // 时间选择器通常没有子元素
  props: [
    {
      name: "allowClear",
      title: "清除按钮",
      defaultValue: true,
    },
    {
      name: "disabled",
      title: "禁用状态",
      defaultValue: false,
    },
    {
      name: "disabledHours",
      title: "不可选择的小时",
      defaultValue: null,
    },
    {
      name: "disabledMinutes",
      title: "不可选择的分钟",
      defaultValue: null,
    },
    {
      name: "disabledSeconds",
      title: "不可选择的秒",
      defaultValue: null,
    },
    {
      name: "format",
      title: "时间格式",
      defaultValue: "HH:mm:ss",
    },
    {
      name: "getPopupContainer",
      title: "菜单渲染父节点，默认渲染到 body 上",
      defaultValue: null,
    },
    {
      name: "hideDisabledOptions",
      title: "隐藏被禁用的时间选项",
      defaultValue: false,
    },
    {
      name: "inputReadOnly",
      title: "设置 input 只读，防止移动设备键盘弹出",
      defaultValue: false,
    },
    {
      name: "placeholder",
      title: "占位提示",
      defaultValue: "请选择时间",
    },
    {
      name: "size",
      title: "输入框大小",
      defaultValue: "default",
    },
    {
      name: "value",
      title: "当前选中值",
      defaultValue: null,
    },
    {
      name: "defaultValue",
      title: "默认值",
      defaultValue: null,
    },
    {
      name: "onChange",
      title: "选中时间发生变化时触发",
      defaultValue: null,
    },
    {
      name: "onOpenChange",
      title: "面板打开关闭时触发",
      defaultValue: null,
    },
    {
      name: "picker",
      title: "指定为时间选择还是其他类型的选择",
      defaultValue: "time",
    },
    {
      name: "showNow",
      title: "是否显示'此刻'按钮",
      defaultValue: true,
    },
    {
      name: "use12Hours",
      title: "是否使用 12 小时制",
      defaultValue: false,
    },
    {
      name: "variant",
      title: "是否展示边框样式",
      defaultValue: true,
    },
  ],
  configure: {
    component: {
      isContainer: false,
      isModal: false,
      description: "时间选择框，提供丰富的配置项来满足各种场景的需求。",
      device: ["pc", "mobile"],
      nestingRule: {
        childWhitelist: [], // 时间选择器不接受子元素
        parentWhitelist: [],
      },
    },
    props: [
      {
        name: "allowClear",
        title: "清除按钮",
        defaultValue: true,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "disabled",
        title: "禁用状态",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "disabledHours",
        title: "不可选择的小时",
        defaultValue: null,
        setter: {
          componentName: "FunctionSetter",
          props: {},
        },
      },
      {
        name: "disabledMinutes",
        title: "不可选择的分钟",
        defaultValue: null,
        setter: {
          componentName: "FunctionSetter",
          props: {},
        },
      },
      {
        name: "disabledSeconds",
        title: "不可选择的秒",
        defaultValue: null,
        setter: {
          componentName: "FunctionSetter",
          props: {},
        },
      },
      {
        name: "format",
        title: "时间格式",
        defaultValue: "HH:mm:ss",
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "hideDisabledOptions",
        title: "隐藏被禁用的时间选项",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "inputReadOnly",
        title: "设置 input 只读，防止移动设备键盘弹出",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "placeholder",
        title: "占位提示",
        defaultValue: "请选择时间",
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "size",
        title: "输入框大小",
        defaultValue: "default",
        setter: {
          componentName: "SelectSetter",
          props: {
            style: { width: "100%" },
            options: [
              { value: "large", label: "大" },
              { value: "default", label: "中" },
              { value: "small", label: "小" },
            ],
          },
        },
      },
      {
        name: "value",
        title: "当前选中值",
        defaultValue: null,
        setter: {
          componentName: "StringSetter",
          props: {},
        },
      },
      {
        name: "defaultValue",
        title: "默认值",
        defaultValue: null,
        setter: {
          componentName: "StringSetter",
          props: {},
        },
      },
      {
        name: "onChange",
        title: "选中时间发生变化时触发",
        defaultValue: null,
        setter: {
          componentName: "FunctionSetter",
          props: {},
        },
      },
      {
        name: "onOpenChange",
        title: "面板打开关闭时触发",
        defaultValue: null,
        setter: {
          componentName: "FunctionSetter",
          props: {},
        },
      },
      {
        name: "showNow",
        title: "是否显示'此刻'按钮",
        defaultValue: true,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "use12Hours",
        title: "是否使用 12 小时制",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "variant",
        title: "是否展示边框样式",
        defaultValue: true,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
    ],
  },
};

export { timePickerSchema };

export default timePickerSchema;
