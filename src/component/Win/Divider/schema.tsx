const dividerSchema = {
  version: "0.0.1",
  css: "",
  i18n: {},
  componentName: "Divider",
  title: "分隔线",
  description: "用于将内容分割成清晰的区块，有水平和垂直两种方向。",
  docUrl: "https://ant-design.antgroup.com/components/divider-cn",
  screenshot: "",
  icon: "",
  keywords: "分隔, 布局, 水平分隔线, 垂直分隔线",
  snippets: [],
  group: "common",
  category: "layout", // 布局组件
  priority: 0,
  sort: 0, // 排序
  children: "分割文本",
  props: [
    {
      name: "type",
      title: "类型",
      defaultValue: "horizontal",
    },
    {
      name: "orientation",
      title: "文本位置",
      defaultValue: "center",
    },
    {
      name: "dashed",
      title: "虚线样式",
      defaultValue: false,
    },
    {
      name: "plain",
      title: "纯文本",
      defaultValue: false,
    },
    {
      name: "className",
      title: "自定义类",
      defaultValue: "",
    },
    {
      name: "style",
      title: "内联样式",
      defaultValue: {},
    },
  ],
  methods: {},
  configure: {
    component: {
      isContainer: false,
      isModal: false,
      description:
        "分隔线，用于在页面或组块之间创建视觉上的分隔效果，帮助组织和区分不同的内容区域。",
      device: ["pc", "mobile"],
      nestingRule: {
        childWhitelist: [], // 分隔线不接受子元素
        parentWhitelist: [], // 可以放置在任意容器中
      },
    },
    props: [
      {
        name: "type",
        title: "分隔线类型",
        defaultValue: "horizontal",
        setter: {
          componentName: "SelectSetter",
          props: {
            options: [
              { value: "horizontal", label: "水平" },
              { value: "vertical", label: "垂直" },
            ],
          },
        },
      },
      {
        name: "orientation",
        title: "文本位置",
        defaultValue: "center",
        setter: {
          componentName: "SelectSetter",
          props: {
            options: [
              { value: "left", label: "左对齐" },
              { value: "right", label: "右对齐" },
              { value: "center", label: "居中" },
            ],
          },
        },
      },
      {
        name: "dashed",
        title: "虚线样式",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "plain",
        title: "纯文本",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "className",
        title: "自定义类",
        defaultValue: "",
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "style",
        title: "内联样式",
        defaultValue: {},
        setter: {
          componentName: "ObjectSetter",
          props: {},
        },
      },
    ],
  },
};

export { dividerSchema };

export default dividerSchema;
