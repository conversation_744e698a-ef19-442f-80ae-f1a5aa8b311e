import { useEffect, useRef } from "react";
import videojs from "video.js";
import "video.js/dist/video-js.css";
import "./index.less";

/* Video */
const WinVideo = (props) => {
  // 使用 useRef 创建一个 ref 对象，用于引用视频播放器的 DOM 元素
  const { src = "//vjs.zencdn.net/v/oceans.mp4", type = "video/mp4" } = props;
  const videoRef = useRef(null);
  const playerRef = useRef(null);

  useEffect(() => {
    // 创建 video.js 播放器实例
    const videoElement = videoRef.current;
    playerRef.current = videojs(videoElement, {
      autoplay: false, // 不自动播放
      controls: true, // 显示控制条
      preload: "auto", // 自动预加载
    });

    // 设置视频源
    playerRef.current.src({
      type: type,
      src: src,
    });

    // 组件卸载时销毁播放器
    return () => {
      if (playerRef.current) {
        playerRef.current.dispose();
      }
    };
  }, [src, type]);

  return (
    <div data-vjs-player>
      {/* 使用 ref 绑定视频元素 */}
      <video
        ref={videoRef}
        className="video-js vjs-big-play-centered"
        playsInline
      ></video>
    </div>
  );
};

export { WinVideo };

export default WinVideo;
