const videoSchema = {
  version: "0.0.1",
  css: "",
  i18n: {},
  componentName: "Video",
  title: "视频播放器",
  description: "用于展示视频内容的组件。",
  docUrl: "https://ant-design.antgroup.com/components/card-cn",
  screenshot: "",
  icon: "",
  keywords: "视频，媒体",
  snippets: [],
  group: "common",
  category: "media", // 媒体组件
  priority: 0,
  sort: 0, // 排序
  children: [],
  props: [
    {
      name: "style",
      title: "内联样式",
      defaultValue: {},
    },
  ],
  configure: {
    component: {
      isContainer: true,
      isModal: false,
      description:
        "用于展示内容和功能操作的容器，提供丰富的配置项来满足各种场景的需求。",
      device: ["pc", "mobile"],
      nestingRule: {
        childWhitelist: [], // 卡片可以接受任意子元素
        parentWhitelist: [],
      },
    },
    props: [
      {
        name: "style",
        title: "内联样式",
        defaultValue: {},
        setter: {
          componentName: "ObjectSetter",
          props: {},
        },
      },
    ],
  },
};

export { videoSchema };

export default videoSchema;
