import { useEffect, useRef } from "react";
import "./index.less";

/* Map 腾讯地图 */
const WinMap = (props) => {
  const { align, children, ...restProps } = props;
  const { VITE_TENCENT_MAP_KEY } = import.meta.env;
  const mapRef = useRef(null);
  const SCRIPT_URL = `https://map.qq.com/api/gljs?v=1.exp&key=${VITE_TENCENT_MAP_KEY}&callback=initMap`;
  const CENTER_LAT_LNG = { lat: 39.98412, lng: 116.307484 };
  const ZOOM_LEVEL = 17.2;

  // 定义初始化地图的函数
  // Check if a map instance already exists before creating a new one
  window.initMap = () => {
    if (!mapRef.current || mapRef.current.mapInstance) return; // Prevent re-initialization
    const map = new TMap.Map(mapRef.current, {
      center: new TMap.LatLng(CENTER_LAT_LNG.lat, CENTER_LAT_LNG.lng),
      zoom: ZOOM_LEVEL,
    });
    mapRef.current.mapInstance = map; // Store the map instance for future reference
  };

  // 动态加载腾讯地图API
  useEffect(() => {
    if (document.querySelector(`script[src="${SCRIPT_URL}"]`)) return;
    const script = document.createElement("script");
    script.type = "text/javascript";
    script.src = SCRIPT_URL;
    script.async = true;

    // 添加一个简单的错误处理机制
    script.onerror = () => console.error("Failed to load Tencent Map API.");
    document.body.appendChild(script);

    // 清理函数：移除脚本元素
    return () => {
      document.body.removeChild(script);
    };
  }, []);

  return (
    <div
      ref={mapRef}
      style={{ width: "500px", height: "500px" }}
      {...restProps}
    ></div>
  );
};

export { WinMap };

export default WinMap;
