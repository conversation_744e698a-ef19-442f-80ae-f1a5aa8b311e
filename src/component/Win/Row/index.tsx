import { Row } from "antd";
import "./index.less";

/* 栅格行 */
const WinRow = (props: any) => {
  const {
    style,
    className,
    align,
    gutter = [12, 12],
    justify,
    wrap,
    children,
  } = props;
  return (
    <Row
      style={style}
      className={className}
      align={align}
      gutter={gutter}
      justify={justify}
      wrap={wrap}
    >
      {children}
    </Row>
  );
};

export { WinRow };

export default WinRow;
