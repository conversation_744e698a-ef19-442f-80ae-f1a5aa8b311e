const rowSchema = {
  version: "0.0.1",
  css: "",
  i18n: {},
  componentName: "Row",
  title: "栅格行",
  description: "24栅格系统中的行，用于包裹列组件，所有列（Col）必须放在 Row 内。",
  docUrl: "https://ant-design.antgroup.com/components/grid-cn",
  screenshot: "",
  icon: "",
  keywords: "栅格，行，布局，Grid，Row",
  snippets: [],
  group: "common",
  category: "layout", // 布局组件
  priority: 0,
  sort: 5, // 排序
  npm: {
    package: "antd",
    exportName: "Row",
    subName: "",
    destructuring: "",
    main: "",
    version: "",
  },
  children: [], // 容器组件必须有 children
  props: [
    {
      name: "align",
      title: "垂直对齐",
      description: "垂直对齐方式",
      defaultValue: "top",
    },
    {
      name: "gutter",
      title: "栅格间隔",
      description: "栅格间隔，可以是数字或响应式对象，或者使用数组形式同时设置 [水平间距, 垂直间距]",
      defaultValue: 12,
    },
    {
      name: "justify",
      title: "水平排列",
      description: "水平排列方式",
      defaultValue: "start",
    },
    {
      name: "wrap",
      title: "自动换行",
      description: "是否自动换行",
      defaultValue: true,
    },
    {
      name: "className",
      title: "样式类名",
      description: "样式类名",
      defaultValue: "",
    },
    {
      name: "style",
      title: "行内样式",
      description: "行内样式",
      defaultValue: {},
    },
  ],
  events: [],
  configure: {
    component: {
      isContainer: true,
      isModal: false,
      description: "24栅格系统中的行，用于包裹列组件，所有列（Col）必须放在 Row 内。",
      device: ["pc", "mobile"],
      nestingRule: {
        childWhitelist: ["Col"],
        parentWhitelist: [],
      },
    },
    props: [
      {
        name: "align",
        title: "垂直对齐",
        defaultValue: "top",
        setter: {
          componentName: "SelectSetter",
          props: {
            options: [
              { value: "top", label: "顶部对齐" },
              { value: "middle", label: "居中对齐" },
              { value: "bottom", label: "底部对齐" },
              { value: "stretch", label: "拉伸对齐" },
            ],
          },
        },
      },
      {
        name: "gutter",
        title: "栅格间隔",
        defaultValue: 12,
        setter: {
          componentName: "NumberSetter",
          props: {
            min: 0,
            max: 24,
            step: 1,
            style: { width: "100%" },
          }
        },
      },
      {
        name: "justify",
        title: "水平排列",
        defaultValue: "start",
        setter: {
          componentName: "SelectSetter",
          props: {
            options: [
              { value: "start", label: "起点对齐" },
              { value: "end", label: "终点对齐" },
              { value: "center", label: "居中对齐" },
              { value: "space-around", label: "环绕对齐" },
              { value: "space-between", label: "两端对齐" },
              { value: "space-evenly", label: "均匀对齐" },
            ],
          },
        },
      },
      {
        name: "wrap",
        title: "自动换行",
        defaultValue: true,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "className",
        title: "样式类名",
        defaultValue: "",
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "style",
        title: "行内样式",
        defaultValue: {},
        setter: {
          componentName: "ObjectSetter",
          props: {},
        },
      },
    ],
    supports: {
      events: ["onClick", "onChange"],
      loop: true,
      condition: true,
      style: true,
      className: true,
    },
    advanced: {},
  },
};

export { rowSchema };

export default rowSchema;
