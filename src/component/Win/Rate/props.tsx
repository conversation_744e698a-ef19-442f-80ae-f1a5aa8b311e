/* 评分组件配置 */
const meta = {
  componentName: "Rate",
  version: "0.0.1",
  css: "",
  i18n: {},
  props: {
    allowClear: {
      type: "boolean",
      value: true
    },
    allowHalf: {
      type: "boolean",
      value: false
    },
    count: {
      type: "number",
      value: 5
    },
    defaultValue: {
      type: "number",
      value: 0
    },
    value: {
      type: "number",
      value: 0
    },
    className: [""],
    style: {
      fontSize: 20,
      color: "#FFD700"
    },
    tooltips: {
      type: "array",
      value: []
    }
  },
  events: {
    onChange: {
      type: "JSFunction",
      value: "function(value) { console.log('评分变化:', value) }"
    },
    onHoverChange: {
      type: "JSFunction",
      value: "function(value) { console.log('悬停变化:', value) }"
    }
  },
  methods: {},
  dataSource: {
    list: [],
    dataHandler: {
      type: "JSFunction",
      value: "function(dataMap) { }"
    }
  }
};

export default meta;