import { useState, useRef, useEffect } from "react";
import "./index.less";

const Camera = (props) => {
  const { onRecordingReady, autoStart = false } = props;
  const [isRecording, setIsRecording] = useState(false);
  const [videoUrl, setVideoUrl] = useState(null);
  const [facingMode, setFacingMode] = useState("user"); // 'user' for front camera, 'environment' for back camera
  const [mirror, setMirror] = useState(true); // 是否启用镜像显示，默认为 true
  const mediaRecorderRef = useRef(null);
  const videoChunks = useRef([]);
  const videoRef = useRef(null);

  useEffect(() => {
    startCamera();

    return () => {
      // 清理媒体流以防止内存泄漏
      if (mediaRecorderRef.current) {
        mediaRecorderRef.current.stream
          .getTracks()
          .forEach((track) => track.stop());
      }
    };
  }, [facingMode]);

  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode },
        audio: true,
      });
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }

      // 如果设置了 autoStart，则在获取到流后自动开始录制
      if (autoStart && !isRecording) {
        startRecording();
      }
    } catch (err) {
      console.error("Error accessing the camera.", err);
    }
  };

  const startRecording = () => {
    if (!videoRef.current.srcObject) return;

    mediaRecorderRef.current = new MediaRecorder(videoRef.current.srcObject);

    mediaRecorderRef.current.ondataavailable = (event) => {
      if (event.data.size > 0) {
        videoChunks.current.push(event.data);
      }
    };

    mediaRecorderRef.current.onstop = () => {
      const videoBlob = new Blob(videoChunks.current, { type: "video/webm" });
      const url = URL.createObjectURL(videoBlob);
      setVideoUrl(url);
      videoChunks.current = [];
      if (onRecordingReady) onRecordingReady(url);
    };

    mediaRecorderRef.current.start();
    setIsRecording(true);
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  const toggleFacingMode = () => {
    setFacingMode((currentMode) =>
      currentMode === "user" ? "environment" : "user"
    );
    // Restart camera with new settings
    if (mediaRecorderRef.current) {
      mediaRecorderRef.current.stream
        .getTracks()
        .forEach((track) => track.stop());
      startCamera();
    }
  };

  const toggleMirror = () => {
    setMirror((prev) => !prev);
  };

  return (
    <div>
      <video
        ref={videoRef}
        autoPlay
        playsInline
        muted
        className={`${mirror ? "mirror" : ""}`} // 应用镜像类
      />
      <div>
        <button onClick={toggleFacingMode}>Switch Camera</button>
        <button onClick={toggleMirror}>Toggle Mirror</button>
        <button onClick={startRecording} disabled={isRecording}>
          Start Recording
        </button>
        <button onClick={stopRecording} disabled={!isRecording}>
          Stop Recording
        </button>
      </div>
      {videoUrl && (
        <div>
          <h3>Playback</h3>
          <video src={videoUrl} controls />
        </div>
      )}
    </div>
  );
};

export { Camera };

export default Camera;
