const colSchema = {
  version: "0.0.1",
  css: "",
  i18n: {},
  componentName: "Col",
  title: "栅格列",
  description: "24栅格系统中的列，必须放在 Row 组件内。",
  docUrl: "https://ant-design.antgroup.com/components/grid-cn",
  screenshot: "",
  icon: "",
  keywords: "栅格，列，布局，Grid，Col",
  snippets: [],
  group: "common",
  category: "layout", // 布局组件
  priority: 0,
  sort: 6, // 排序
  npm: {
    package: "antd",
    exportName: "Col",
    subName: "",
    destructuring: "",
    main: "",
    version: "",
  },
  children: [],
  props: [
    {
      name: "span",
      title: "栅格格数",
      description: "栅格占位格数，为 0 时相当于 display: none",
      defaultValue: 6,
    },
    {
      name: "flex",
      title: "Flex布局",
      description: "flex 布局属性",
      defaultValue: undefined,
    },
    {
      name: "offset",
      title: "左侧间隔",
      description: "栅格左侧的间隔格数，间隔内不可以有栅格",
      defaultValue: 0,
    },
    {
      name: "order",
      title: "栅格顺序",
      description: "栅格顺序，flex 布局模式下有效",
      defaultValue: 0,
    },
    {
      name: "pull",
      title: "向左移动",
      description: "栅格向左移动格数",
      defaultValue: 0,
    },
    {
      name: "push",
      title: "向右移动",
      description: "栅格向右移动格数",
      defaultValue: 0,
    },
    {
      name: "className",
      title: "样式类名",
      description: "样式类名",
      defaultValue: "",
    },
    {
      name: "style",
      title: "行内样式",
      description: "行内样式",
      defaultValue: {},
    },
  ],
  configure: {
    component: {
      isContainer: true,
      isModal: false,
      description: "24栅格系统中的列，必须放在 Row 组件内。",
      device: ["pc", "mobile"],
      nestingRule: {
        childWhitelist: ["*"],
        parentWhitelist: [],
      },
    },
    props: [
      {
        name: "span",
        title: "栅格格数",
        defaultValue: 6,
        setter: {
          componentName: "NumberSetter",
          props: {
            min: 0,
            max: 24,
            step: 1,
            style: { width: "100%" }
          },
        },
      },
      {
        name: "flex",
        title: "Flex布局",
        defaultValue: undefined,
        setter: {
          componentName: "MixedSetter",
          props: {
            setters: ["StringSetter", "NumberSetter"]
          }
        },
      },
      {
        name: "offset",
        title: "左侧间隔",
        defaultValue: 0,
        setter: {
          componentName: "NumberSetter",
          props: {
            min: 0,
            max: 24,
            step: 1,
          },
        },
      },
      {
        name: "order",
        title: "栅格顺序",
        defaultValue: 0,
        setter: {
          componentName: "NumberSetter",
          props: {
            step: 1,
          },
        },
      },
      {
        name: "pull",
        title: "向左移动",
        defaultValue: 0,
        setter: {
          componentName: "NumberSetter",
          props: {
            min: 0,
            max: 24,
            step: 1,
          },
        },
      },
      {
        name: "push",
        title: "向右移动",
        defaultValue: 0,
        setter: {
          componentName: "NumberSetter",
          props: {
            min: 0,
            max: 24,
            step: 1,
          },
        },
      },
      {
        name: "xs",
        title: "超小屏幕",
        defaultValue: undefined,
        setter: {
          componentName: "MixedSetter",
          props: {
            setters: [
              {
                componentName: "NumberSetter",
                props: {
                  min: 0,
                  max: 24,
                  step: 1,
                },
              },
              {
                componentName: "ObjectSetter",
                props: {
                  config: {
                    items: [
                      {
                        name: "span",
                        title: "栅格格数",
                        setter: {
                          componentName: "NumberSetter",
                          props: {
                            min: 0,
                            max: 24,
                            step: 1,
                          },
                        }
                      },
                      {
                        name: "offset",
                        title: "左侧间隔",
                        setter: {
                          componentName: "NumberSetter",
                          props: {
                            min: 0,
                            max: 24,
                            step: 1,
                          },
                        }
                      },
                      {
                        name: "order",
                        title: "栅格顺序",
                        setter: {
                          componentName: "NumberSetter",
                          props: {
                            step: 1,
                          },
                        }
                      },
                      {
                        name: "pull",
                        title: "向左移动",
                        setter: {
                          componentName: "NumberSetter",
                          props: {
                            min: 0,
                            max: 24,
                            step: 1,
                          },
                        }
                      },
                      {
                        name: "push",
                        title: "向右移动",
                        setter: {
                          componentName: "NumberSetter",
                          props: {
                            min: 0,
                            max: 24,
                            step: 1,
                          },
                        }
                      }
                    ]
                  }
                }
              }
            ]
          }
        },
      },
      {
        name: "sm",
        title: "小屏幕",
        defaultValue: undefined,
        setter: {
          componentName: "MixedSetter",
          props: {
            setters: [
              {
                componentName: "NumberSetter",
                props: {
                  min: 0,
                  max: 24,
                  step: 1,
                },
              },
              {
                componentName: "ObjectSetter",
                props: {
                  config: {
                    items: [
                      {
                        name: "span",
                        title: "栅格格数",
                        setter: {
                          componentName: "NumberSetter",
                          props: {
                            min: 0,
                            max: 24,
                            step: 1,
                          },
                        }
                      },
                      {
                        name: "offset",
                        title: "左侧间隔",
                        setter: {
                          componentName: "NumberSetter",
                          props: {
                            min: 0,
                            max: 24,
                            step: 1,
                          },
                        }
                      },
                      {
                        name: "order",
                        title: "栅格顺序",
                        setter: {
                          componentName: "NumberSetter",
                          props: {
                            step: 1,
                          },
                        }
                      },
                      {
                        name: "pull",
                        title: "向左移动",
                        setter: {
                          componentName: "NumberSetter",
                          props: {
                            min: 0,
                            max: 24,
                            step: 1,
                          },
                        }
                      },
                      {
                        name: "push",
                        title: "向右移动",
                        setter: {
                          componentName: "NumberSetter",
                          props: {
                            min: 0,
                            max: 24,
                            step: 1,
                          },
                        }
                      }
                    ]
                  }
                }
              }
            ]
          }
        },
      },
      {
        name: "md",
        title: "中等屏幕",
        defaultValue: undefined,
        setter: {
          componentName: "MixedSetter",
          props: {
            setters: [
              {
                componentName: "NumberSetter",
                props: {
                  min: 0,
                  max: 24,
                  step: 1,
                },
              },
              {
                componentName: "ObjectSetter",
                props: {
                  config: {
                    items: [
                      {
                        name: "span",
                        title: "栅格格数",
                        setter: {
                          componentName: "NumberSetter",
                          props: {
                            min: 0,
                            max: 24,
                            step: 1,
                          },
                        }
                      },
                      {
                        name: "offset",
                        title: "左侧间隔",
                        setter: {
                          componentName: "NumberSetter",
                          props: {
                            min: 0,
                            max: 24,
                            step: 1,
                          },
                        }
                      },
                      {
                        name: "order",
                        title: "栅格顺序",
                        setter: {
                          componentName: "NumberSetter",
                          props: {
                            step: 1,
                          },
                        }
                      },
                      {
                        name: "pull",
                        title: "向左移动",
                        setter: {
                          componentName: "NumberSetter",
                          props: {
                            min: 0,
                            max: 24,
                            step: 1,
                          },
                        }
                      },
                      {
                        name: "push",
                        title: "向右移动",
                        setter: {
                          componentName: "NumberSetter",
                          props: {
                            min: 0,
                            max: 24,
                            step: 1,
                          },
                        }
                      }
                    ]
                  }
                }
              }
            ]
          }
        },
      },
      {
        name: "lg",
        title: "大屏幕",
        defaultValue: undefined,
        setter: {
          componentName: "MixedSetter",
          props: {
            setters: [
              {
                componentName: "NumberSetter",
                props: {
                  min: 0,
                  max: 24,
                  step: 1,
                },
              },
              {
                componentName: "ObjectSetter",
                props: {
                  config: {
                    items: [
                      {
                        name: "span",
                        title: "栅格格数",
                        setter: {
                          componentName: "NumberSetter",
                          props: {
                            min: 0,
                            max: 24,
                            step: 1,
                          },
                        }
                      },
                      {
                        name: "offset",
                        title: "左侧间隔",
                        setter: {
                          componentName: "NumberSetter",
                          props: {
                            min: 0,
                            max: 24,
                            step: 1,
                          },
                        }
                      },
                      {
                        name: "order",
                        title: "栅格顺序",
                        setter: {
                          componentName: "NumberSetter",
                          props: {
                            step: 1,
                          },
                        }
                      },
                      {
                        name: "pull",
                        title: "向左移动",
                        setter: {
                          componentName: "NumberSetter",
                          props: {
                            min: 0,
                            max: 24,
                            step: 1,
                          },
                        }
                      },
                      {
                        name: "push",
                        title: "向右移动",
                        setter: {
                          componentName: "NumberSetter",
                          props: {
                            min: 0,
                            max: 24,
                            step: 1,
                          },
                        }
                      }
                    ]
                  }
                }
              }
            ]
          }
        },
      },
      {
        name: "xl",
        title: "超大屏幕",
        defaultValue: undefined,
        setter: {
          componentName: "MixedSetter",
          props: {
            setters: [
              {
                componentName: "NumberSetter",
                props: {
                  min: 0,
                  max: 24,
                  step: 1,
                },
              },
              {
                componentName: "ObjectSetter",
                props: {
                  config: {
                    items: [
                      {
                        name: "span",
                        title: "栅格格数",
                        setter: {
                          componentName: "NumberSetter",
                          props: {
                            min: 0,
                            max: 24,
                            step: 1,
                          },
                        }
                      },
                      {
                        name: "offset",
                        title: "左侧间隔",
                        setter: {
                          componentName: "NumberSetter",
                          props: {
                            min: 0,
                            max: 24,
                            step: 1,
                          },
                        }
                      },
                      {
                        name: "order",
                        title: "栅格顺序",
                        setter: {
                          componentName: "NumberSetter",
                          props: {
                            step: 1,
                          },
                        }
                      },
                      {
                        name: "pull",
                        title: "向左移动",
                        setter: {
                          componentName: "NumberSetter",
                          props: {
                            min: 0,
                            max: 24,
                            step: 1,
                          },
                        }
                      },
                      {
                        name: "push",
                        title: "向右移动",
                        setter: {
                          componentName: "NumberSetter",
                          props: {
                            min: 0,
                            max: 24,
                            step: 1,
                          },
                        }
                      }
                    ]
                  }
                }
              }
            ]
          }
        },
      },
      {
        name: "xxl",
        title: "巨大屏幕",
        defaultValue: undefined,
        setter: {
          componentName: "MixedSetter",
          props: {
            setters: [
              {
                componentName: "NumberSetter",
                props: {
                  min: 0,
                  max: 24,
                  step: 1,
                },
              },
              {
                componentName: "ObjectSetter",
                props: {
                  config: {
                    items: [
                      {
                        name: "span",
                        title: "栅格格数",
                        setter: {
                          componentName: "NumberSetter",
                          props: {
                            min: 0,
                            max: 24,
                            step: 1,
                          },
                        }
                      },
                      {
                        name: "offset",
                        title: "左侧间隔",
                        setter: {
                          componentName: "NumberSetter",
                          props: {
                            min: 0,
                            max: 24,
                            step: 1,
                          },
                        }
                      },
                      {
                        name: "order",
                        title: "栅格顺序",
                        setter: {
                          componentName: "NumberSetter",
                          props: {
                            step: 1,
                          },
                        }
                      },
                      {
                        name: "pull",
                        title: "向左移动",
                        setter: {
                          componentName: "NumberSetter",
                          props: {
                            min: 0,
                            max: 24,
                            step: 1,
                          },
                        }
                      },
                      {
                        name: "push",
                        title: "向右移动",
                        setter: {
                          componentName: "NumberSetter",
                          props: {
                            min: 0,
                            max: 24,
                            step: 1,
                          },
                        }
                      }
                    ]
                  }
                }
              }
            ]
          }
        },
      },
      {
        name: "className",
        title: "样式类名",
        defaultValue: "",
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "style",
        title: "行内样式",
        defaultValue: {},
        setter: {
          componentName: "ObjectSetter",
          props: {},
        },
      },
    ],
  },
};

export { colSchema };

export default colSchema;
