const selectSchema = {
  version: "0.0.1",
  css: "",
  i18n: {},
  componentName: "Select",
  title: "选择器",
  description: "用于从一个选项列表中选择一个或多个值。",
  docUrl: "https://ant-design.antgroup.com/components/select-cn",
  screenshot: "",
  icon: "",
  keywords: "选择器，下拉选择，多选，单选",
  snippets: [],
  group: "common",
  category: "entry", // 数据录入组件
  priority: 0,
  sort: 0, // 排序
  children: "选项列表",
  props: [
    {
      name: "value",
      title: "当前值",
      defaultValue: null,
    },
    {
      name: "defaultValue",
      title: "默认值",
      defaultValue: null,
    },
    // {
    //   name: "onChange",
    //   title: "值变化时触发的回调函数",
    //   defaultValue: null,
    // },
    {
      name: "placeholder",
      title: "占位提示",
      defaultValue: "",
    },
    {
      name: "options",
      title: "选项列表",
      defaultValue: [],
    },
    {
      name: "mode",
      title: "选择模式",
      defaultValue: "default",
    },
    {
      name: "allowClear",
      title: "清除按钮",
      defaultValue: false,
    },
    {
      name: "showSearch",
      title: "支持搜索",
      defaultValue: false,
    },
    {
      name: "disabled",
      title: "禁用状态",
      defaultValue: false,
    },
    {
      name: "size",
      title: "显示大小",
      defaultValue: "default",
    },
    {
      name: "maxTagCount",
      title: "最多显示",
      defaultValue: "responsive",
    },
    {
      name: "dropdownStyle",
      title: "菜单样式",
      defaultValue: {},
    },
    {
      name: "dropdownMatchSelectWidth",
      title: "宽度一致",
      defaultValue: true,
    },
    {
      name: "notFoundContent",
      title: "无匹配项",
      defaultValue: "没有找到匹配项",
    },
    {
      name: "loading",
      title: "加载状态",
      defaultValue: false,
    },
    // {
    //   name: "onSearch",
    //   title: "搜索时触发的回调函数",
    //   defaultValue: null,
    // },
    {
      name: "filterOption",
      title: "输入筛选",
      defaultValue: true,
    },
    {
      name: "className",
      title: "自定义类",
      defaultValue: "",
    },
    {
      name: "style",
      title: "内联样式",
      defaultValue: {},
    },
  ],
  configure: {
    component: {
      isContainer: false, // Select 不是容器组件
      isModal: false,
      description: "选择器，提供丰富的配置项来满足各种场景的需求。",
      device: ["pc", "mobile"],
      nestingRule: {
        childWhitelist: ["Option"], // 选择器只接受 Option 子元素
        parentWhitelist: [],
      },
    },
    props: [
      {
        name: "value",
        title: "当前值",
        defaultValue: null,
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "defaultValue",
        title: "默认值",
        defaultValue: null,
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      // {
      //   name: "onChange",
      //   title: "值变化时触发的回调函数",
      //   defaultValue: null,
      //   setter: {
      //     componentName: "FunctionSetter",
      //     props: {},
      //   },
      // },
      {
        name: "placeholder",
        title: "占位提示",
        defaultValue: "",
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "options",
        title: "选项列表",
        defaultValue: [],
        setter: {
          componentName: "ArraySetter",
          props: {},
        },
      },
      {
        name: "mode",
        title: "选择模式",
        defaultValue: "default",
        setter: {
          componentName: "SelectSetter",
          props: {
            options: [
              { value: "default", label: "单选" },
              { value: "multiple", label: "多选" },
              { value: "tags", label: "标签" },
              { value: "combobox", label: "组合框" },
            ],
          },
        },
      },
      {
        name: "allowClear",
        title: "清除按钮",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "showSearch",
        title: "支持搜索",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "disabled",
        title: "禁用状态",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "size",
        title: "显示大小",
        defaultValue: "default",
        setter: {
          componentName: "SelectSetter",
          props: {
            options: [
              { value: "large", label: "大" },
              { value: "default", label: "中" },
              { value: "small", label: "小" },
            ],
          },
        },
      },
      {
        name: "maxTagCount",
        title: "最多标签",
        defaultValue: "responsive",
        setter: {
          componentName: "NumberSetter",
          props: {},
        },
      },
      {
        name: "dropdownStyle",
        title: "菜单样式",
        defaultValue: {},
        setter: {
          componentName: "ObjectSetter",
          props: {},
        },
      },
      {
        name: "dropdownMatchSelectWidth",
        title: "宽度一致",
        defaultValue: true,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "notFoundContent",
        title: "无匹配项",
        defaultValue: "没有找到匹配项",
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "loading",
        title: "加载状态",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      // {
      //   name: "onSearch",
      //   title: "搜索时触发的回调函数",
      //   defaultValue: null,
      //   setter: {
      //     componentName: "FunctionSetter",
      //     props: {},
      //   },
      // },
      {
        name: "filterOption",
        title: "输入筛选",
        defaultValue: true,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "className",
        title: "自定义类",
        defaultValue: "",
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "style",
        title: "内联样式",
        defaultValue: {},
        setter: {
          componentName: "ObjectSetter",
          props: {},
        },
      },
    ],
  },
};

export { selectSchema };

export default selectSchema;
