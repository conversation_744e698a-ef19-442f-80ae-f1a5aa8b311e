import { Select } from "antd";
import { SelectProps } from "antd/lib/select";
import React from "react";
import "./index.less";

/**
 * 选择器组件接口定义
 * 继承 SelectProps 并添加表单相关属性
 */
export interface WinSelectProps extends SelectProps {
  // 表单相关属性
  name?: string;
  label?: React.ReactNode;
  rules?: any[];
  [key: string]: any;
}

/**
 * WinSelect 组件
 * 
 * 对 Ant Design 的 Select 组件进行二次封装
 * 支持直接传入Form.Item属性，会被Form组件自动处理
 * 
 * @param {WinSelectProps} props - 组件属性
 * @returns {JSX.Element} - 返回封装后的选择器组件
 */
const WinSelect = (props: WinSelectProps) => {
  const { children, ...resetProps } = props;
  return <Select {...resetProps}>{children}</Select>;
};

export { WinSelect };

export default WinSelect;
