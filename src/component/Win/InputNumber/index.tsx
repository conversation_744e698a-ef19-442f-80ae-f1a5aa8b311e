import { InputNumber } from "antd";
import { InputNumberProps } from "antd/lib/input-number";
import React from "react";
import "./index.less";

/**
 * 数字输入框组件接口定义
 * 继承 InputNumberProps 并添加表单相关属性
 */
export interface WinInputNumberProps extends InputNumberProps {
  // 表单相关属性
  name?: string;
  label?: React.ReactNode;
  rules?: any[];
  [key: string]: any;
}

/**
 * WinInputNumber 组件
 * 
 * 对 Ant Design 的 InputNumber 组件进行二次封装
 * 支持直接传入Form.Item属性，会被Form组件自动处理
 * 
 * @param {WinInputNumberProps} props - 组件属性
 * @returns {JSX.Element} - 返回封装后的数字输入框组件
 */
const WinInputNumber = (props: WinInputNumberProps) => {
  const { children, ...resetProps } = props;
  return <InputNumber {...resetProps}>{children}</InputNumber>;
};

export { WinInputNumber };

export default WinInputNumber;
