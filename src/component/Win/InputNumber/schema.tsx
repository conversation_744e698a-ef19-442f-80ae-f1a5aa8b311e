const inputNumberSchema = {
  version: "0.0.1",
  css: "",
  i18n: {},
  componentName: "InputNumber",
  title: "数字输入",
  description: "用于输入数值，支持限制范围、步长等特性。",
  docUrl: "https://ant-design.antgroup.com/components/input-number-cn",
  screenshot: "",
  icon: "",
  keywords: "数字输入框，数值输入，步进器",
  snippets: [],
  group: "common",
  category: "entry", // 数据录入组件
  priority: 0,
  sort: 0, // 排序
  props: [
    {
      name: "placeholder",
      title: "占位提示",
      defaultValue: "",
    },
    {
      name: "value",
      title: "当前值",
      defaultValue: null,
    },
    {
      name: "defaultValue",
      title: "默认值",
      defaultValue: null,
    },
    {
      name: "min",
      title: "最小值",
      defaultValue: -Infinity,
    },
    {
      name: "max",
      title: "最大值",
      defaultValue: Infinity,
    },
    {
      name: "step",
      title: "步长",
      defaultValue: 1,
    },
    {
      name: "precision",
      title: "精度",
      defaultValue: null,
    },
    {
      name: "formatter",
      title: "格式化",
      defaultValue: null,
    },
    {
      name: "parser",
      title: "解析函数",
      defaultValue: null,
    },
    {
      name: "stringMode",
      title: "string 模式",
      defaultValue: false,
    },
    {
      name: "disabled",
      title: "禁用状态",
      defaultValue: false,
    },
    {
      name: "size",
      title: "显示大小",
      defaultValue: "default",
    },
    // {
    //   name: "onChange",
    //   title: "值变化时触发的回调函数",
    //   defaultValue: null,
    // },
    // {
    //   name: "onBlur",
    //   title: "失去焦点时触发的回调函数",
    //   defaultValue: null,
    // },
    // {
    //   name: "onFocus",
    //   title: "获得焦点时触发的回调函数",
    //   defaultValue: null,
    // },
    // {
    //   name: "onPressEnter",
    //   title: "按下 Enter 键时触发的回调函数",
    //   defaultValue: null,
    // },
    {
      name: "addonBefore",
      title: "前缀",
      defaultValue: null,
    },
    {
      name: "addonAfter",
      title: "后缀",
      defaultValue: null,
    },
    {
      name: "prefix",
      title: "图标前缀",
      defaultValue: null,
    },
    {
      name: "suffix",
      title: "图标后缀",
      defaultValue: null,
    },
    {
      name: "className",
      title: "自定义类",
      defaultValue: "",
    },
    {
      name: "style",
      title: "内联样式",
      defaultValue: {},
    },
    {
      name: "status",
      title: "状态",
      defaultValue: null,
    },
    {
      name: "variant",
      title: "边框样式",
      defaultValue: true,
    },
  ],
  configure: {
    component: {
      isContainer: false,
      isModal: false,
      description: "数字输入框，提供丰富的配置项来满足各种场景的需求。",
      device: ["pc", "mobile"],
      nestingRule: {
        childWhitelist: [], // 数字输入框不接受子元素
        parentWhitelist: [],
      },
    },
    props: [
      {
        name: "placeholder",
        title: "占位提示",
        defaultValue: "",
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "value",
        title: "当前值",
        defaultValue: null,
        setter: {
          componentName: "StringSetter",
          props: {},
        },
      },
      {
        name: "defaultValue",
        title: "默认值",
        defaultValue: null,
        setter: {
          componentName: "StringSetter",
          props: {},
        },
      },
      {
        name: "min",
        title: "最小值",
        defaultValue: -Infinity,
        setter: {
          componentName: "NumberSetter",
          props: {},
        },
      },
      {
        name: "max",
        title: "最大值",
        defaultValue: Infinity,
        setter: {
          componentName: "NumberSetter",
          props: {},
        },
      },
      {
        name: "step",
        title: "步长",
        defaultValue: 1,
        setter: {
          componentName: "NumberSetter",
          props: {},
        },
      },
      {
        name: "precision",
        title: "精度",
        defaultValue: null,
        setter: {
          componentName: "NumberSetter",
          props: {},
        },
      },
      {
        name: "formatter",
        title: "格式化",
        defaultValue: null,
        setter: {
          componentName: "FunctionSetter",
          props: {},
        },
      },
      {
        name: "parser",
        title: "解析函数",
        defaultValue: null,
        setter: {
          componentName: "FunctionSetter",
          props: {},
        },
      },
      {
        name: "stringMode",
        title: "string模式",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "disabled",
        title: "禁用状态",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "size",
        title: "显示大小",
        defaultValue: "default",
        setter: {
          componentName: "SelectSetter",
          props: {
            style: { width: "100%" },
            options: [
              { value: "large", label: "大" },
              { value: "default", label: "中" },
              { value: "small", label: "小" },
            ],
          },
        },
      },
      // {
      //   name: "onChange",
      //   title: "值变化时触发的回调函数",
      //   defaultValue: null,
      //   setter: {
      //     componentName: "FunctionSetter",
      //     props: {},
      //   },
      // },
      // {
      //   name: "onBlur",
      //   title: "失去焦点时触发的回调函数",
      //   defaultValue: null,
      //   setter: {
      //     componentName: "FunctionSetter",
      //     props: {},
      //   },
      // },
      // {
      //   name: "onFocus",
      //   title: "获得焦点时触发的回调函数",
      //   defaultValue: null,
      //   setter: {
      //     componentName: "FunctionSetter",
      //     props: {},
      //   },
      // },
      // {
      //   name: "onPressEnter",
      //   title: "按下 Enter 键时触发的回调函数",
      //   defaultValue: null,
      //   setter: {
      //     componentName: "FunctionSetter",
      //     props: {},
      //   },
      // },
      {
        name: "addonBefore",
        title: "前缀",
        defaultValue: null,
        setter: {
          componentName: "ReactNodeSetter",
          props: {},
        },
      },
      {
        name: "addonAfter",
        title: "后缀",
        defaultValue: null,
        setter: {
          componentName: "ReactNodeSetter",
          props: {},
        },
      },
      {
        name: "prefix",
        title: "图标前缀",
        defaultValue: null,
        setter: {
          componentName: "ReactNodeSetter",
          props: {},
        },
      },
      {
        name: "suffix",
        title: "图标后缀",
        defaultValue: null,
        setter: {
          componentName: "ReactNodeSetter",
          props: {},
        },
      },
      {
        name: "className",
        title: "自定义类",
        defaultValue: "",
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "style",
        title: "内联样式",
        defaultValue: {},
        setter: {
          componentName: "ObjectSetter",
          props: {},
        },
      },
      {
        name: "status",
        title: "状态",
        defaultValue: null,
        setter: {
          componentName: "SelectSetter",
          props: {
            style: { width: "100%" },
            options: [
              { value: "warning", label: "警告" },
              { value: "error", label: "错误" },
            ],
          },
        },
      },
      {
        name: "variant",
        title: "边框样式",
        defaultValue: true,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
    ],
  },
};

export { inputNumberSchema };

export default inputNumberSchema;
