import { useEffect, useRef } from "react";
import Plyr from "plyr";
import "plyr/dist/plyr.css"; // 引入 Plyr 样式
import "./index.less";

/* Plyr */
const WinPlyr = (props) => {
  const {
    source = "https://cdn.plyr.io/static/demo/View_From_A_Blue_Moon_Trailer-576p.mp4",
    type = "video/mp4",
  } = props;
  const playerRef = useRef(null);

  useEffect(() => {
    if (!playerRef.current) return;

    // 初始化 Plyr 播放器
    const player = new Plyr(playerRef.current, {
      controls: [
        "play-large", // The large play button in the center
        "restart", // Restart playback
        "rewind", // Rewind by the seek time (default -10)
        "play", // Play/pause playback
        "fast-forward", // Fast forward by the seek time (default 10)
        "progress", // Progress bar and scrubber for playback and buffering
        "current-time", // Current time of playback
        "duration", // Duration of playback
        "mute", // Toggle mute
        "volume", // Volume control
        "captions", // Captions/Toggle captions on/off (when available)
        "settings", // Settings menu (by default, includes speed)
        "pip", // Picture-in-picture (currently Safari only)
        "airplay", // Airplay (currently Safari only)
        "fullscreen", // Fullscreen mode
      ],
      poster:
        "https://cdn.plyr.io/static/demo/View_From_A_Blue_Moon_Trailer-HD.jpg",
      // 可以添加更多配置选项...
    });

    // 清理函数，在组件卸载时销毁 Plyr 实例
    return () => {
      if (player) {
        player.destroy();
      }
    };
  }, []);

  return (
    <div>
      <video ref={playerRef} playsInline>
        <source src={source} type={type} />
        Your browser does not support the video tag.
      </video>
    </div>
  );
};

export { WinPlyr };

export default WinPlyr;
