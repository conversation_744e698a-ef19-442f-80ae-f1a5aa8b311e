import { useState, useEffect, useRef } from "react";
import { Input } from "antd";
import "./index.less";

const WinScanInput = (props) => {
  const { onScan, inputProps = {}, children, ...restProps } = props;

  const [value, setValue] = useState("");
  const inputRef = useRef(null);
  const isComposing = useRef(false);

  // 自动聚焦输入框
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  const handleInputChange = (event) => {
    const newValue = event.target.value;
    setValue(newValue);

    // 检测是否为组合键输入（例如中文输入法），如果是，则不处理
    if (event.nativeEvent.isComposing) {
      isComposing.current = true;
      return;
    }

    isComposing.current = false;

    // 如果扫码枪发送回车键作为结束符，我们可以通过 keyDown 事件捕捉
    // 或者通过判断输入速度来猜测扫码完成
    // 这里假设扫码枪发送了回车键
    if (
      event.nativeEvent.inputType === "insertText" &&
      newValue.endsWith("\r")
    ) {
      event.preventDefault(); // 防止默认行为

      // 移除回车符并调用扫描完成的回调函数
      const barcode = newValue.slice(0, -1);
      onScan(barcode);

      // 清空输入框以备下次扫描
      setValue("");
    }
  };

  const handleKeyDown = (event) => {
    // 如果是回车键并且不是组合键输入，则认为扫描完成
    if (event.key === "Enter" && !isComposing.current) {
      event.preventDefault();

      onScan(value);

      // 清空输入框以备下次扫描
      setValue("");
    }
  };

  return (
    <Input
      ref={inputRef}
      {...inputProps}
      value={value}
      onChange={handleInputChange}
      onKeyDown={handleKeyDown}
      {...restProps}
    />
  );
};

export { WinScanInput };

export default WinScanInput;
