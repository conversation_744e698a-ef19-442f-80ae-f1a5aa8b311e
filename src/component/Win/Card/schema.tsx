const cardSchema = {
  version: "0.0.1",
  css: "",
  i18n: {},
  componentName: "Card",
  title: "卡片",
  description: "用于展示内容和功能操作的容器。",
  docUrl: "https://ant-design.antgroup.com/components/card-cn",
  screenshot: "",
  icon: "",
  keywords: "卡片，信息展示，布局",
  snippets: [],
  group: "common",
  category: "display", // 数据展示组件
  priority: 0,
  sort: 0, // 排序
  children: [],
  props: [
    {
      name: "title",
      title: "卡片标题",
      defaultValue: "默认标题",
    },
    {
      name: "extra",
      title: "操作区域",
      defaultValue: null,
    },
    {
      name: "loading",
      title: "载入状态",
      defaultValue: false,
    },
    {
      name: "variant",
      title: "显示边框",
      defaultValue: true,
    },
    {
      name: "hoverable",
      title: "悬停效果",
      defaultValue: false,
    },
    {
      name: "size",
      title: "卡片大小",
      defaultValue: "default",
    },
    {
      name: "type",
      title: "卡片类型",
      defaultValue: "inner",
    },
    {
      name: "cover",
      title: "封面",
      defaultValue: null,
    },
    {
      name: "actions",
      title: "底部操作",
      defaultValue: [],
    },
    {
      name: "className",
      title: "自定义类",
      defaultValue: "winyh",
    },
    {
      name: "style",
      title: "内联样式",
      defaultValue: {},
    },
  ],
  events: {
    onClick: {
      title: "点击事件",
      actions: [
        {
          actionType: "subscribe",
          args: {
            target: "",
            msg: "",
          },
        },
      ],
    },
  },
  configure: {
    component: {
      isContainer: true,
      isModal: false,
      description:
        "卡片，用于展示内容和功能操作的容器，提供丰富的配置项来满足各种场景的需求。",
      device: ["pc", "mobile"],
      nestingRule: {
        childWhitelist: [], // 卡片可以接受任意子元素
        parentWhitelist: [],
      },
    },
    props: [
      {
        name: "title",
        title: "卡片标题",
        defaultValue: null,
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "extra",
        title: "操作区域",
        defaultValue: null,
        setter: {
          componentName: "ReactNodeSetter",
          props: {},
        },
      },
      {
        name: "loading",
        title: "载入状态",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "variant",
        title: "显示边框",
        defaultValue: true,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "hoverable",
        title: "悬停状态",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "size",
        title: "卡片大小",
        defaultValue: "default",
        setter: {
          componentName: "SelectSetter",
          props: {
            options: [
              { value: "default", label: "默认" },
              { value: "small", label: "小" },
            ],
          },
        },
      },
      {
        name: "type",
        title: "卡片类型",
        defaultValue: "inner",
        setter: {
          componentName: "SelectSetter",
          props: {
            options: [
              { value: "inner", label: "内部型" },
              { value: "outer", label: "外部型" },
            ],
          },
        },
      },
      {
        name: "cover",
        title: "卡片封面",
        defaultValue: null,
        setter: {
          componentName: "ReactNodeSetter",
          props: {},
        },
      },
      {
        name: "actions",
        title: "底部操作",
        defaultValue: [],
        setter: {
          componentName: "ArraySetter",
          props: {},
        },
      },
      {
        name: "className",
        title: "自定义类",
        defaultValue: "",
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "style",
        title: "内联样式",
        defaultValue: {},
        setter: {
          componentName: "ObjectSetter",
          props: {},
        },
      },
    ],
  },
};

export { cardSchema };

export default cardSchema;
