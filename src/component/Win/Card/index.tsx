import { Card } from "antd";
import "./index.less";

/* 卡片 */
const WinCard = (props) => {
  const {
    align,
    gutter = [12, 12],
    justify,
    wrap,
    children,
    ...restProps
  } = props;

  return (
    <Card
      align={align}
      gutter={gutter}
      justify={justify}
      wrap={wrap}
      {...restProps}
    >
      {children}
    </Card>
  );
};

export { WinCard };

export default WinCard;
