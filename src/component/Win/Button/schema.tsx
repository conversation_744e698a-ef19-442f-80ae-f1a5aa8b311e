const schema = {
  version: "0.0.1",
  css: "",
  i18n: {},
  componentName: "Button",
  title: "按钮",
  description: "响应用户点击行为，触发相应的业务逻辑",
  docUrl: "",
  screenshot: "",
  icon: "",
  keywords: "按钮，操作",
  snippets: [],
  group: "common",
  category: "basic", // 基础组件
  priority: 0,
  sort: 0, // 排序
  children: "按钮文本",
  props: [
    {
      name: "color",
      title: "按钮颜色",
      defaultValue: "",
    },
    // {
    //   name: "href",
    //   title: "跳转地址",
    //   defaultValue: "",
    // },
    // {
    //   name: "target",
    //   title: "跳转目标",
    //   defaultValue: "_self",
    // },
    {
      name: "htmlType",
      title: "原生属性",
      defaultValue: "button",
    },
    {
      name: "size",
      title: "按钮大小",
      defaultValue: "middle",
    },
    {
      name: "shape",
      title: "按钮形状",
      defaultValue: "default",
    },
    {
      name: "type",
      title: "按钮类型",
      defaultValue: "default",
    },
    {
      name: "variant",
      title: "按钮变体",
      defaultValue: "default",
    },
    {
      name: "classNames",
      title: "语义类名",
      defaultValue: "",
    },
    {
      name: "styles",
      title: "语义样式",
      defaultValue: "",
    },
    {
      name: "icon",
      title: "按钮图标",
      defaultValue: "",
    },
    {
      name: "iconPosition",
      title: "图标位置",
      defaultValue: "start",
    },
    {
      name: "autoInsertSpace",
      title: "文字间距",
      defaultValue: true,
    },
    {
      name: "block",
      title: "块级元素",
      defaultValue: false,
    },
    {
      name: "danger",
      title: "危险按钮",
      defaultValue: false,
    },
    {
      name: "disabled",
      title: "禁用按钮",
      defaultValue: false,
    },
    {
      name: "ghost",
      title: "背景透明",
      defaultValue: false,
    },
    {
      name: "loading",
      title: "载入状态",
      defaultValue: false,
    },
  ],
  events: {
    onClick: {
      title: "点击事件",
      isCustom: false,
      actions: [
        // {
        //   actionType: "message",
        //   nodeType: "notice",
        //   actionId: "xxx001",
        //   args: {
        //     msgType: "success",
        //     msg: "我是全局警告消息，可以配置不同类型和弹出位置~",
        //     position: "top",
        //   },
        // },
        // {
        //   actionType: "message",
        //   nodeType: "notice",
        //   actionId: "xxx002",
        //   args: {
        //     msgType: "info",
        //     msg: "我是全局警告消息，可以配置不同类型和弹出位置~",
        //     position: "top-right",
        //   },
        // },
        // {
        //   actionType: "publish",
        //   nodeType: "notice",
        //   actionId: "xxx003",
        //   args: {
        //     target: "cvp79wf0c8m6x6a3occp5",
        //     msg: "组件A点击了",
        //   },
        // },
      ],
    },
    onMouseEnter: {
      title: "鼠标移入",
      isCustom: true,
      actions: [
        // {
        //   actionType: "message",
        //   nodeType: "notice",
        //   args: {
        //     msgType: "success",
        //     msg: "我是全局警告消息，可以配置不同类型和弹出位置~",
        //     position: "top",
        //   },
        // },
        // {
        //   actionType: "message",
        //   nodeType: "notice",
        //   args: {
        //     msgType: "info",
        //     msg: "我是全局警告消息，可以配置不同类型和弹出位置~",
        //     position: "top-right",
        //   },
        // },
      ],
    },
  },
  configure: {
    component: {
      isContainer: false,
      isModal: false,
      description: "",
      device: ["pc", "mobile"],
      nestingRule: {
        childWhitelist: ["Col"],
        parentWhitelist: [],
      },
    },
    props: [
      {
        name: "color",
        title: "按钮颜色",
        defaultValue: "",
        condition: {
          type: "JSFunction",
          value: "() => false",
        },
        setter: {
          componentName: "SelectSetter",
          props: {
            style: { width: "100%" },
            allowClear: true,
            options: [
              { value: "default", label: "默认" },
              { value: "primary", label: "主题" },
              { value: "danger", label: "警告" },
            ],
          },
        },
      },
      {
        name: "href",
        title: "跳转地址",
        defaultValue: "",
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "target",
        title: "跳转目标",
        defaultValue: "_self",
        setter: {
          componentName: "SelectSetter",
          props: {
            style: { width: "100%" },
            options: [
              { value: "_self", label: "当前框架" },
              { value: "_blank", label: "新标签页" },
              { value: "_parent", label: "父框架" },
              { value: "_top", label: "顶层框架" },
            ],
          },
        },
      },
      {
        name: "htmlType",
        title: "原生属性",
        defaultValue: "button",
        condition: {
          type: "JSFunction",
          value: "() => false",
        },
        setter: {
          componentName: "SelectSetter",
          props: {
            style: { width: "100%" },
            allowClear: true,
            options: [
              { value: "button", label: "按钮" },
              { value: "submit", label: "提交" },
              { value: "reset", label: "重置" },
            ],
          },
        },
      },
      {
        name: "size",
        title: "按钮大小",
        defaultValue: "middle",
        condition: {
          type: "JSFunction",
          value: "() => false",
        },
        setter: {
          componentName: "SelectSetter",
          props: {
            style: { width: "100%" },
            allowClear: true,
            options: [
              { value: "large", label: "大" },
              { value: "middle", label: "中" },
              { value: "small", label: "小" },
            ],
          },
        },
      },
      {
        name: "shape",
        title: "按钮形状",
        defaultValue: "default",
        condition: {
          type: "JSFunction",
          value: "() => false",
        },
        setter: {
          componentName: "SelectSetter",
          props: {
            allowClear: true,
            options: [
              { value: "default", label: "默认" },
              { value: "round", label: "圆角" },
              { value: "circle", label: "圆形" },
            ],
          },
        },
      },
      {
        name: "type",
        title: "按钮类型",
        defaultValue: "default",
        condition: {
          type: "JSFunction",
          value: "() => false",
        },
        setter: {
          componentName: "SelectSetter",
          props: {
            allowClear: true,
            options: [
              { value: "default", label: "次按钮" },
              { value: "dashed", label: "虚线按钮" },
              { value: "link", label: "链接按钮" },
              { value: "text", label: "文本按钮" },
              { value: "primary", label: "主按钮" },
            ],
          },
        },
      },
      {
        name: "variant",
        title: "按钮变体",
        defaultValue: "default",
        condition: {
          type: "JSFunction",
          value: "() => false",
        },
        setter: {
          componentName: "SelectSetter",
          props: {
            allowClear: true,
            options: [
              { value: "outlined", label: "描边按钮" },
              { value: "dashed", label: "虚线按钮" },
              { value: "solid", label: "实线按钮" },
              { value: "filled", label: "实心按钮" },
              { value: "text", label: "文本按钮" },
              { value: "link", label: "链接按钮" },
            ],
          },
        },
      },
      {
        name: "classNames",
        title: "语义类名",
        defaultValue: "",
        condition: {
          type: "JSFunction",
          value: "() => false",
        },
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "styles",
        title: "语义样式",
        defaultValue: "",
        condition: {
          type: "JSFunction",
          value: "() => false",
        },
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "icon",
        title: "按钮图标",
        defaultValue: "",
        condition: {
          type: "JSFunction",
          value: "() => false",
        },
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "iconPosition",
        title: "图标位置",
        defaultValue: "start",
        condition: {
          type: "JSFunction",
          value: "() => false",
        },
        setter: {
          componentName: "SelectSetter",
          props: {
            allowClear: true,
            options: [
              { value: "start", label: "左边" },
              { value: "end", label: "右边" },
            ],
          },
        },
      },
      {
        name: "autoInsertSpace",
        title: "文字间距",
        defaultValue: true,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "block",
        title: "块级元素",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "danger",
        title: "危险按钮",
        defaultValue: false,
        condition: {
          type: "JSFunction",
          value: "() => false",
        },
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "disabled",
        title: "禁用按钮",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "ghost",
        title: "背景透明",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "loading",
        title: "载入状态",
        defaultValue: false,
        condition: {
          type: "JSFunction",
          value: "() => false",
        },
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
    ],
  },
};

export { schema };

export default schema;
