import { genAlphabetFieldId, genUniqueId } from "@/utils";

const tabsSchema = {
  version: "0.0.1",
  css: "",
  i18n: {},
  componentName: "Tabs",
  title: "标签页",
  description: "用于在不同视图之间进行切换。",
  docUrl: "https://ant-design.antgroup.com/components/tabs-cn",
  screenshot: "",
  icon: "",
  keywords: "标签页，分页，内容切换",
  snippets: [],
  group: "common",
  category: "navigation", // 导航组件
  priority: 0,
  sort: 0, // 排序
  children: [
    {
      uuid: genUniqueId(),
      name: genAlphabetFieldId(),
      field: genAlphabetFieldId(),
      title: "页签子模块",
      componentName: "TabPane",
      css: "",
      version: "0.0.1",
      i18n: {},
      events: {},
      methods: {},
      props: {
        label: "页签",
        className: "winyh",
        style: {},
      },
      children: [],
    },
    {
      uuid: genUniqueId(),
      name: gen<PERSON>l<PERSON>betFieldId(),
      field: genAlphabetFieldId(),
      title: "页签子模块",
      componentName: "TabPane",
      css: "",
      version: "0.0.1",
      i18n: {},
      events: {},
      methods: {},
      props: {
        label: "页签",
        key: genAlphabetFieldId(10),
        className: "winyh",
        style: {},
      },
      children: [],
    },
  ],
  props: [
    {
      name: "activeKey",
      title: "当前激活",
      defaultValue: null,
    },
    {
      name: "defaultActiveKey",
      title: "默认激活",
      defaultValue: null,
    },
    {
      name: "onChange",
      title: "切换回调",
      defaultValue: null,
    },
    {
      name: "type",
      title: "类型",
      defaultValue: "line",
    },
    {
      name: "tabPosition",
      title: "标签位置",
      defaultValue: "top",
    },
    {
      name: "size",
      title: "尺寸",
      defaultValue: "default",
    },
    {
      name: "animated",
      title: "开启动画",
      defaultValue: true,
    },
    {
      name: "hideAdd",
      title: "添加按钮",
      defaultValue: false,
    },
    {
      name: "onEdit",
      title: "编辑回调",
      defaultValue: null,
    },
    {
      name: "className",
      title: "自定义类名",
      defaultValue: "",
    },
    {
      name: "style",
      title: "自定义内联样式",
      defaultValue: {},
    },
  ],
  configure: {
    component: {
      isContainer: true,
      isModal: false,
      description: "标签页，提供丰富的配置项来满足各种场景的需求。",
      device: ["pc", "mobile"],
      nestingRule: {
        childWhitelist: ["TabPane"], // 标签页只接受 TabPane 子元素
        parentWhitelist: [],
      },
    },
    props: [
      {
        name: "items",
        title: "选项内容",
        defaultValue: [],
        setter: {
          componentName: "ArraySchemaSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "activeKey",
        title: "当前激活",
        defaultValue: null,
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "defaultActiveKey",
        title: "默认激活",
        defaultValue: null,
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "onChange",
        title: "切换回调",
        defaultValue: null,
        setter: {
          componentName: "FunctionSetter",
          props: {},
        },
      },
      {
        name: "type",
        title: "类型",
        defaultValue: "line",
        setter: {
          componentName: "SelectSetter",
          props: {
            options: [
              { value: "line", label: "线条型" },
              { value: "card", label: "卡片型" },
              { value: "editable-card", label: "可编辑卡片型" },
            ],
          },
        },
      },
      {
        name: "tabPosition",
        title: "标签位置",
        defaultValue: "top",
        setter: {
          componentName: "SelectSetter",
          props: {
            options: [
              { value: "top", label: "顶部" },
              { value: "right", label: "右侧" },
              { value: "bottom", label: "底部" },
              { value: "left", label: "左侧" },
            ],
          },
        },
      },
      {
        name: "size",
        title: "尺寸",
        defaultValue: "default",
        setter: {
          componentName: "SelectSetter",
          props: {
            options: [
              { value: "large", label: "大" },
              { value: "default", label: "中" },
              { value: "small", label: "小" },
            ],
          },
        },
      },
      {
        name: "animated",
        title: "开启动画",
        defaultValue: true,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "hideAdd",
        title: "添加按钮",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "onEdit",
        title: "编辑回调",
        defaultValue: null,
        setter: {
          componentName: "FunctionSetter",
          props: {},
        },
      },
      {
        name: "className",
        title: "自定义类名",
        defaultValue: "",
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "style",
        title: "自定义内联样式",
        defaultValue: {},
        setter: {
          componentName: "ObjectSetter",
          props: {},
        },
      },
    ],
  },
};

export { tabsSchema };

export default tabsSchema;
