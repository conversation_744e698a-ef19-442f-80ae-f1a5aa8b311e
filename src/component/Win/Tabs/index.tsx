import { Tabs } from "antd";
import "./index.less";

import { SchemaRender } from "@/core/render";
import { getObjectById } from "@/utils";
import useStore from "@/store";

/* 标签页 */
const WinTabs = (props) => {
  const { children, items, ...restProps } = props;
  const { jsonSchema, params } = useStore();
  const schema = getObjectById(jsonSchema, restProps.id);
  const list = Array.isArray(schema?.children) ? schema?.children : [];

  let innerItems = list?.map((child) => {
    return {
      key: child?.name,
      label: child?.props?.label,
      children: <SchemaRender tense={params?.mode} schema={[child]} />,
    };
  });

  if (!restProps.activeKey) {
    delete restProps.activeKey;
  }

  if (!restProps.defaultActiveKey) {
    delete restProps.defaultActiveKey;
  }

  return <Tabs items={innerItems} {...restProps}></Tabs>;
};

export { WinTabs };

export default WinTabs;
