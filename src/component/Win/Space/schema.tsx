const spaceSchema = {
  version: "0.0.1",
  css: "",
  i18n: {},
  componentName: "Space",
  title: "间距",
  description: "用于控制子元素之间的间距，支持水平和垂直排列。",
  docUrl: "https://ant.design/components/space-cn",
  screenshot: "",
  icon: "",
  keywords: "间距, 布局, 水平间距, 垂直间距, 排列",
  snippets: [],
  group: "common",
  category: "layout", // 布局组件
  priority: 0,
  sort: 0, // 排序
  children: [], // Space 可以包含任何数量的子元素
  props: [
    {
      name: "size",
      title: "间距大小",
      description: "间距大小，支持预设值或数字",
      defaultValue: "small",
    },
    {
      name: "direction",
      title: "排列方向",
      defaultValue: "horizontal",
    },
    {
      name: "align",
      title: "对齐方式",
      defaultValue: "center",
    },
    {
      name: "wrap",
      title: "换行",
      defaultValue: false,
    },
    {
      name: "split",
      title: "分隔符",
      description: "设置拆分符",
      defaultValue: "",
    },
    {
      name: "className",
      title: "自定义类",
      defaultValue: "",
    },
    {
      name: "style",
      title: "内联样式",
      defaultValue: {},
    },
  ],
  methods: {},
  configure: {
    component: {
      isContainer: true,
      isModal: false,
      description:
        "间距组件，用于快速设置子元素间的间隔，简化了布局管理，使界面更加整洁。",
      device: ["pc", "mobile"],
      nestingRule: {
        childWhitelist: [], // Space 可以接受任意子元素
        parentWhitelist: [], // 可以放置在任意容器中
      },
    },
    props: [
      {
        name: "size",
        title: "间距大小",
        defaultValue: "small",
        setter: {
          componentName: "MixedSetter",
          props: {
            setters: [
              {
                componentName: "SelectSetter",
                props: {
                  options: [
                    { value: "small", label: "小" },
                    { value: "middle", label: "中" },
                    { value: "large", label: "大" },
                  ],
                },
              },
              {
                componentName: "NumberSetter",
                props: {
                  min: 0,
                },
              },
            ],
          },
        },
      },
      {
        name: "direction",
        title: "排列方向",
        defaultValue: "horizontal",
        setter: {
          componentName: "SelectSetter",
          props: {
            options: [
              { value: "horizontal", label: "水平" },
              { value: "vertical", label: "垂直" },
            ],
          },
        },
      },
      {
        name: "align",
        title: "对齐方式",
        defaultValue: "center",
        setter: {
          componentName: "SelectSetter",
          props: {
            options: [
              { value: "start", label: "顶部/左端对齐" },
              { value: "end", label: "底部/右端对齐" },
              { value: "center", label: "居中对齐" },
              { value: "baseline", label: "基线对齐" },
            ],
          },
        },
      },
      {
        name: "wrap",
        title: "换行",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "split",
        title: "分隔符",
        defaultValue: "",
        setter: {
          componentName: "StringSetter",
          props: {},
        },
      },
      {
        name: "className",
        title: "自定义类",
        defaultValue: "",
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "style",
        title: "内联样式",
        defaultValue: {},
        setter: {
          componentName: "ObjectSetter",
          props: {},
        },
      },
    ],
  },
};

export { spaceSchema };

export default spaceSchema;
