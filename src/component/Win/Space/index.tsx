import { Space } from "antd";
import "./index.less";

const WinSpace = (props: any) => {
  const {
    style,
    className,
    size,
    direction,
    align,
    wrap,
    split,
    children,
  } = props;
  
  return (
    <Space
      style={style}
      className={className}
      size={size}
      direction={direction}
      align={align}
      wrap={wrap}
      split={split}
    >
      {children}
    </Space>
  );
};

export { WinSpace };

export default WinSpace;
