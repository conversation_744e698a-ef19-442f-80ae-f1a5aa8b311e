const switchSchema = {
  version: "0.0.1",
  css: "",
  i18n: {},
  componentName: "Switch",
  title: "开关",
  description: "开关选择器，表示两种相互对立的状态间的切换，多用于触发「开/关」。",
  docUrl: "https://ant.design/components/switch-cn",
  screenshot: "",
  icon: "",
  keywords: "开关，Switch，切换，toggle",
  snippets: [],
  group: "common",
  category: "entry",
  priority: 0,
  sort: 10,
  npm: {
    package: "antd",
    exportName: "Switch",
    subName: "",
    destructuring: "",
    main: "",
    version: "",
  },
  children: [],
  props: [
    {
      name: "checked",
      title: "选中状态",
      description: "指定当前是否选中",
      defaultValue: false,
    },
    {
      name: "defaultChecked",
      title: "默认选中",
      description: "初始是否选中",
      defaultValue: false,
    },
    {
      name: "disabled",
      title: "禁用状态",
      description: "是否禁用",
      defaultValue: false,
    },
    {
      name: "loading",
      title: "加载状态",
      description: "加载中的开关",
      defaultValue: false,
    },
    {
      name: "size",
      title: "尺寸",
      description: "开关大小",
      defaultValue: "default",
    },
    {
      name: "checkedChildren",
      title: "选中时内容",
      description: "选中时的内容",
      defaultValue: "",
    },
    {
      name: "unCheckedChildren",
      title: "非选中时内容",
      description: "非选中时的内容",
      defaultValue: "",
    },
    {
      name: "className",
      title: "样式类名",
      description: "样式类名",
      defaultValue: "",
    },
    {
      name: "style",
      title: "行内样式",
      description: "行内样式",
      defaultValue: {},
    },
  ],
  events: [
    {
      name: "onChange",
      title: "变化时回调",
      description: "变化时的回调函数",
    },
  ],
  configure: {
    component: {
      isContainer: false,
      isModal: false,
      description: "开关选择器，表示两种相互对立的状态间的切换，多用于触发「开/关」。",
      device: ["pc", "mobile"],
      nestingRule: {
        childWhitelist: [],
        parentWhitelist: [],
      },
    },
    props: [
      {
        name: "checked",
        title: "选中状态",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "defaultChecked",
        title: "默认选中",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "disabled",
        title: "禁用状态",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "loading",
        title: "加载状态",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "size",
        title: "尺寸",
        defaultValue: "default",
        setter: {
          componentName: "SelectSetter",
          props: {
            options: [
              { value: "default", label: "默认" },
              { value: "small", label: "小" },
            ],
          },
        },
      },
      {
        name: "checkedChildren",
        title: "选中时内容",
        defaultValue: "",
        setter: {
          componentName: "StringSetter",
          props: {},
        },
      },
      {
        name: "unCheckedChildren",
        title: "非选中时内容",
        defaultValue: "",
        setter: {
          componentName: "StringSetter",
          props: {},
        },
      },
      {
        name: "className",
        title: "样式类名",
        defaultValue: "",
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "style",
        title: "行内样式",
        defaultValue: {},
        setter: {
          componentName: "ObjectSetter",
          props: {},
        },
      },
    ],
    supports: {
      events: ["onChange"],
      loop: true,
      condition: true,
      style: true,
      className: true,
    },
    advanced: {},
  },
};

export { switchSchema };
export default switchSchema;