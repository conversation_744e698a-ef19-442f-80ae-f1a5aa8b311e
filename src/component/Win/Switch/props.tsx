const meta = {
  componentName: "Switch",
  props: {
    checked: false,
    defaultChecked: false,
    disabled: false,
    loading: false,
    size: "default",
    checkedChildren: "",
    unCheckedChildren: "",
    className: [""],
    style: {},
  },
  css: "",
  children: [],
  events: {
    onChange: {
      type: "JSFunction",
      value: "function(checked, event) { console.log('switch change', checked) }",
    },
  },
  methods: {},
  dataSource: {
    list: [],
    dataHandler: {
      type: "JSFunction",
      value: "function(dataMap) { }",
    },
  },
};

export default meta;