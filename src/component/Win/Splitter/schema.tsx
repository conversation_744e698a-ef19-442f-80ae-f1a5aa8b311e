const splitterSchema = {
  version: "0.0.1",
  css: "",
  i18n: {},
  componentName: "Splitter",
  title: "分割面板",
  description: "用于创建可调整大小的分割面板，支持水平和垂直方向。",
  docUrl: "", // 如果有官方文档链接，请提供
  screenshot: "",
  icon: "",
  keywords: "分割, 布局, 分割面板, 可调整大小",
  snippets: [],
  group: "common",
  category: "layout", // 布局组件
  priority: 0,
  sort: 0, // 排序
  children: [
    { componentName: "Panel", title: "左/上面板" },
    { componentName: "Panel", title: "右/下面板" },
  ],
  props: [
    {
      name: "direction",
      title: "分割方向",
      defaultValue: "horizontal",
    },
    {
      name: "initialSize",
      title: "初始尺寸",
      defaultValue: 50,
    },
    {
      name: "minSize",
      title: "最小尺寸",
      defaultValue: 20,
    },
    {
      name: "maxSize",
      title: "最大尺寸",
      defaultValue: 80,
    },
    {
      name: "onDrag",
      title: "拖动回调",
      defaultValue: null,
    },
    {
      name: "className",
      title: "自定义类",
      defaultValue: "",
    },
    {
      name: "style",
      title: "内联样式",
      defaultValue: {},
    },
  ],
  methods: {},
  configure: {
    component: {
      isContainer: true,
      isModal: false,
      description:
        "分割面板组件，允许用户通过拖拽来调整两个相邻区域的大小，适用于复杂的布局场景。",
      device: ["pc"], // 分割面板更适合桌面端
      nestingRule: {
        childWhitelist: ["Panel"], // 只能包含 Panel 类型的子元素
        parentWhitelist: [], // 可以放置在任意容器中
      },
    },
    props: [
      {
        name: "direction",
        title: "分割方向",
        defaultValue: "horizontal",
        setter: {
          componentName: "SelectSetter",
          props: {
            options: [
              { value: "horizontal", label: "水平" },
              { value: "vertical", label: "垂直" },
            ],
          },
        },
      },
      {
        name: "initialSize",
        title: "初始尺寸",
        defaultValue: 50,
        setter: {
          componentName: "NumberSetter",
          props: {
            min: 0,
            max: 100,
          },
        },
      },
      {
        name: "minSize",
        title: "最小尺寸",
        defaultValue: 20,
        setter: {
          componentName: "NumberSetter",
          props: {
            min: 0,
            max: 100,
          },
        },
      },
      {
        name: "maxSize",
        title: "最大尺寸",
        defaultValue: 80,
        setter: {
          componentName: "NumberSetter",
          props: {
            min: 0,
            max: 100,
          },
        },
      },
      {
        name: "onDrag",
        title: "拖动回调",
        defaultValue: null,
        setter: {
          componentName: "FunctionSetter",
          props: {},
        },
      },
      {
        name: "className",
        title: "自定义类",
        defaultValue: "",
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "style",
        title: "内联样式",
        defaultValue: {},
        setter: {
          componentName: "ObjectSetter",
          props: {},
        },
      },
    ],
  },
};

export { splitterSchema };

export default splitterSchema;
