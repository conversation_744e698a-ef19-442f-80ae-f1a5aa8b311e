import { useEffect, useRef } from "react";
import SignaturePad from "signature_pad";
import "./index.less";

/* 签名 */
const WinSignature = (props) => {
  const { children, ...restProps } = props;
  const canvasRef = useRef(null);
  const signaturePadRef = useRef(null);

  useEffect(() => {
    if (canvasRef.current) {
      signaturePadRef.current = new SignaturePad(canvasRef.current);
    }
  }, []);

  const clearSignature = () => {
    if (signaturePadRef.current) {
      signaturePadRef.current.clear();
    }
  };

  const getSignatureData = () => {
    if (signaturePadRef.current) {
      console.log(signaturePadRef.current.toDataURL("image/png"));
      return signaturePadRef.current.toDataURL("image/png"); // Get the signature as a data URL
    }
    return null;
  };

  return (
    <div {...restProps}>
      <canvas
        ref={canvasRef}
        width={400}
        height={200}
        style={{ border: "1px solid black" }}
      />
      <button onClick={clearSignature}>Clear</button>
      <button onClick={getSignatureData}>Get Signature Data</button>
    </div>
  );
};

export { WinSignature };
export default WinSignature;
