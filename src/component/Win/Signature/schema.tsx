const signatureSchema = {
  version: "0.0.1",
  css: "",
  i18n: {},
  componentName: "Signature",
  title: "签名",
  description: "用于捕捉用户签名的组件。",
  docUrl: "https://github.com/szimek/signature_pad",
  screenshot: "",
  icon: "",
  keywords: "签名，手写，捕捉",
  snippets: [],
  group: "common",
  category: "entry", // 数据输入组件
  priority: 0,
  sort: 0, // 排序
  children: [],
  props: [
    {
      name: "width",
      title: "画布宽度",
      defaultValue: 500,
    },
    {
      name: "height",
      title: "画布高度",
      defaultValue: 300,
    },
    {
      name: "className",
      title: "自定义类",
      defaultValue: "",
    },
    {
      name: "style",
      title: "内联样式",
      defaultValue: {},
    },
  ],
  methods: {
    clear: {
      title: "清除签名",
      description: "清除当前签名。",
    },
    getSignatureData: {
      title: "获取签名数据",
      description: "返回签名的图像数据URL。",
    },
  },
  configure: {
    component: {
      isContainer: false,
      isModal: false,
      description: "签名组件，用于捕捉用户签名。",
      device: ["pc", "mobile"],
      nestingRule: {
        childWhitelist: [], // 签名组件不接受子元素
        parentWhitelist: [],
      },
    },
    props: [
      {
        name: "width",
        title: "画布宽度",
        defaultValue: 500,
        setter: {
          componentName: "NumberSetter",
          props: {},
        },
      },
      {
        name: "height",
        title: "画布高度",
        defaultValue: 300,
        setter: {
          componentName: "NumberSetter",
          props: {},
        },
      },
      {
        name: "className",
        title: "自定义类",
        defaultValue: "",
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "style",
        title: "内联样式",
        defaultValue: {},
        setter: {
          componentName: "ObjectSetter",
          props: {},
        },
      },
    ],
  },
};

export { signatureSchema };
export default signatureSchema;
