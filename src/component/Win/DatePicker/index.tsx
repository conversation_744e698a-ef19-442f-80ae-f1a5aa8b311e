import { DatePicker } from "antd";
import { DatePickerProps } from "antd/lib/date-picker";
import React from "react";
import "./index.less";

/**
 * 日期选择器组件接口定义
 * 继承 DatePickerProps 并添加表单相关属性
 */
export interface WinDatePickerProps extends DatePickerProps {
  // 表单相关属性
  name?: string;
  label?: React.ReactNode;
  rules?: any[];
  [key: string]: any;
}

/**
 * WinDatePicker 组件
 * 
 * 对 Ant Design 的 DatePicker 组件进行二次封装
 * 支持直接传入Form.Item属性，会被Form组件自动处理
 * 
 * @param {WinDatePickerProps} props - 组件属性
 * @returns {JSX.Element} - 返回封装后的日期选择器组件
 */
const WinDatePicker = (props: WinDatePickerProps) => {
  const { children, ...resetProps } = props;
  return <DatePicker {...resetProps}>{children}</DatePicker>;
};

export { WinDatePicker };

export default WinDatePicker;
