const datePickerSchema = {
  version: "0.0.1",
  css: "",
  i18n: {},
  componentName: "DatePicker",
  title: "日期选择",
  description: "用于选择或输入日期，支持周、月、年、日期时间等不同类型的选项。",
  docUrl: "https://ant-design.antgroup.com/components/date-picker-cn",
  screenshot: "",
  icon: "",
  keywords: "日期选择器，时间选择器，日期时间选择器",
  snippets: [],
  group: "common",
  category: "basic", // 基础组件
  priority: 0,
  sort: 0, // 排序
  children: "", // 日期选择器通常没有子元素
  props: [
    {
      name: "allowClear",
      title: "清除按钮",
      defaultValue: true,
    },
    {
      name: "disabled",
      title: "禁用状态",
      defaultValue: false,
    },
    {
      name: "disabledDate",
      title: "禁选日期",
      defaultValue: null,
    },
    {
      name: "format",
      title: "日期格式",
      defaultValue: "YYYY-MM-DD",
    },
    {
      name: "getPopupContainer",
      title: "父节点",
      defaultValue: null,
    },
    {
      name: "placeholder",
      title: "占位提示",
      defaultValue: "",
    },
    {
      name: "size",
      title: "显示大小",
      defaultValue: "default",
    },
    {
      name: "value",
      title: "当前值",
      defaultValue: null,
    },
    {
      name: "defaultValue",
      title: "默认值",
      defaultValue: null,
    },
    {
      name: "picker",
      title: "日期类型",
      defaultValue: "date",
    },
    {
      name: "showTime",
      title: "时间选择",
      defaultValue: false,
    },
    {
      name: "open",
      title: "弹出面板",
      defaultValue: null,
    },
    {
      name: "panelRender",
      title: "浮层内容",
      defaultValue: null,
    },
    {
      name: "renderExtraFooter",
      title: "额外元素",
      defaultValue: null,
    },
    {
      name: "dropdownClassName",
      title: "下拉菜单",
      defaultValue: "",
    },
    {
      name: "inputReadOnly",
      title: "设置 input 只读，防止移动设备键盘弹出",
      defaultValue: false,
    },
    {
      name: "variant",
      title: "是否展示边框样式",
      defaultValue: true,
    },
    {
      name: "showToday",
      title: "是否显示今日按钮",
      defaultValue: true,
    },
    {
      name: "locale",
      title: "国际化配置",
      defaultValue: null,
    },
    {
      name: "popupStyle",
      title: "浮层样式",
      defaultValue: {},
    },
    {
      name: "suffixIcon",
      title: "日历图标",
      defaultValue: null,
    },
    {
      name: "dateRender",
      title: "自定义日期单元格内容",
      defaultValue: null,
    },
    {
      name: "mode",
      title: "面板模式",
      defaultValue: "time",
    },
    {
      name: "transitionName",
      title: "过渡动画名称",
      defaultValue: "slide-up",
    },
    {
      name: "placement",
      title: "浮层位置",
      defaultValue: "bottomLeft",
    },
    {
      name: "autofocus",
      title: "自动获取焦点",
      defaultValue: false,
    },
    {
      name: "clearIcon",
      title: "自定义清除图标",
      defaultValue: null,
    },
    {
      name: "onOpenChange",
      title: "弹出面板打开关闭时触发",
      defaultValue: null,
    },
    {
      name: "className",
      title: "自定义类名",
      defaultValue: "",
    },
    {
      name: "style",
      title: "自定义内联样式",
      defaultValue: {},
    },
  ],
  configure: {
    component: {
      isContainer: false,
      isModal: false,
      description: "日期选择器，提供丰富的配置项来满足各种场景的需求。",
      device: ["pc", "mobile"],
      nestingRule: {
        childWhitelist: [], // 日期选择器不接受子元素
        parentWhitelist: [],
      },
    },
    props: [
      {
        name: "allowClear",
        title: "清除按钮",
        defaultValue: true,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "disabled",
        title: "禁用状态",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "disabledDate",
        title: "不可选择的日期",
        defaultValue: null,
        setter: {
          componentName: "FunctionSetter",
          props: {},
        },
      },
      {
        name: "format",
        title: "日期格式",
        defaultValue: "YYYY-MM-DD",
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "getPopupContainer",
        title: "菜单渲染父节点，默认渲染到 body 上",
        defaultValue: null,
        setter: {
          componentName: "FunctionSetter",
          props: {},
        },
      },
      {
        name: "placeholder",
        title: "占位提示",
        defaultValue: "",
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "size",
        title: "输入框大小",
        defaultValue: "default",
        setter: {
          componentName: "SelectSetter",
          props: {
            options: [
              { value: "large", label: "大" },
              { value: "default", label: "默认" },
              { value: "small", label: "小" },
            ],
          },
        },
      },
      {
        name: "value",
        title: "当前选中值",
        defaultValue: null,
        setter: {
          componentName: "StringSetter",
          props: {},
        },
      },
      {
        name: "defaultValue",
        title: "默认值",
        defaultValue: null,
        setter: {
          componentName: "StringSetter",
          props: {},
        },
      },
      {
        name: "onChange",
        title: "选中日期发生变化时触发",
        defaultValue: null,
        setter: {
          componentName: "FunctionSetter",
          props: {},
        },
      },
      {
        name: "onOk",
        title: "点击确认按钮时触发",
        defaultValue: null,
        setter: {
          componentName: "FunctionSetter",
          props: {},
        },
      },
      {
        name: "picker",
        title: "指定为日期选择还是周/月/年/日期时间选择",
        defaultValue: "date",
        setter: {
          componentName: "SelectSetter",
          props: {
            options: [
              { value: "date", label: "日期" },
              { value: "week", label: "周" },
              { value: "month", label: "月" },
              { value: "quarter", label: "季度" },
              { value: "year", label: "年" },
              { value: "time", label: "时间" },
            ],
          },
        },
      },
      {
        name: "showTime",
        title: "是否带时间选择",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "open",
        title: "弹出面板是否打开",
        defaultValue: null,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "panelRender",
        title: "自定义浮层内容",
        defaultValue: null,
        setter: {
          componentName: "ReactNodeSetter",
          props: {},
        },
      },
      {
        name: "renderExtraFooter",
        title: "在弹出面板底部添加额外的元素",
        defaultValue: null,
        setter: {
          componentName: "ReactNodeSetter",
          props: {},
        },
      },
      {
        name: "dropdownClassName",
        title: "下拉菜单的 className 属性",
        defaultValue: "",
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "inputReadOnly",
        title: "设置 input 只读，防止移动设备键盘弹出",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "variant",
        title: "是否展示边框样式",
        defaultValue: true,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "showToday",
        title: "是否显示今日按钮",
        defaultValue: true,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "locale",
        title: "国际化配置",
        defaultValue: null,
        setter: {
          componentName: "ObjectSetter",
          props: {},
        },
      },
      {
        name: "popupStyle",
        title: "浮层样式",
        defaultValue: {},
        setter: {
          componentName: "ObjectSetter",
          props: {},
        },
      },
      {
        name: "suffixIcon",
        title: "日历图标",
        defaultValue: null,
        setter: {
          componentName: "ReactNodeSetter",
          props: {},
        },
      },
      {
        name: "dateRender",
        title: "自定义日期单元格内容",
        defaultValue: null,
        setter: {
          componentName: "FunctionSetter",
          props: {},
        },
      },
      {
        name: "mode",
        title: "面板模式",
        defaultValue: "time",
        setter: {
          componentName: "SelectSetter",
          props: {
            options: [
              { value: "time", label: "时间" },
              { value: "date", label: "日期" },
              { value: "month", label: "月" },
              { value: "year", label: "年" },
            ],
          },
        },
      },
      {
        name: "transitionName",
        title: "过渡动画名称",
        defaultValue: "slide-up",
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "placement",
        title: "浮层位置",
        defaultValue: "bottomLeft",
        setter: {
          componentName: "SelectSetter",
          props: {
            options: [
              { value: "bottomLeft", label: "左下" },
              { value: "bottomRight", label: "右下" },
              { value: "topLeft", label: "左上" },
              { value: "topRight", label: "右上" },
            ],
          },
        },
      },
      {
        name: "autofocus",
        title: "自动获取焦点",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "clearIcon",
        title: "自定义清除图标",
        defaultValue: null,
        setter: {
          componentName: "ReactNodeSetter",
          props: {},
        },
      },
      {
        name: "onOpenChange",
        title: "弹出面板打开关闭时触发",
        defaultValue: null,
        setter: {
          componentName: "FunctionSetter",
          props: {},
        },
      },
      {
        name: "className",
        title: "自定义类名",
        defaultValue: "",
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "style",
        title: "自定义内联样式",
        defaultValue: {},
        setter: {
          componentName: "ObjectSetter",
          props: {},
        },
      },
    ],
  },
};

export { datePickerSchema };

export default datePickerSchema;
