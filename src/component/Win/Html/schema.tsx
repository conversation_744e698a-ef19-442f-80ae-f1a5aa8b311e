const htmlSchema = {
  version: "0.0.1",
  css: "",
  i18n: {},
  componentName: "Html",
  title: "HTML",
  description: "用于展示HTML内容。",
  docUrl: "https://ant-design.antgroup.com/components/card-cn",
  screenshot: "",
  icon: "",
  keywords: "容器，块，布局",
  snippets: [],
  group: "common",
  category: "display", // 数据展示组件
  priority: 0,
  sort: 0, // 排序
  children: [],
  props: [
    {
      name: "style",
      title: "内联样式",
      defaultValue: {},
    },
  ],
  configure: {
    component: {
      isContainer: true,
      isModal: false,
      description: "用于展示HTML内容。",
      device: ["pc", "mobile"],
      nestingRule: {
        childWhitelist: [], // 卡片可以接受任意子元素
        parentWhitelist: [],
      },
    },
    props: [
      {
        name: "style",
        title: "内联样式",
        defaultValue: {},
        setter: {
          componentName: "ObjectSetter",
          props: {},
        },
      },
    ],
  },
};

export { htmlSchema };

export default htmlSchema;
