import DOMPurify from "dompurify";
import "./index.less";

/* Html */
const WinHtml = (props) => {
  const { html = "<button>winyh</button>", children, ...restProps } = props;
  // 确保不会同时有 html 和 children
  if (html && children) {
    // console.warn(
    //   "Html<PERSON><PERSON><PERSON> received both `html` and `children`. Only one can be set."
    // );
  }

  const cleanHtml = html ? DOMPurify.sanitize(html) : "";

  return (
    <div {...restProps}>
      <div dangerouslySetInnerHTML={{ __html: cleanHtml }} />
    </div>
  );
};

export { WinHtml };

export default WinHtml;
