import { useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>, Row, Col } from "antd";
import { componentTag } from "@/utils/component";
import { DragAndDrop } from "@/core/dnd";
import { WinFormItem, FORM_ITEM_PROPS, extractFormItemProps } from "./item";
import { setGlobalForm, removeGlobalForm } from '@/utils/form';
import "./index.less";

/* 表单 */
const WinForm = (props) => {
  const { children, submitAfter, tableName, ...resetProps } = props;
  const [form] = Form.useForm()

  useEffect(() => {
    setGlobalForm(`${resetProps?.id}`, form);
    return () => removeGlobalForm(`${resetProps?.id}`);
  }, [form]);

  const onValuesChange = (data) => {
    console.log(data, { submitAfter, tableName })
  }

  const onSubmit = (values) => {
    console.log(form.getFieldsValue())
  }

  const { schema, tense } = children?.props || {}

  const FormItemList = schema?.map((child) => {
    let { props: allProps, componentName, uuid } = child;
    let formItemProps = extractFormItemProps(allProps); // 提取Form.Item相关属性
    let componentProps = { ...allProps };
    let ComponentTag = componentTag(componentName);

    // 移除Form.Item相关属性，保留Input属性
    FORM_ITEM_PROPS.forEach(key => {
      delete (componentProps)[key];
    });

    // 设计模式下表单字段收集功能待实现
    if (tense === "design") {
      return (
        <Col span={8}  key={`item-${uuid}`}>
          <WinFormItem {...formItemProps}>
            <DragAndDrop item={child} componentTag={ComponentTag}></DragAndDrop>
          </WinFormItem>
        </Col>
      );
    } else {
      return <Col span={8}  key={`item-${uuid}`}><WinFormItem {...formItemProps}><ComponentTag {...componentProps}></ComponentTag></WinFormItem></Col>
    }
  })

  return <Form {...resetProps} form={form} onValuesChange={onValuesChange} initialValues={{ username: "222", test: "222999" }}><Row>{FormItemList}</Row><Button htmlType="submit" onClick={onSubmit}>提交</Button></Form>;
};

export { WinForm };

export default WinForm;
