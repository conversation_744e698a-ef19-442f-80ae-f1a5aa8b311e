import { Form } from "antd";
import { FormItemProps } from "antd/lib/form";
import { ReactNode } from "react";
import "./index.less";

/**
 * Form.Item 支持的属性列表
 * 详细参数参考: https://ant.design/components/form-cn#Form.Item
 */
export const FORM_ITEM_PROPS = [
  "name", // 字段名
  "label", // 标签名
  "labelCol", // 标签布局
  "wrapperCol", // 控件布局
  "rules", // 校验规则
  "help", // 提示信息
  "extra", // 额外的提示信息
  "validateStatus", // 校验状态
  "hasFeedback", // 是否展示校验状态图标
  "colon", // 是否有冒号
  "required", // 是否必填
  "tooltip", // 标签提示
  "hidden", // 是否隐藏
  "initialValue", // 默认值
  "trigger", // 收集字段值的时机：自定义控件
  "valuePropName", // 子节点值的属性：自定义控件
  "getValueFromEvent", // 设置如何将事件的值转换成字段值
  "getValueProps", // 为子节点添加额外的属性 
  "normalize", // 转换字段值
  "shouldUpdate", // 当 Form 的 values 变化后触发重新渲染
  "dependencies", // 依赖的字段
  "noStyle", // 不带样式
  "labelAlign", // 标签文本对齐方式
  "messageVariables", // 默认验证字段的信息
  "validateFirst", // 当某一规则校验不通过时是否停止校验
  "htmlFor", // 设置子元素label的 htmlFor 属性
  "validateTrigger", // 设置字段校验的时机
];

/**
 * 扩展的FormItem组件接口
 */
export interface WinFormItemProps extends FormItemProps {
  defaultValue: string;
  children?: ReactNode;
}

/**
 * 提取表单项属性
 * 
 * @param props 传入的所有属性
 * @returns 分离后的FormItem属性
 */
export const extractFormItemProps = (props: Record<string, any>): Partial<FormItemProps> => {
  const formItemProps: Partial<FormItemProps> = {};
  
  Object.keys(props).forEach((key: string) => {
    if (FORM_ITEM_PROPS.includes(key)) {
      (formItemProps as any)[key] = props[key];
    }
  });
  
  return formItemProps;
};

/**
 * WinFormItem 组件
 * 
 * 对 Ant Design 的 Form.Item 组件进行二次封装
 * 
 * @param {WinFormItemProps} props - 组件属性
 * @returns {JSX.Element} - 返回封装后的表单项组件
 */
const WinFormItem = (props: WinFormItemProps) => {
  const { children, defaultValue, ...formItemProps } = props;
  return (
    <Form.Item {...formItemProps}>
      {children}
    </Form.Item>
  );
};

export { WinFormItem };

export default WinFormItem; 