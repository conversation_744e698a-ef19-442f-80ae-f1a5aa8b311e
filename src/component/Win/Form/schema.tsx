const schema = {
  version: "0.0.1",
  css: "",
  i18n: {},
  componentName: "Form",
  title: "表单",
  description: "所有列(Col)必须放在 Button 内",
  docUrl: "",
  screenshot: "",
  icon: "",
  keywords: "栅格，布局，分栏，行",
  snippets: [],
  group: "common",
  category: "container",
  children: [],
  priority: 0,
  sort: 0, // 排序
  props: [
    {
      name: "tableName",
      title: "数据表",
      description: "数据表",
      defaultValue: "",
    },
    {
      name: "submitAfter",
      title: "提交后动作",
      description: "表单提交后的动作",
      defaultValue: "reload",
    },
    {
      name: "justify",
      title: "水平排列",
      description: "水平排列方式",
      defaultValue: "start",
    },
    {
      name: "wrap",
      title: "自动换行",
      description: "是否自动换行",
      defaultValue: true,
    },
    {
      name: "className",
      title: "样式类名",
      description: "样式类名",
      defaultValue: "",
    },
    {
      name: "style",
      title: "行业样式",
      description: "行业样式",
      defaultValue: "",
    },
  ],
  configure: {
    component: {
      isContainer: true,
      isModal: false,
      description: "",
      device: ["pc", "mobile"],
      nestingRule: {
        childWhitelist: ["Col"],
        parentWhitelist: [],
      },
    },
    props: [
      {
        name: "tableName",
        title: "数据表",
        defaultValue: "",
        setter: {
          componentName: "StringSetter",
          props: {},
        },
      },
      {
        name: "submitAfter",
        title: "提交后动作",
        defaultValue: "",
        setter: {
          componentName: "SelectSetter",
          props: {
            options:[
              {
                label:"刷新页面",
                value:"reload",
              },
              {
                label:"跳转详情页",
                value:"detail",
              }
            ]
          },
        },
      }
    ],
  },
};

export { schema };

export default schema;
