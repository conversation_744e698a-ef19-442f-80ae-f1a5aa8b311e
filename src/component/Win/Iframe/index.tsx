import React, { useRef, useEffect } from "react";
import "./index.less";

/* Iframe */
const WinIframe = (props) => {
  const {
    src = "https://www.baidu.com/",
    title,
    onLoad,
    onMessage,
    ...restProps
  } = props;
  const iframeRef = useRef(null);

  // 处理 iframe 加载完成事件
  const handleLoad = () => {
    if (onLoad) {
      onLoad(iframeRef.current);
    }
  };

  // 监听来自 iframe 内部的消息事件
  useEffect(() => {
    const handleMessage = (event) => {
      if (onMessage && event.origin !== window.location.origin) {
        // 只有当消息来源与当前页面同源时才处理消息
        onMessage(event.data, event.origin);
      }
    };

    window.addEventListener("message", handleMessage);

    return () => {
      window.removeEventListener("message", handleMessage);
    };
  }, [onMessage]);

  return (
    <iframe
      ref={iframeRef}
      src={src}
      title={title || "inline frame"}
      height={800}
      width="100%"
      onLoad={handleLoad}
      {...restProps}
    />
  );
};

export { WinIframe };

export default WinIframe;
