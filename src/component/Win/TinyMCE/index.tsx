import { useRef } from 'react';
import { Editor } from '@tinymce/tinymce-react';
import type { Editor as TinyMCEEditor } from '@tinymce/tinymce-react';
import React from 'react';
import './index.less';

/**
 * TinyMCE富文本编辑器组件接口定义
 */
export interface TinyMCEProps {
  value?: string;
  onChange?: (value: string) => void;
  readOnly?: boolean;
  placeholder?: string;
  height?: number | string;
  menubar?: boolean;
  toolbar?: string | boolean;
  plugins?: string[];
  onImageUpload?: (file: File) => Promise<string>;
  // 表单相关属性
  name?: string;
  label?: React.ReactNode;
  rules?: any[];
  extra?: React.ReactNode;
  hidden?: boolean;
  initialValue?: any;
  required?: boolean;
  tooltip?: React.ReactNode;
  labelAlign?: 'left' | 'right';
  [key: string]: any;
}

const defaultPlugins = [
  'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
  'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
  'insertdatetime', 'media', 'table', 'help', 'wordcount'
];

const defaultToolbar =
  'undo redo | blocks | bold italic underline strikethrough | ' +
  'alignleft aligncenter alignright alignjustify | ' +
  'bullist numlist outdent indent | link image | removeformat | code | help';

/**
 * TinyMCE富文本编辑器组件
 * 
 * 对TinyMCE编辑器进行封装，支持表单集成
 * 支持直接传入Form.Item属性，会被Form组件自动处理
 * 
 * @param {TinyMCEProps} props - 组件属性
 * @returns {JSX.Element} - 返回封装后的富文本编辑器组件
 */
const TinyMCE = ({
  value = '',
  onChange,
  readOnly = false,
  placeholder = '请输入内容...',
  height = 400,
  menubar = true,
  toolbar = defaultToolbar,
  plugins = defaultPlugins,
  onImageUpload,
  ...restProps
}: TinyMCEProps) => {
  const editorRef = useRef<TinyMCEEditor | null>(null);

  // 图片上传处理
  const images_upload_handler = async (blobInfo: any) => {
    if (onImageUpload) {
      return onImageUpload(blobInfo.blob());
    }
    // 默认base64
    return new Promise<string>((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = () => reject('图片上传失败');
      reader.readAsDataURL(blobInfo.blob());
    });
  };

  // 失焦才触发-输入时不会
  const handleChange = (content: string) => {
    onChange?.(content);
  };

  const editorConfig = {
    height,
    menubar,
    plugins,
    toolbar,
    placeholder,
    readonly: readOnly,
    branding: false,
    resize: false,
    language: 'zh_CN',
    content_style:
      'body { font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; font-size: 14px; }',
    images_upload_handler,
    paste_data_images: true,
    image_advtab: true,
    image_title: true,
    automatic_uploads: true,
    file_picker_types: 'image',
    images_reuse_filename: true,
  };

  // 为了让Form组件能正确处理TinyMCE的值，添加特殊属性
  const formProps = {
    // 指定值的属性名
    valuePropName: 'value',
    // 指定触发值变化的事件名
    trigger: 'onEditorChange',
  };

  return (
    <div className="win-tinymce-container">
      <Editor
        apiKey="k1ejxq6mi215jgntq787je550dqziaf4h55v5r398haud4cz"
        onInit={(evt: any, editor: TinyMCEEditor) => {
          editorRef.current = editor;
        }}
        value={value}
        onEditorChange={handleChange}
        init={editorConfig}
        {...formProps}
        {...restProps}
      />
    </div>
  );
};

export default TinyMCE;
