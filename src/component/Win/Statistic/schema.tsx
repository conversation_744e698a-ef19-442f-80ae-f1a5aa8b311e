const statisticSchema = {
  version: "0.0.1",
  css: "",
  i18n: {},
  componentName: "Statistic",
  title: "统计值",
  description: "用于展示统计信息的组件。",
  docUrl: "https://ant-design.antgroup.com/components/statistic-cn",
  screenshot: "",
  icon: "",
  keywords: "统计，数据展示，信息",
  snippets: [],
  group: "common",
  category: "display", // 数据展示组件
  priority: 0,
  sort: 0, // 排序
  children: [],
  props: [
    {
      name: "title",
      title: "标题",
      defaultValue: null,
    },
    {
      name: "value",
      title: "数值",
      defaultValue: 0,
    },
    {
      name: "valueStyle",
      title: "数值样式",
      defaultValue: {},
    },
    {
      name: "prefix",
      title: "前缀",
      defaultValue: null,
    },
    {
      name: "suffix",
      title: "后缀",
      defaultValue: null,
    },
    {
      name: "loading",
      title: "载入状态",
      defaultValue: false,
    },
    {
      name: "className",
      title: "自定义类",
      defaultValue: "",
    },
    {
      name: "style",
      title: "内联样式",
      defaultValue: {},
    },
  ],
  methods: {},
  configure: {
    component: {
      isContainer: false,
      isModal: false,
      description: "统计值组件，用于展示统计信息。",
      device: ["pc", "mobile"],
      nestingRule: {
        childWhitelist: [], // 统计值组件不接受子元素
        parentWhitelist: [],
      },
    },
    props: [
      {
        name: "title",
        title: "标题",
        defaultValue: null,
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "value",
        title: "数值",
        defaultValue: 0,
        setter: {
          componentName: "NumberSetter",
          props: {},
        },
      },
      {
        name: "valueStyle",
        title: "数值样式",
        defaultValue: {},
        setter: {
          componentName: "ObjectSetter",
          props: {},
        },
      },
      {
        name: "prefix",
        title: "前缀",
        defaultValue: null,
        setter: {
          componentName: "StringSetter",
          props: {},
        },
      },
      {
        name: "suffix",
        title: "后缀",
        defaultValue: null,
        setter: {
          componentName: "StringSetter",
          props: {},
        },
      },
      {
        name: "loading",
        title: "载入状态",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "className",
        title: "自定义类",
        defaultValue: "",
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "style",
        title: "内联样式",
        defaultValue: {},
        setter: {
          componentName: "ObjectSetter",
          props: {},
        },
      },
    ],
  },
};

export { statisticSchema };
export default statisticSchema;
