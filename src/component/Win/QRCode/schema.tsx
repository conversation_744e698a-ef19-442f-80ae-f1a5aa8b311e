const qrcodeSchema = {
  version: "0.0.1",
  css: "",
  i18n: {},
  componentName: "QRCode",
  title: "二维码",
  description: "生成二维码图片，支持自定义大小、颜色等。",
  docUrl: "https://ant-design.antgroup.com/components/qr-code-cn",
  screenshot: "",
  icon: "",
  keywords: "二维码, 生成, 图片, 自定义",
  snippets: [],
  group: "common",
  category: "display",
  priority: 0,
  sort: 0, // 排序
  children: [],
  props: [
    {
      name: "value",
      title: "二维码内容",
      defaultValue: "winyh",
    },
    {
      name: "size",
      title: "二维码大小",
      defaultValue: 128,
    },
    {
      name: "level",
      title: "纠错级别",
      defaultValue: "L",
    },
    {
      name: "bgColor",
      title: "背景颜色",
      defaultValue: "#ffffff",
    },
    {
      name: "fgColor",
      title: "前景颜色",
      defaultValue: "#000000",
    },
    {
      name: "style",
      title: "内联样式",
      defaultValue: {},
    },
  ],
  methods: {},
  configure: {
    component: {
      isContainer: false,
      isModal: false,
      description: "二维码组件，用于生成二维码图片，支持自定义大小、颜色等。",
      device: ["pc", "mobile"],
      nestingRule: {
        childWhitelist: [],
        parentWhitelist: [],
      },
    },
    props: [
      {
        name: "value",
        title: "二维码内容",
        defaultValue: "winyh",
        setter: {
          componentName: "StringSetter",
          props: {},
        },
      },
      {
        name: "size",
        title: "二维码大小",
        defaultValue: 128,
        setter: {
          componentName: "NumberSetter",
          props: {},
        },
      },
      {
        name: "level",
        title: "纠错级别",
        defaultValue: "L",
        setter: {
          componentName: "SelectSetter",
          props: {
            options: [
              { value: "L", label: "低" },
              { value: "M", label: "中" },
              { value: "Q", label: "高" },
              { value: "H", label: "最高" },
            ],
          },
        },
      },
      {
        name: "bgColor",
        title: "背景颜色",
        defaultValue: "#ffffff",
        setter: {
          componentName: "ColorSetter",
          props: {},
        },
      },
      {
        name: "fgColor",
        title: "前景颜色",
        defaultValue: "#000000",
        setter: {
          componentName: "ColorSetter",
          props: {},
        },
      },
      {
        name: "style",
        title: "内联样式",
        defaultValue: {},
        setter: {
          componentName: "ObjectSetter",
          props: {},
        },
      },
    ],
  },
};

export { qrcodeSchema };
export default qrcodeSchema;
