const drawerSchema = {
  version: "0.0.1",
  css: "",
  i18n: {},
  componentName: "Drawer",
  title: "抽屉",
  description: "用于在页面边缘弹出一个浮层，常用于显示更多操作或内容。",
  docUrl: "https://ant-design.antgroup.com/components/drawer-cn",
  screenshot: "",
  icon: "",
  keywords: "抽屉，侧边栏，弹出框",
  snippets: [],
  group: "common",
  category: "feedback", // 反馈组件
  priority: 0,
  sort: 0, // 排序
  children: "抽屉内容",
  props: [
    {
      name: "title",
      title: "标题",
      defaultValue: "标题",
    },
    {
      name: "open",
      title: "是否显示抽屉",
      defaultValue: true,
    },
    {
      name: "placement",
      title: "抽屉出现的位置",
      defaultValue: "right",
    },
    {
      name: "closable",
      title: "是否显示右上角的关闭按钮",
      defaultValue: true,
    },
    {
      name: "closeIcon",
      title: "自定义关闭图标",
      defaultValue: null,
    },
    {
      name: "mask",
      title: "是否有遮罩",
      defaultValue: true,
    },
    {
      name: "maskClosable",
      title: "点击蒙层是否允许关闭",
      defaultValue: true,
    },
    {
      name: "destroyOnHidden",
      title: "关闭时销毁 DOM",
      defaultValue: false,
    },
    {
      name: "forceRender",
      title: "强制渲染 DOM",
      defaultValue: false,
    },
    {
      name: "getContainer",
      title: "指定挂载的 HTML 节点",
      defaultValue: false,
    },
    {
      name: "keyboard",
      title: "是否支持键盘 ESC 关闭",
      defaultValue: true,
    },
    {
      name: "bodyStyle",
      title: "自定义 body 样式",
      defaultValue: {},
    },
    {
      name: "headerStyle",
      title: "自定义 header 样式",
      defaultValue: {},
    },
    {
      name: "footerStyle",
      title: "自定义 footer 样式",
      defaultValue: {},
    },
    {
      name: "maskStyle",
      title: "自定义 mask 样式",
      defaultValue: {},
    },
    {
      name: "width",
      title: "宽度",
      defaultValue: 256,
    },
    {
      name: "height",
      title: "高度",
      defaultValue: null,
    },
    {
      name: "zIndex",
      title: "设置 Drawer 的层级",
      defaultValue: 1000,
    },
    {
      name: "onClose",
      title: "关闭时触发的回调函数",
      defaultValue: null,
    },
    {
      name: "afterOpenChange",
      title: "抽屉可见状态变化后的回调",
      defaultValue: null,
    },
    {
      name: "push",
      title: "开启 push 动画效果",
      defaultValue: true,
    },
    {
      name: "extra",
      title: "额外的操作区",
      defaultValue: null,
    },
    {
      name: "rootClassName",
      title: "根节点样式类名",
      defaultValue: "",
    },
  ],
  configure: {
    component: {
      isContainer: true,
      isModal: true,
      description: "抽屉，提供丰富的配置项来满足各种场景的需求。",
      device: ["pc", "mobile"],
      nestingRule: {
        childWhitelist: [], // 抽屉可以接受任意子元素
        parentWhitelist: [],
      },
    },
    props: [
      {
        name: "title",
        title: "标题",
        defaultValue: "标题",
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "open",
        title: "是否显示抽屉",
        defaultValue: true,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "placement",
        title: "抽屉出现的位置",
        defaultValue: "right",
        setter: {
          componentName: "SelectSetter",
          props: {
            style: { width: "100%" },
            options: [
              { value: "top", label: "顶部" },
              { value: "left", label: "左侧" },
              { value: "right", label: "右侧" },
              { value: "bottom", label: "底部" },
            ],
          },
        },
      },
      {
        name: "closable",
        title: "是否显示右上角的关闭按钮",
        defaultValue: true,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "closeIcon",
        title: "自定义关闭图标",
        defaultValue: null,
        setter: {
          componentName: "ReactNodeSetter",
          props: {},
        },
      },
      {
        name: "mask",
        title: "是否有遮罩",
        defaultValue: true,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "maskClosable",
        title: "点击蒙层是否允许关闭",
        defaultValue: true,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "destroyOnHidden",
        title: "关闭时销毁 DOM",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "forceRender",
        title: "强制渲染 DOM",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "getContainer",
        title: "指定挂载的 HTML 节点",
        defaultValue: false,
        setter: {
          componentName: "FunctionSetter",
          props: {},
        },
      },
      {
        name: "keyboard",
        title: "是否支持键盘 ESC 关闭",
        defaultValue: true,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "bodyStyle",
        title: "自定义 body 样式",
        defaultValue: {},
        setter: {
          componentName: "ObjectSetter",
          props: {},
        },
      },
      {
        name: "headerStyle",
        title: "自定义 header 样式",
        defaultValue: {},
        setter: {
          componentName: "ObjectSetter",
          props: {},
        },
      },
      {
        name: "footerStyle",
        title: "自定义 footer 样式",
        defaultValue: {},
        setter: {
          componentName: "ObjectSetter",
          props: {},
        },
      },
      {
        name: "maskStyle",
        title: "自定义 mask 样式",
        defaultValue: {},
        setter: {
          componentName: "ObjectSetter",
          props: {},
        },
      },
      {
        name: "width",
        title: "宽度",
        defaultValue: 256,
        setter: {
          componentName: "NumberSetter",
          props: {},
        },
      },
      {
        name: "height",
        title: "高度",
        defaultValue: null,
        setter: {
          componentName: "NumberSetter",
          props: {},
        },
      },
      {
        name: "zIndex",
        title: "设置 Drawer 的层级",
        defaultValue: 1000,
        setter: {
          componentName: "NumberSetter",
          props: {},
        },
      },
      {
        name: "onClose",
        title: "关闭时触发的回调函数",
        defaultValue: null,
        setter: {
          componentName: "FunctionSetter",
          props: {},
        },
      },
      {
        name: "afterOpenChange",
        title: "抽屉可见状态变化后的回调",
        defaultValue: null,
        setter: {
          componentName: "FunctionSetter",
          props: {},
        },
      },
      {
        name: "push",
        title: "开启 push 动画效果",
        defaultValue: true,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "extra",
        title: "额外的操作区",
        defaultValue: null,
        setter: {
          componentName: "ReactNodeSetter",
          props: {},
        },
      },
      {
        name: "rootClassName",
        title: "根节点样式类名",
        defaultValue: "",
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
    ],
  },
};

export { drawerSchema };

export default drawerSchema;
