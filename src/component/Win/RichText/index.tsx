import { useEffect, useRef, useState } from 'react';
import ReactQuill from 'react-quill-new';
import type { QuillOptionsStatic } from 'react-quill-new';
import 'react-quill-new/dist/quill.snow.css';
import './index.less';

interface RichTextProps {
  value?: string;
  onChange?: (value: string) => void;
  readOnly?: boolean;
  placeholder?: string;
  height?: number | string;
  toolbar?: boolean;
  modules?: QuillOptionsStatic['modules'];
  formats?: string[];
  onImageUpload?: (file: File) => Promise<string>;
}

const RichText = ({
  value = '',
  onChange,
  readOnly = false,
  placeholder = '请输入内容...',
  height = 400,
  toolbar = true,
  modules,
  formats = [
    'header',
    'bold', 'italic', 'underline', 'strike',
    'list', 'indent',
    'link', 'image',
    'color', 'background',
    'align'
  ],
  onImageUpload
}: RichTextProps) => {
  const [editorValue, setEditorValue] = useState(value);
  const quillRef = useRef<ReactQuill>(null);

  useEffect(() => {
    setEditorValue(value);
  }, [value]);

  const defaultModules: QuillOptionsStatic['modules'] = {
    toolbar: toolbar ? {
      container: [
        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
        ['bold', 'italic', 'underline', 'strike'],
        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
        [{ 'indent': '-1'}, { 'indent': '+1' }],
        [{ 'color': [] }, { 'background': [] }],
        [{ 'align': [] }],
        ['link', 'image'],
        ['clean']
      ],
      handlers: {
        image: function() {
          const input = document.createElement('input');
          input.setAttribute('type', 'file');
          input.setAttribute('accept', 'image/*');
          input.click();

          input.onchange = async () => {
            const file = input.files?.[0];
            if (file) {
              try {
                let imageUrl: string;
                if (onImageUpload) {
                  // 使用自定义上传函数
                  imageUrl = await onImageUpload(file);
                } else {
                  // 默认使用 base64
                  imageUrl = await new Promise((resolve) => {
                    const reader = new FileReader();
                    reader.onload = (e) => resolve(e.target?.result as string);
                    reader.readAsDataURL(file);
                  });
                }

                const range = quillRef.current?.getEditor().getSelection();
                if (range) {
                  quillRef.current?.getEditor().insertEmbed(range.index, 'image', imageUrl);
                }
              } catch (error) {
                console.error('图片上传失败:', error);
              }
            }
          };
        }
      }
    } : false,
    clipboard: {
      matchVisual: false
    },
    history: {
      delay: 2000,
      maxStack: 500,
      userOnly: true
    },
    syntax: false,
    table: false
  };

  const handleChange = (content: string) => {
    setEditorValue(content);
    onChange?.(content);
  };

  return (
    <div className="win-rich-text-container" style={{ height }}>
      <ReactQuill
        ref={quillRef}
        theme="snow"
        value={editorValue}
        onChange={handleChange}
        modules={modules || defaultModules}
        formats={formats}
        readOnly={readOnly}
        placeholder={placeholder}
      />
    </div>
  );
};

export { RichText };
export default RichText;
