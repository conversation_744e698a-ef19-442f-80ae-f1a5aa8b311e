.winrich {
  height: 100%;
}

.ql-container {
  height: calc(100% - 42px);
}

.win-rich-text-container {
  .winrich {
    .ql-container {
      min-height: 200px;
      font-size: 14px;
      line-height: 1.5;
    }

    .ql-editor {
      min-height: 200px;
      padding: 12px 15px;
    }

    .ql-toolbar {
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
      background-color: #fafafa;
    }

    .ql-container {
      border-bottom-left-radius: 4px;
      border-bottom-right-radius: 4px;
    }
  }

  .ql-container {
    font-size: 14px;
    line-height: 1.5;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    border-color: #d9d9d9;
  }

  .ql-toolbar {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    border-color: #d9d9d9;
    background-color: #fafafa;
  }

  .ql-editor {
    min-height: 200px;
    padding: 12px 15px;
  }

  &:hover {
    .ql-container,
    .ql-toolbar {
      border-color: #40a9ff;
    }
  }

  &:focus-within {
    .ql-container,
    .ql-toolbar {
      border-color: #40a9ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }

  // 工具栏按钮样式
  .ql-toolbar button {
    color: rgba(0, 0, 0, 0.85);
    
    &:hover {
      color: #40a9ff;
    }

    &.ql-active {
      color: #1890ff;
    }
  }

  // 下拉菜单样式
  .ql-picker {
    color: rgba(0, 0, 0, 0.85);
    
    &:hover {
      color: #40a9ff;
    }

    &.ql-expanded {
      color: #1890ff;
    }
  }

  // 链接输入框样式
  .ql-tooltip {
    background-color: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    
    input[type="text"] {
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      padding: 4px 11px;
      
      &:hover {
        border-color: #40a9ff;
      }
      
      &:focus {
        border-color: #40a9ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    }
  }
}

.win-tinymce-container {
  .tox-tinymce {
    border-radius: 4px;
    border-color: #d9d9d9;
    
    &:hover {
      border-color: #40a9ff;
    }
    
    &:focus-within {
      border-color: #40a9ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }

  .tox .tox-toolbar__primary {
    background: #fafafa;
  }

  .tox .tox-toolbar__overflow {
    background: #fafafa;
  }

  .tox .tox-tbtn {
    color: rgba(0, 0, 0, 0.85);
    
    &:hover {
      background: #e6f7ff;
    }
  }

  .tox .tox-tbtn--enabled {
    background: #e6f7ff;
  }
}
