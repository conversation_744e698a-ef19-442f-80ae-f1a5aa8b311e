/* 轮播图组件配置 */
const meta = {
  componentName: "Carousel",
  version: "0.0.1",
  css: "",
  i18n: {},
  props: {
    autoplay: {
      type: "boolean",
      value: false
    },
    dotPosition: {
      type: "string",
      value: "bottom",
      options: ["top", "bottom", "left", "right"]
    },
    effect: {
      type: "string",
      value: "scrollx",
      options: ["scrollx", "fade"]
    },
    speed: {
      type: "number",
      value: 500
    },
    easing: {
      type: "string",
      value: "linear"
    },
    className: [""],
    style: {
      width: "100%",
      height: 300
    }
  },
  events: {
    afterChange: {
      type: "JSFunction",
      value: "function(current) {}"
    },
    beforeChange: {
      type: "JSFunction",
      value: "function(from, to) {}"
    }
  },
  methods: {},
  dataSource: {
    list: [],
    dataHandler: {
      type: "JSFunction",
      value: "function(dataMap) { }"
    }
  }
};

export default meta;