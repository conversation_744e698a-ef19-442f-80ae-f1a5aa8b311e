/* 轮播图组件样式 */
.win-carousel {
  .slick-slider {
    position: relative;
    height: 100%;
  }

  /* 自定义dots样式 */
  .slick-dots {
    bottom: 20px;
    
    li {
      margin: 0 4px;
      
      button {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.5);
        
        &:hover {
          background: rgba(255, 255, 255, 0.8);
        }
      }
      
      &.slick-active button {
        background: #1890ff;
        width: 16px;
        border-radius: 8px;
      }
    }
  }

  /* 自定义箭头样式 */
  .slick-arrow {
    width: 40px;
    height: 40px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    z-index: 1;
    
    &:hover {
      background: rgba(0, 0, 0, 0.5);
    }
    
    &::before {
      font-size: 20px;
      color: white;
    }
  }
  
  .slick-prev {
    left: 20px;
  }
  
  .slick-next {
    right: 20px;
  }
}