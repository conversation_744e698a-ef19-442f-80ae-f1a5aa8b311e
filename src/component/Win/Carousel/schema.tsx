const CarouselSchema = {
  version: "0.0.1",
  css: "",
  i18n: {},
  componentName: "Carousel",
  title: "轮播图",
  description: "用于展示图片或内容的轮播组件",
  docUrl: "https://ant-design.antgroup.com/components/carousel-cn",
  screenshot: "",
  icon: "",
  keywords: "轮播,幻灯片,图片展示",
  group: "common",
  category: "display",
  priority: 0,
  sort: 0,
  children: [],
  props: [
    {
      name: "autoplay",
      title: "自动播放",
      type: "boolean",
      defaultValue: false,
      description: "是否自动切换"
    },
    {
      name: "dotPosition",
      title: "指示点位置",
      type: "string",
      defaultValue: "bottom",
      required: true,
      enum: [
        { label: "上", value: "top" },
        { label: "下", value: "bottom" },
        { label: "左", value: "left" },
        { label: "右", value: "right" }
      ],
      description: "指示点位置，可选 top bottom left right",
      rules: [
        {
          validator: (value: string) => {
            const validValues = ['top', 'bottom', 'left', 'right'];
            return validValues.includes(value || '');
          },
          message: '指示点位置必须是 top/bottom/left/right 之一'
        }
      ]
    },
    {
      name: "effect",
      title: "切换效果",
      type: "string",
      defaultValue: "scrollx",
      required: true,
      enum: [
        { label: "滑动", value: "scrollx" },
        { label: "淡入淡出", value: "fade" }
      ],
      description: "动画效果，可选 scrollx 或 fade",
      rules: [
        {
          validator: (value: string) => {
            const validValues = ['scrollx', 'fade'];
            return validValues.includes(value || '');
          },
          message: '切换效果必须是 scrollx 或 fade'
        }
      ]
    },
    {
      name: "speed",
      title: "切换速度",
      type: "number",
      defaultValue: 500,
      min: 100,
      max: 5000,
      description: "切换动画速度（毫秒）"
    },
    {
      name: "easing",
      title: "动画曲线",
      type: "string",
      defaultValue: "linear",
      description: "动画效果曲线"
    },
    {
      name: "style",
      title: "容器样式",
      type: "object",
      defaultValue: {
        width: "100%",
        height: 300
      },
      description: "自定义容器样式"
    }
  ],
  configure: {
    component: {
      isContainer: true,
      isModal: false,
      description: "支持自动播放、多种切换效果的轮播组件",
      device: ["pc", "mobile"],
      nestingRule: {
        childWhitelist: [],
        parentWhitelist: [],
      },
    },
    props: [
      {
        name: "autoplay",
        title: "自动播放",
        setter: "BoolSetter",
        defaultValue: false
      },
      {
        name: "dotPosition",
        title: "指示点位置",
        setter: {
          componentName: "RadioGroupSetter",
          props: {
            options: [
              { label: "上", value: "top" },
              { label: "下", value: "bottom" },
              { label: "左", value: "left" },
              { label: "右", value: "right" }
            ]
          }
        },
        defaultValue: "bottom"
      },
      {
        name: "effect",
        title: "切换效果",
        setter: {
          componentName: "RadioGroupSetter",
          props: {
            options: [
              { label: "滑动", value: "scrollx" },
              { label: "淡入淡出", value: "fade" }
            ]
          }
        },
        defaultValue: "scrollx"
      },
      {
        name: "speed",
        title: "切换速度",
        setter: {
          componentName: "NumberSetter",
          props: {
            min: 100,
            max: 5000,
            step: 100
          }
        },
        defaultValue: 500
      },
      {
        name: "easing",
        title: "动画曲线",
        setter: "StringSetter",
        defaultValue: "linear"
      },
      {
        name: "style",
        title: "容器样式",
        setter: "StyleSetter",
        defaultValue: {
          width: "100%",
          height: 300
        }
      }
    ]
  }
};

export { CarouselSchema };

export default CarouselSchema;