const meta = {
  componentName: "Flex",
  props: {
    vertical: false,
    wrap: "nowrap",
    justify: "normal",
    align: "normal",
    flex: "none",
    gap: "small",
    className: [""],
    style: {},
  },
  css: "",
  children: [],
  events: {
    onClick: {
      type: "JSFunction",
      value: "function(e) { console.log('flex click') }",
    },
  },
  methods: {},
  dataSource: {
    list: [],
    dataHandler: {
      type: "JSFunction",
      value: "function(dataMap) { }",
    },
  },
};

export default meta;