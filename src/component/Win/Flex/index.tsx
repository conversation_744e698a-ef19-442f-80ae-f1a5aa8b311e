import { Flex } from "antd";
import "./index.less";

const WinFlex = (props: any) => {
  const {
    style,
    className,
    vertical,
    wrap,
    justify,
    align,
    flex,
    gap,
    children,
  } = props;
  
  return (
    <Flex
      style={style}
      className={className}
      vertical={vertical}
      wrap={wrap}
      justify={justify}
      align={align}
      flex={flex}
      gap={gap}
    >
      {children}
    </Flex>
  );
};

export { WinFlex };
export default WinFlex;