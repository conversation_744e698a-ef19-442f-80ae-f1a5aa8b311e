const flexSchema = {
  version: "0.0.1",
  css: "",
  i18n: {},
  componentName: "Flex",
  title: "弹性布局",
  description: "弹性布局组件，基于 CSS Flexbox 实现，提供了更加灵活的布局方式。",
  docUrl: "https://ant.design/components/flex-cn",
  screenshot: "",
  icon: "",
  keywords: "弹性布局，Flex，布局，Flexbox",
  snippets: [],
  group: "common",
  category: "layout",
  priority: 0,
  sort: 4,
  npm: {
    package: "antd",
    exportName: "Flex",
    subName: "",
    destructuring: "",
    main: "",
    version: "",
  },
  children: [],
  props: [
    {
      name: "vertical",
      title: "垂直方向",
      description: "flex 主轴的方向是否为垂直",
      defaultValue: false,
    },
    {
      name: "wrap",
      title: "换行",
      description: "设置元素单行显示还是多行显示",
      defaultValue: "nowrap",
    },
    {
      name: "justify",
      title: "主轴对齐",
      description: "设置元素在主轴方向上的对齐方式",
      defaultValue: "normal",
    },
    {
      name: "align",
      title: "交叉轴对齐",
      description: "设置元素在交叉轴方向上的对齐方式",
      defaultValue: "normal",
    },
    {
      name: "flex",
      title: "flex 属性",
      description: "flex CSS 简写属性",
      defaultValue: "none",
    },
    {
      name: "gap",
      title: "间距",
      description: "设置网格之间的间隙",
      defaultValue: "small",
    },
    {
      name: "className",
      title: "样式类名",
      description: "样式类名",
      defaultValue: "",
    },
    {
      name: "style",
      title: "行内样式",
      description: "行内样式",
      defaultValue: {},
    },
  ],
  events: [],
  configure: {
    component: {
      isContainer: true,
      isModal: false,
      description: "弹性布局组件，基于 CSS Flexbox 实现，提供了更加灵活的布局方式。",
      device: ["pc", "mobile"],
      nestingRule: {
        childWhitelist: [],
        parentWhitelist: [],
      },
    },
    props: [
      {
        name: "vertical",
        title: "垂直方向",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "wrap",
        title: "换行",
        defaultValue: "nowrap",
        setter: {
          componentName: "SelectSetter",
          props: {
            options: [
              { value: "nowrap", label: "不换行" },
              { value: "wrap", label: "换行" },
              { value: "wrap-reverse", label: "反向换行" },
            ],
          },
        },
      },
      {
        name: "justify",
        title: "主轴对齐",
        defaultValue: "normal",
        setter: {
          componentName: "SelectSetter",
          props: {
            options: [
              { value: "normal", label: "默认" },
              { value: "flex-start", label: "起始对齐" },
              { value: "flex-end", label: "末尾对齐" },
              { value: "center", label: "居中对齐" },
              { value: "space-between", label: "两端对齐" },
              { value: "space-around", label: "环绕对齐" },
              { value: "space-evenly", label: "均匀对齐" },
            ],
          },
        },
      },
      {
        name: "align",
        title: "交叉轴对齐",
        defaultValue: "normal",
        setter: {
          componentName: "SelectSetter",
          props: {
            options: [
              { value: "normal", label: "默认" },
              { value: "flex-start", label: "起始对齐" },
              { value: "flex-end", label: "末尾对齐" },
              { value: "center", label: "居中对齐" },
              { value: "stretch", label: "拉伸对齐" },
            ],
          },
        },
      },
      {
        name: "flex",
        title: "flex 属性",
        defaultValue: "none",
        setter: {
          componentName: "MixedSetter",
          props: {
            setters: [
              "StringSetter",
              "NumberSetter",
            ],
          },
        },
      },
      {
        name: "gap",
        title: "间距",
        defaultValue: "small",
        setter: {
          componentName: "MixedSetter",
          props: {
            setters: [
              {
                componentName: "SelectSetter",
                props: {
                  options: [
                    { value: "small", label: "小" },
                    { value: "middle", label: "中" },
                    { value: "large", label: "大" },
                  ],
                },
              },
              {
                componentName: "NumberSetter",
                props: {
                  min: 0,
                },
              },
            ],
          },
        },
      },
      {
        name: "className",
        title: "样式类名",
        defaultValue: "",
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "style",
        title: "行内样式",
        defaultValue: {},
        setter: {
          componentName: "ObjectSetter",
          props: {},
        },
      },
    ],
    supports: {
      events: ["onClick"],
      loop: true,
      condition: true,
      style: true,
      className: true,
    },
    advanced: {},
  },
};

export { flexSchema };
export default flexSchema;