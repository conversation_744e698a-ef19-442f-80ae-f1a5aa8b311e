const modalSchema = {
  version: "0.0.1",
  css: "",
  i18n: {},
  componentName: "Modal",
  title: "模态框",
  description: "用于对话框展示，如提示、确认、表单等。",
  docUrl: "https://ant-design.antgroup.com/components/modal-cn",
  screenshot: "",
  icon: "",
  keywords: "模态框，弹窗，对话框，确认框",
  snippets: [],
  group: "common",
  category: "feedback", // 反馈组件
  priority: 0,
  sort: 0, // 排序
  children: [],
  props: [
    {
      name: "title",
      title: "标题",
      defaultValue: null,
    },
    {
      name: "open",
      title: "是否显示模态框",
      defaultValue: true,
    },
    {
      name: "onOk",
      title: "点击确定时触发的回调函数",
      defaultValue: null,
    },
    {
      name: "onCancel",
      title: "点击取消或关闭时触发的回调函数",
      defaultValue: null,
    },
    {
      name: "okText",
      title: "确定按钮文字",
      defaultValue: "确定",
    },
    {
      name: "cancelText",
      title: "取消按钮文字",
      defaultValue: "取消",
    },
    {
      name: "okType",
      title: "确定按钮类型",
      defaultValue: "primary",
    },
    {
      name: "closable",
      title: "是否显示右上角的关闭按钮",
      defaultValue: true,
    },
    {
      name: "maskClosable",
      title: "点击蒙层是否允许关闭",
      defaultValue: true,
    },
    {
      name: "destroyOnHidden",
      title: "关闭时销毁 DOM",
      defaultValue: false,
    },
    {
      name: "forceRender",
      title: "强制渲染 DOM",
      defaultValue: false,
    },
    {
      name: "getContainer",
      title: "指定挂载的 HTML 节点",
      defaultValue: null,
    },
    {
      name: "keyboard",
      title: "是否支持键盘 ESC 关闭",
      defaultValue: true,
    },
    {
      name: "mask",
      title: "是否有遮罩",
      defaultValue: true,
    },
    {
      name: "maskStyle",
      title: "自定义 mask 样式",
      defaultValue: {},
    },
    {
      name: "zIndex",
      title: "设置 Modal 的层级",
      defaultValue: 1000,
    },
    {
      name: "width",
      title: "宽度",
      defaultValue: 520,
    },
    {
      name: "centered",
      title: "是否垂直居中展示",
      defaultValue: false,
    },
    {
      name: "bodyStyle",
      title: "自定义 body 样式",
      defaultValue: {},
    },
    {
      name: "footer",
      title: "自定义页脚",
      defaultValue: null,
    },
    {
      name: "className",
      title: "自定义类名",
      defaultValue: "",
    },
    {
      name: "style",
      title: "自定义内联样式",
      defaultValue: {},
    },
    {
      name: "confirmLoading",
      title: "确定按钮 loading 状态",
      defaultValue: false,
    },
    {
      name: "afterClose",
      title: "关闭后触发的回调函数",
      defaultValue: null,
    },
    {
      name: "closeIcon",
      title: "自定义关闭图标",
      defaultValue: null,
    },
  ],
  configure: {
    component: {
      isContainer: true,
      isModal: true,
      description: "模态框，提供丰富的配置项来满足各种场景的需求。",
      device: ["pc", "mobile"],
      nestingRule: {
        childWhitelist: [], // 模态框可以接受任意子元素
        parentWhitelist: [],
      },
    },
    props: [
      {
        name: "title",
        title: "标题",
        defaultValue: null,
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "open",
        title: "是否显示模态框",
        defaultValue: true,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "onOk",
        title: "点击确定时触发的回调函数",
        defaultValue: null,
        setter: {
          componentName: "FunctionSetter",
          props: {},
        },
      },
      {
        name: "onCancel",
        title: "点击取消或关闭时触发的回调函数",
        defaultValue: null,
        setter: {
          componentName: "FunctionSetter",
          props: {},
        },
      },
      {
        name: "okText",
        title: "确定按钮文字",
        defaultValue: "确定",
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "cancelText",
        title: "取消按钮文字",
        defaultValue: "取消",
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "okType",
        title: "确定按钮类型",
        defaultValue: "primary",
        setter: {
          componentName: "SelectSetter",
          props: {
            style: { width: "100%" },
            options: [
              { value: "primary", label: "主要" },
              { value: "default", label: "默认" },
              { value: "dashed", label: "虚线" },
              { value: "text", label: "文本" },
              { value: "link", label: "链接" },
            ],
          },
        },
      },
      {
        name: "closable",
        title: "是否显示右上角的关闭按钮",
        defaultValue: true,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "maskClosable",
        title: "点击蒙层是否允许关闭",
        defaultValue: true,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "destroyOnHidden",
        title: "关闭时销毁 DOM",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "forceRender",
        title: "强制渲染 DOM",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "getContainer",
        title: "指定挂载的 HTML 节点",
        defaultValue: null,
        setter: {
          componentName: "FunctionSetter",
          props: {},
        },
      },
      {
        name: "keyboard",
        title: "是否支持键盘 ESC 关闭",
        defaultValue: true,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "mask",
        title: "是否有遮罩",
        defaultValue: true,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "maskStyle",
        title: "自定义 mask 样式",
        defaultValue: {},
        setter: {
          componentName: "ObjectSetter",
          props: {},
        },
      },
      {
        name: "zIndex",
        title: "设置 Modal 的层级",
        defaultValue: 1000,
        setter: {
          componentName: "NumberSetter",
          props: {},
        },
      },
      {
        name: "width",
        title: "宽度",
        defaultValue: 520,
        setter: {
          componentName: "NumberSetter",
          props: {},
        },
      },
      {
        name: "centered",
        title: "是否垂直居中展示",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "bodyStyle",
        title: "自定义 body 样式",
        defaultValue: {},
        setter: {
          componentName: "ObjectSetter",
          props: {},
        },
      },
      {
        name: "footer",
        title: "自定义页脚",
        defaultValue: null,
        setter: {
          componentName: "ReactNodeSetter",
          props: {},
        },
      },
      {
        name: "className",
        title: "自定义类名",
        defaultValue: "",
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "style",
        title: "自定义内联样式",
        defaultValue: {},
        setter: {
          componentName: "ObjectSetter",
          props: {},
        },
      },
      {
        name: "confirmLoading",
        title: "确定按钮 loading 状态",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "afterClose",
        title: "关闭后触发的回调函数",
        defaultValue: null,
        setter: {
          componentName: "FunctionSetter",
          props: {},
        },
      },
      {
        name: "closeIcon",
        title: "自定义关闭图标",
        defaultValue: null,
        setter: {
          componentName: "ReactNodeSetter",
          props: {},
        },
      },
    ],
  },
};

export { modalSchema };

export default modalSchema;
