/* 配置生成的渲染数据 */
const meta = {
  componentName: "Modal",
  props: {
    align: "top",
    gutter: [12, 12],
    justify: "start",
    wrap: false,
    className: [""],
    style: {
      width: 100,
      height: 20,
    },
    text: {
      type: "JSExpression",
      value: "this.state.btnText",
    },
    events: {
      onClick: {
        type: "JSFunction",
        value:
          "function(e) {\
          console.log('btn click')\
        }",
      },
      onHover: {
        type: "JSFunction",
        value:
          "function(e) {\
          console.log('btn click')\
        }",
      },
    },
  },
  css: "", // 当前组件全局样式
  children: [],
  methods: {
    showInfo: {
      type: "JSFunction",
      value:
        "function() {\
        console.log('test func');\
      }",
    },
  }, // 自定义方法
  dataSource: {
    list: [
      {
        id: "list",
        isInit: true,
        type: "fetch/mtop/jsonp",
        options: {
          uri: "",
          params: {},
          method: "GET",
          isCors: true,
          timeout: 5000,
          headers: {},
        },
        dataHandler: {
          type: "JSFunction",
          value: "function(data, err) {}",
        },
      },
    ],
    dataHandler: {
      type: "JSFunction",
      value: "function(dataMap) { }",
    },
  },
};
