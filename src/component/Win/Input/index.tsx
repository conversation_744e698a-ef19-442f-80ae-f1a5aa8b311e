import { Input } from "antd";
import { InputProps } from "antd/lib/input";
import React from "react";
import "./index.less";

/**
 * 输入框组件接口定义
 * 继承 InputProps 并添加表单相关属性
 */
export interface WinInputProps extends InputProps {
  // 表单相关属性
  name?: string;
  label?: React.ReactNode;
  rules?: any[];
  [key: string]: any;
}

/**
 * WinInput 组件
 * 
 * 对 Ant Design 的 Input 组件进行二次封装
 * 支持直接传入Form.Item属性，会被Form组件自动处理
 * 
 * @param {WinInputProps} props - 组件属性
 * @returns {JSX.Element} - 返回封装后的输入框组件
 */
const WinInput = (props: WinInputProps) => {
  const { children, ...restProps } = props;
  // 直接渲染Input组件，Form组件会自动处理包装
  return <Input {...restProps} >{children}</Input>;
};

export { WinInput };

export default WinInput;
