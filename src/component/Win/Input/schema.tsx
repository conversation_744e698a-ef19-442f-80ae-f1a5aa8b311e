const inputSchema = {
  version: "0.0.1",
  css: "",
  i18n: {},
  componentName: "Input",
  title: "输入框",
  description: "用于文本输入，支持多种样式和功能扩展。",
  docUrl: "https://ant-design.antgroup.com/components/input-cn",
  screenshot: "",
  icon: "",
  keywords: "输入框，文本输入，密码输入，搜索框",
  snippets: [],
  group: "common",
  category: "entry", // 数据录入组件
  priority: 0,
  sort: 0, // 排序
  props: [
    {
      name: "name",
      title: "字段name",
      defaultValue: "",
    },
    {
      name: "placeholder",
      title: "占位提示",
      defaultValue: "",
    },
    {
      name: "type",
      title: "输入类型",
      defaultValue: "text",
    },
    {
      name: "size",
      title: "显示大小",
      defaultValue: "default",
    },
    {
      name: "defaultValue",
      title: "默认值",
      defaultValue: "",
    },
    {
      name: "allowClear",
      title: "清除按钮",
      defaultValue: false,
    },
    {
      name: "addonBefore",
      title: "前缀",
      defaultValue: null,
    },
    {
      name: "addonAfter",
      title: "后缀",
      defaultValue: null,
    },
    {
      name: "prefix",
      title: "图标前缀",
      defaultValue: null,
    },
    {
      name: "suffix",
      title: "图标后缀",
      defaultValue: null,
    },
    {
      name: "disabled",
      title: "禁用状态",
      defaultValue: false,
    },
    {
      name: "maxLength",
      title: "最大长度",
      defaultValue: null,
    },
    {
      name: "showCount",
      title: "显示字数",
      defaultValue: false,
    },
    {
      name: "variant",
      title: "展示边框",
      defaultValue: true,
    },
    {
      name: "status",
      title: "状态",
      defaultValue: null,
    },
    {
      name: "className",
      title: "自定义类",
      defaultValue: "",
    },
    {
      name: "style",
      title: "内联样式",
      defaultValue: {},
    },
    {
      name: "readOnly",
      title: "只读状态",
      defaultValue: false,
    },
    {
      name: "autoComplete",
      title: "自动完成",
      defaultValue: "off",
    },
    {
      name: "spellCheck",
      title: "拼写检查",
      defaultValue: false,
    },
    {
      name: "id",
      title: "输入框 ID",
      defaultValue: null,
    },
    {
      name: "ref",
      title: "DOM 节点",
      defaultValue: null,
    },
  ],
  configure: {
    component: {
      isContainer: false,
      isModal: false,
      description: "输入框，提供丰富的配置项来满足各种场景的需求。",
      device: ["pc", "mobile"],
      nestingRule: {
        childWhitelist: [], // 输入框不接受子元素
        parentWhitelist: [],
      },
    },
    props: [
      {
        name: "name",
        title: "字段name",
        defaultValue: "",
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "placeholder",
        title: "占位提示",
        defaultValue: "",
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "type",
        title: "输入类型",
        defaultValue: "text",
        setter: {
          componentName: "SelectSetter",
          props: {
            style: { width: "100%" },
            options: [
              { value: "text", label: "文本" },
              { value: "password", label: "密码" },
              { value: "textarea", label: "多行文本" },
              { value: "search", label: "搜索" },
            ],
          },
        },
      },
      {
        name: "size",
        title: "显示大小",
        defaultValue: "default",
        setter: {
          componentName: "SelectSetter",
          props: {
            style: { width: "100%" },
            options: [
              { value: "large", label: "大" },
              { value: "default", label: "中" },
              { value: "small", label: "小" },
            ],
          },
        },
      },
      {
        name: "defaultValue",
        title: "默认值",
        defaultValue: "",
        setter: {
          componentName: "StringSetter",
          props: {},
        },
      },
      // {
      //   name: "onChange",
      //   title: "值变化时触发的回调函数",
      //   defaultValue: null,
      //   setter: {
      //     componentName: "FunctionSetter",
      //     props: {},
      //   },
      // },
      // {
      //   name: "onPressEnter",
      //   title: "按下 Enter 键时触发的回调函数",
      //   defaultValue: null,
      //   setter: {
      //     componentName: "FunctionSetter",
      //     props: {},
      //   },
      // },
      {
        name: "allowClear",
        title: "清除按钮",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "addonBefore",
        title: "前缀",
        defaultValue: null,
        setter: {
          componentName: "ReactNodeSetter",
          props: {},
        },
      },
      {
        name: "addonAfter",
        title: "后缀",
        defaultValue: null,
        setter: {
          componentName: "ReactNodeSetter",
          props: {},
        },
      },
      {
        name: "prefix",
        title: "图标前缀",
        defaultValue: null,
        setter: {
          componentName: "ReactNodeSetter",
          props: {},
        },
      },
      {
        name: "suffix",
        title: "图标后缀",
        defaultValue: null,
        setter: {
          componentName: "ReactNodeSetter",
          props: {},
        },
      },
      {
        name: "disabled",
        title: "禁用状态",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "maxLength",
        title: "最大长度",
        defaultValue: null,
        setter: {
          componentName: "NumberSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "showCount",
        title: "显示字数",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "variant",
        title: "边框样式",
        defaultValue: true,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "status",
        title: "状态",
        defaultValue: null,
        setter: {
          componentName: "SelectSetter",
          props: {
            style: { width: "100%" },
            options: [
              { value: "warning", label: "警告" },
              { value: "error", label: "错误" },
            ],
          },
        },
      },
      {
        name: "className",
        title: "自定义类",
        defaultValue: "",
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "style",
        title: "内联样式",
        defaultValue: {},
        setter: {
          componentName: "ObjectSetter",
          props: {},
        },
      },
      {
        name: "readOnly",
        title: "只读状态",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "autoComplete",
        title: "自动完成",
        defaultValue: "off",
        setter: {
          componentName: "SelectSetter",
          props: {
            style: { width: "100%" },
            options: [
              { value: "on", label: "开启" },
              { value: "off", label: "关闭" },
            ],
          },
        },
      },
      {
        name: "spellCheck",
        title: "拼写检查",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "id",
        title: "输入框 ID",
        defaultValue: null,
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "name",
        title: "字段name",
        defaultValue: null,
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "ref",
        title: "DOM 节点",
        defaultValue: null,
        setter: {
          componentName: "ReactNodeSetter",
          props: {},
        },
      },
    ],
  },
};

export { inputSchema };

export default inputSchema;
