/* 时间轴组件配置 */
const meta = {
  componentName: "Timeline",
  version: "0.0.1",
  css: "",
  i18n: {},
  props: {
    mode: {
      type: "string",
      value: "left",
      options: ["left", "right", "alternate"]
    },
    pending: {
      type: "string|ReactNode",
      value: null
    },
    pendingDot: {
      type: "string|ReactNode",
      value: null
    },
    reverse: {
      type: "boolean",
      value: false
    },
    className: [""],
    style: {
      fontSize: 14
    }
  },
  events: {},
  methods: {},
  dataSource: {
    list: [],
    dataHandler: {
      type: "JSFunction",
      value: "function(dataMap) { }"
    }
  }
};

export default meta;