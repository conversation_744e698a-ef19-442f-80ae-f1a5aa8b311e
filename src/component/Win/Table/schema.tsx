const tableSchema = {
  version: "0.0.1",
  css: "",
  i18n: {},
  componentName: "Table",
  title: "表格",
  description: "用于展示数据的表格组件。",
  docUrl: "https://ant-design.antgroup.com/components/table-cn",
  screenshot: "",
  icon: "",
  keywords: "表格，数据，展示，布局",
  snippets: [],
  group: "common",
  category: "display",
  children: [],
  priority: 0,
  sort: 0, // 排序
  props: [
    {
      name: "tableName",
      title: "数据表",
      description: "数据表",
      defaultValue: "",
    },
    {
      name: "columns",
      title: "列配置",
      description: "列的配置，包含每一列的标题、数据索引等",
      defaultValue: [],
    },
    {
      name: "pagination",
      title: "分页",
      description: "是否显示分页",
      defaultValue: true,
    },
    {
      name: "loading",
      title: "加载状态",
      description: "表格是否处于加载状态",
      defaultValue: false,
    },
    {
      name: "rowKey",
      title: "行键",
      description: "用于唯一标识每一行的数据",
      defaultValue: "key",
    },
    {
      name: "className",
      title: "样式类名",
      description: "自定义样式类名",
      defaultValue: "",
    },
    {
      name: "style",
      title: "内联样式",
      description: "自定义内联样式",
      defaultValue: {},
    },
  ],
  configure: {
    component: {
      isContainer: false,
      isModal: false,
      description: "表格组件，用于展示数据。",
      device: ["pc", "mobile"],
      nestingRule: {
        childWhitelist: [], // 表格不接受子元素
        parentWhitelist: [],
      },
    },
    props: [
      {
        name: "tableName",
        title: "数据表",
        defaultValue: "",
        setter: {
          componentName: "StringSetter",
          props: {},
        },
      },
      {
        name: "columns",
        title: "列配置",
        defaultValue: [],
        setter: {
          componentName: "ArraySetter",
          props: {},
        },
      },
      {
        name: "pagination",
        title: "分页",
        defaultValue: true,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "loading",
        title: "加载状态",
        defaultValue: false,
        setter: {
          componentName: "BooleanSetter",
          props: {},
        },
      },
      {
        name: "rowKey",
        title: "行键",
        defaultValue: "key",
        setter: {
          componentName: "StringSetter",
          props: {},
        },
      },
      {
        name: "className",
        title: "样式类名",
        defaultValue: "",
        setter: {
          componentName: "StringSetter",
          props: {
            style: { width: "100%" },
          },
        },
      },
      {
        name: "style",
        title: "内联样式",
        defaultValue: {},
        setter: {
          componentName: "ObjectSetter",
          props: {},
        },
      },
    ],
  },
};

export { tableSchema };
export default tableSchema;
