import { Table } from "antd";
import React, { useEffect, useState } from "react";
import "./index.less";
import { getClient } from "@/client";

interface WinTableProps {
  tableName: string;
  columns?: any[];
  dataSource?: any[];
  query?: {
    current?: number;
    pageSize?: number;
    sortField?: string;
    sortOrder?: 'ascend' | 'descend';
    [key: string]: any;
  };
  [key: string]: any;
}

/* 表格 */
const WinTable = (props: WinTableProps) => {
  const { 
    children, 
    tableName,
    columns: columnsProps, 
    dataSource: dataSourceProps, 
    query = {},
    ...resetProps 
  } = props;

  const [columns, setColumns] = useState(columnsProps);
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: query.current || 1,
    pageSize: query.pageSize || 10,
    total: 0,
  });

  const client = getClient();

  useEffect(() => {
    getColumns();
    getData();
  }, [tableName]);

  useEffect(() => {
    getData();
  }, [pagination.current, pagination.pageSize]);

  const getColumns = async () => {
    try {
      const columns = await client.meta.getFieldsMeta(tableName);
      const cols = columns.map(column => ({
        title: column.label || column.field_name,
        dataIndex: column.field_name,
        key: column.field_name,
        ellipsis: true,
        width: column.width,
        fixed: column.fixed,
        align: column.align || 'left',
        render: column.render,
      }));
      setColumns(cols);
    } catch (error) {
      console.error('获取列配置失败:', error);
    }
  };

  const getData = async () => {
    if (!tableName) return;
    
    setLoading(true);
    try {
      const result = await client.database.readList(tableName, {
        ...query,
        current: pagination.current,
        pageSize: pagination.pageSize,
      });
      
      setDataSource(result.data);
      setPagination(prev => ({
        ...prev,
        total: result.total,
      }));
    } catch (error) {
      console.error('获取数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    const newQuery = {
      ...query,
      current: pagination.current,
      pageSize: pagination.pageSize,
      sortField: sorter.field,
      sortOrder: sorter.order,
    };
    
    setPagination({
      current: pagination.current,
      pageSize: pagination.pageSize,
      total: pagination.total,
    });
    
    // 触发父组件的查询回调
    if (props.onChange) {
      props.onChange(newQuery);
    }
  };

  return (
    <Table 
      {...resetProps}
      rowKey={(record) => record?.id}
      columns={columns}
      dataSource={dataSource}
      loading={loading}
      pagination={{
        ...pagination,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total) => `共 ${total} 条`,
      }}
      onChange={handleTableChange}
    >
      {children}
    </Table>
  );
};

export { WinTable };
export default WinTable;
