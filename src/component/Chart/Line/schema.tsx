const lineSchema = {
  version: "0.0.1",
  css: "",
  i18n: {},
  componentName: "Line",
  title: "折线图",
  description: "用于展示内容和功能操作的容器。",
  docUrl: "https://ant-design.antgroup.com/components/card-cn",
  screenshot: "",
  icon: "",
  keywords: "图表，折线图",
  snippets: [],
  group: "chart",
  category: "line", // 数据展示组件
  priority: 0,
  children: [],
  props: [
    {
      name: "name",
      title: "标题",
      defaultValue: "Access From",
    },
    {
      name: "width",
      title: "宽度",
      defaultValue: 600,
    },
    {
      name: "height",
      title: "高度",
      defaultValue: 400,
    },
  ],
  configure: {
    component: {
      isContainer: false,
      isModal: false,
      description: "饼状图",
      device: ["pc", "mobile"],
      nestingRule: {
        childWhitelist: [], // 卡片可以接受任意子元素
        parentWhitelist: [],
      },
    },
    props: [
      {
        name: "name",
        title: "标题",
        defaultValue: "Access From",
        setter: {
          componentName: "StringSetter",
          props: {},
        },
      },
      {
        name: "width",
        title: "宽度",
        defaultValue: 600,
        setter: {
          componentName: "NumberSetter",
          props: {
            style: {
              width: "100%",
            },
          },
        },
      },
      {
        name: "height",
        title: "高度",
        defaultValue: 400,
        setter: {
          componentName: "NumberSetter",
          props: {
            style: {
              width: "100%",
            },
          },
        },
      },
    ],
  },
};

export { lineSchema };

export default lineSchema;
