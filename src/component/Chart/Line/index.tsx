import { useRef, useEffect } from "react";
import * as echarts from "echarts/core";
import { GridComponent } from "echarts/components";
import { BarChart } from "echarts/charts";
import { CanvasRenderer } from "echarts/renderers";
import "./index.less";

echarts.use([<PERSON><PERSON><PERSON>ompo<PERSON>, <PERSON><PERSON><PERSON>, CanvasRenderer]);

/* Line */
const WinLine = (props) => {
  const { name, width, height, children, ...restProps } = props;
  const refChart = useRef(null);
  const resizeObserverRef = useRef(null);

  useEffect(() => {
    var chartDom = refChart.current;
    var myChart = echarts.getInstanceByDom(chartDom) || echarts.init(chartDom);
    var option;

    option = {
      xAxis: {
        type: "category",
        data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
      },
      yAxis: {
        type: "value",
      },
      series: [
        {
          data: [120, 200, 150, 80, 70, 110, 130],
          type: "line",
        },
      ],
    };

    option && myChart.setOption(option);

    // 创建 ResizeObserver 实例
    resizeObserverRef.current = new ResizeObserver(() => {
      myChart.resize();
    });

    // 开始观察元素大小变化
    resizeObserverRef.current.observe(chartDom);

    // 组件卸载时清除 ResizeObserver 和 ECharts 实例
    return () => {
      resizeObserverRef.current.disconnect();
      myChart.dispose();
    };
  }, [props]);

  return (
    <div
      ref={refChart}
      {...restProps}
      style={{ width: width, height: height, resize: "both" }}
    ></div>
  );
};

export { WinLine };

export default WinLine;
