import { useRef, useEffect } from "react";
import * as echarts from "echarts/core";
import {
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  LegendComponent,
} from "echarts/components";
import { Funnel<PERSON>hart } from "echarts/charts";
import { CanvasRenderer } from "echarts/renderers";
import "./index.less";

echarts.use([
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  Legend<PERSON>omponent,
  FunnelChart,
  CanvasRenderer,
]);

/* Funnel */
const WinFunnel = (props) => {
  const { name, children, ...restProps } = props;
  const refChart = useRef(null);

  useEffect(() => {
    var chartDom = refChart.current;
    var myChart = echarts.getInstanceByDom(chartDom) || echarts.init(chartDom);
    var option;

    option = {
      title: {
        text: "Funnel",
      },
      tooltip: {
        trigger: "item",
        formatter: "{a} <br/>{b} : {c}%",
      },
      toolbox: {
        feature: {
          dataView: { readOnly: false },
          restore: {},
          saveAsImage: {},
        },
      },
      legend: {
        data: ["Show", "Click", "Visit", "Inquiry", "Order"],
      },
      series: [
        {
          name: "Funnel",
          type: "funnel",
          left: "10%",
          top: 60,
          bottom: 60,
          width: "80%",
          min: 0,
          max: 100,
          minSize: "0%",
          maxSize: "100%",
          sort: "descending",
          gap: 2,
          label: {
            show: true,
            position: "inside",
          },
          labelLine: {
            length: 10,
            lineStyle: {
              width: 1,
              type: "solid",
            },
          },
          itemStyle: {
            borderColor: "#fff",
            borderWidth: 1,
          },
          emphasis: {
            label: {
              fontSize: 20,
            },
          },
          data: [
            { value: 60, name: "Visit" },
            { value: 40, name: "Inquiry" },
            { value: 20, name: "Order" },
            { value: 80, name: "Click" },
            { value: 100, name: "Show" },
          ],
        },
      ],
    };

    option && myChart.setOption(option);
  }, [props]);

  return (
    <div
      ref={refChart}
      {...restProps}
      style={{ width: "600px", height: "400px" }}
    ></div>
  );
};

export { WinFunnel };

export default WinFunnel;
