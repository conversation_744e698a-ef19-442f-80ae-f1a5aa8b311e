import { useEffect, useRef, useState } from "react";
import ace from "ace-builds";
import "ace-builds/src-noconflict/theme-monokai";
import "ace-builds/src-noconflict/theme-tomorrow";
import "ace-builds/src-noconflict/mode-sql";
import "ace-builds/src-noconflict/mode-javascript";
import "ace-builds/src-noconflict/mode-json";
import "ace-builds/src-noconflict/mode-css";
import "ace-builds/src-noconflict/mode-jsx";
import "ace-builds/src-noconflict/mode-tsx";
import "ace-builds/src-noconflict/ext-language_tools";
import "ace-builds/src-noconflict/ext-beautify";
import "./index.css";

import useStore from "@/store/index";

const Code = ({
  mode = "javascript",
  initialValue,
  onChange,
  options,
  style,
}) => {
  const editorRef = useRef(null);
  const [aceEditor, setAceEditor] = useState(null);
  const antdThemeMode = useStore((state) => state.themeMode);

  useEffect(() => {
    if (editorRef.current) {
      const editor = ace.edit(editorRef.current);

      if (antdThemeMode === "dark") {
        editor.setTheme("ace/theme/monokai");
      } else {
        editor.setTheme("ace/theme/tomorrow");
      }

      if (!["javascript", "jsx", "tsx", "json", "sql", "css"].includes(mode)) {
        throw new Error("ace editor mode need oneof javascript | json | sql ");
      } else {
        editor.session.setMode(`ace/mode/${mode}`);
      }

      // 设置其他编辑器选项
      editor.setOptions({
        useWorker: false, // fix Failed to execute 'importScripts' on 'WorkerGlobalScope'
        enableBasicAutocompletion: true,
        enableLiveAutocompletion: true,
        showLineNumbers: true,
        tabSize: 2,
        ...options,
      });

      editor.setValue(initialValue, -1); // -1 表示不触发 change 事件

      // 添加事件监听器等
      editor.on("change", (delta) => {
        const content = editor.getValue();
        onChange && onChange(delta, content);
      });
      setAceEditor(ace);
    }
  }, [antdThemeMode, initialValue]);

  return <div ref={editorRef} className="win-code" style={style}></div>;
};

export default Code;
