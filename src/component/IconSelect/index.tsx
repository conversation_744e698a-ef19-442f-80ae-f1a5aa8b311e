import React, { useState, useMemo } from 'react';
import { Select, Modal, Tabs, Image, Input } from 'antd';
import type { TabsProps } from 'antd';
import * as Icons from '@ant-design/icons';
import type { IconComponentProps } from '@ant-design/icons/lib/components/Icon';
import type { ForwardRefExoticComponent, RefAttributes } from 'react';
import './index.css';

type IconSource = 'antd' | 'url' | 'custom';

interface IconSelectProps {
  value?: string;
  onChange?: (value: string, iconSource: IconSource) => void;
  iconSource?: IconSource;
  customIcons?: { [key: string]: React.ComponentType<IconComponentProps> };
}

// 判断是否为 ForwardRefExoticComponent
function isForwardRefComponent(
  comp: any
): comp is ForwardRefExoticComponent<IconComponentProps & RefAttributes<HTMLSpanElement>> {
  return (
    typeof comp === 'object' &&
    comp !== null &&
    '$$typeof' in comp &&
    comp.$$typeof.toString() === 'Symbol(react.forward_ref)'
  );
}

const IconSelect: React.FC<IconSelectProps> = ({
  value,
  onChange,
  iconSource = 'antd',
  customIcons = {},
}) => {
  const [searchValue, setSearchValue] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<IconSource>(iconSource);
  const [customUrl, setCustomUrl] = useState('');

  // 只保留 Outlined/Filled/TwoTone 结尾的图标，并且是 ForwardRefExoticComponent
  const antdIcons = useMemo(() => {
    return Object.entries(Icons)
      .filter(([name, IconComponent]) => {
        return (
          (name.endsWith('Outlined') || name.endsWith('Filled') || name.endsWith('TwoTone')) &&
          isForwardRefComponent(IconComponent)
        );
      })
      .map(([name]) => name);
  }, []);

  // 过滤图标
  const filteredIcons = useMemo(() => {
    if (!searchValue) return antdIcons;
    const keywords = searchValue.toLowerCase().split(/\s+/).filter(Boolean);
    return antdIcons.filter(name => {
      const lowerName = name.toLowerCase();
      return keywords.every(keyword => lowerName.includes(keyword));
    });
  }, [antdIcons, searchValue]);

  // 渲染图标预览
  const renderIconPreview = (iconName: string) => {
    if (!iconName) return null;

    if (iconName.startsWith('http')) {
      return <Image src={iconName} alt="icon" width={24} height={24} />;
    }

    if (customIcons[iconName]) {
      const IconComponent = customIcons[iconName];
      return <IconComponent />;
    }

    const IconComponent = Icons[iconName as keyof typeof Icons];
    if (isForwardRefComponent(IconComponent)) {
      return <IconComponent />;
    }

    return null;
  };

  const items: TabsProps['items'] = [
    {
      key: 'antd',
      label: 'Ant Design 图标',
      children: (
        <div style={{ maxHeight: 400, overflow: 'auto' }}>
          <div style={{ marginBottom: 16 }}>
            <Input
              style={{ width: '100%' }}
              placeholder="搜索图标"
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              allowClear
            />
          </div>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(6, 1fr)', gap: 8 }}>
            {filteredIcons.map((name) => {
              const IconComponent = Icons[name as keyof typeof Icons];
              if (!isForwardRefComponent(IconComponent)) {
                return null;
              }
              return (
                <div
                  key={name}
                  className="icon-item"
                  onClick={() => {
                    onChange?.(name, 'antd');
                    setIsModalOpen(false);
                  }}
                >
                  <IconComponent />
                  <span style={{ fontSize: 12, marginTop: 4 }}>{name}</span>
                </div>
              );
            })}
          </div>
        </div>
      ),
    },
    {
      key: 'url',
      label: '自定义 URL',
      children: (
        <div style={{ padding: 16 }}>
          <Select
            style={{ width: '100%', marginBottom: 16 }}
            placeholder="输入图标 URL"
            value={customUrl}
            onChange={setCustomUrl}
            showSearch
            allowClear
          />
          <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
            <button
              onClick={() => {
                if (customUrl) {
                  onChange?.(customUrl, 'url');
                  setIsModalOpen(false);
                }
              }}
              disabled={!customUrl}
            >
              确认
            </button>
          </div>
        </div>
      ),
    },
    {
      key: 'custom',
      label: '自定义图标',
      children: (
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(6, 1fr)', gap: 8, padding: 16 }}>
          {Object.entries(customIcons).map(([name, IconComponent]) => (
            <div
              key={name}
              className="icon-item"
              onClick={() => {
                onChange?.(name, 'custom');
                setIsModalOpen(false);
              }}
            >
              <IconComponent />
              <span style={{ fontSize: 12, marginTop: 4 }}>{name}</span>
            </div>
          ))}
        </div>
      ),
    },
  ];

  return (
    <>
      <div
        style={{
          display: 'inline-flex',
          alignItems: 'center',
          padding: '4px 8px',
          border: '1px solid #d9d9d9',
          borderRadius: 6,
          cursor: 'pointer',
        }}
        onClick={() => setIsModalOpen(true)}
      >
        {value ? (
          renderIconPreview(value)
        ) : (
          <span style={{ color: '#999' }}>选择图标</span>
        )}
      </div>

      <Modal
        title="选择图标"
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        footer={null}
        width={600}
      >
        <Tabs
          activeKey={activeTab}
          onChange={(key) => setActiveTab(key as IconSource)}
          items={items}
        />
      </Modal>
    </>
  );
};

export default IconSelect; 