import { useState, useEffect } from "react";

import { loadScript } from "@/utils";

const { VITE_PRIVATE_NPM_HOST } = import.meta.env;

const DynamicUMDComponent = ({
  src = `${VITE_PRIVATE_NPM_HOST}/assets/remote.umd.js`,
  library = "remote",
  name = "Input",
  ...rest
}) => {
  const [RemoteComponent, setRemoteComponent] = useState(null);

  useEffect(() => {
    const loadRemoteComponent = async () => {
      try {
        // 加载远程 UMD 脚本
        await loadScript(src);
        const component = window[library]; // umd 远程组件会挂载在全局对象 window.remote 上
        setRemoteComponent(component);
      } catch (error) {
        console.error("Failed to load remote component:", error);
      }
    };

    loadRemoteComponent();
  }, []);

  const Component = RemoteComponent && RemoteComponent[name];

  if (Component) {
    return <Component {...rest} />;
  } else {
    return <span>Loading remote component...</span>;
  }
};

const DynamicESComponent = ({
  src = "http://localhost/remote.es.js",
  name = "Button",
  ...rest
}) => {
  const [RemoteComponent, setRemoteComponent] = useState(null);

  useEffect(() => {
    const loadRemoteComponent = async () => {
      try {
        // 动态导入远程 ES 模块
        const module = await import(/* @vite-ignore */ src);
        setRemoteComponent(module); // setRemoteComponent(module[name]); // 这里不能获取 子组件，渲染不出来
      } catch (error) {
        console.error("Failed to load remote component:", error);
      }
    };

    loadRemoteComponent();
  }, []);

  const Component = RemoteComponent && RemoteComponent[name];

  if (Component) {
    return <Component {...rest} />;
  } else {
    return <span>Loading remote component...</span>;
  }
};

export { DynamicUMDComponent, DynamicESComponent };
