import { useRef, useEffect } from "react";
import * as echarts from "echarts/core";
import { TooltipComponent } from "echarts/components";
import { GaugeChart } from "echarts/charts";
import { CanvasRenderer } from "echarts/renderers";
import "./index.less";

echarts.use([TooltipComponent, <PERSON><PERSON><PERSON><PERSON>hart, CanvasRenderer]);

/* Gauge */
const WinGauge = (props) => {
  const { name, children, ...restProps } = props;
  const refChart = useRef(null);

  useEffect(() => {
    var chartDom = refChart.current;
    var myChart = echarts.getInstanceByDom(chartDom) || echarts.init(chartDom);
    var option;

    option = {
      tooltip: {
        formatter: "{a} <br/>{b} : {c}%",
      },
      series: [
        {
          name: "Pressure",
          type: "gauge",
          detail: {
            formatter: "{value}",
          },
          data: [
            {
              value: 50,
              name: "SCORE",
            },
          ],
        },
      ],
    };

    option && myChart.setOption(option);
  }, [props]);

  return (
    <div
      ref={refChart}
      {...restProps}
      style={{ width: "600px", height: "400px" }}
    ></div>
  );
};

export { WinGauge };

export default WinGauge;
