import { useRef, useEffect } from "react";
import * as echarts from "echarts/core";
import { GridComponent } from "echarts/components";
import { LineChart } from "echarts/charts";
import { UniversalTransition } from "echarts/features";
import { CanvasRenderer } from "echarts/renderers";
import "./index.less";

echarts.use([GridComponent, LineChart, CanvasRenderer, UniversalTransition]);

/* Bar */
const WinBar = (props) => {
  const { name, children, ...restProps } = props;
  const refChart = useRef(null);

  useEffect(() => {
    var chartDom = refChart.current;
    var myChart = echarts.getInstanceByDom(chartDom) || echarts.init(chartDom);
    var option;

    option = {
      xAxis: {
        type: "category",
        data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
      },
      yAxis: {
        type: "value",
      },
      series: [
        {
          data: [150, 230, 224, 218, 135, 147, 260],
          type: "bar",
        },
      ],
    };

    option && myChart.setOption(option);
  }, [props]);

  return (
    <div
      ref={refChart}
      {...restProps}
      style={{ width: "600px", height: "400px" }}
    ></div>
  );
};

export { WinBar };

export default WinBar;
