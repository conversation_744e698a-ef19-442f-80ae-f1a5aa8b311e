const barSchema = {
  version: "0.0.1",
  css: "",
  i18n: {},
  componentName: "Bar",
  title: "柱状图",
  description: "用于展示内容和功能操作的容器。",
  docUrl: "https://ant-design.antgroup.com/components/card-cn",
  screenshot: "",
  icon: "",
  keywords: "图表，柱状图",
  snippets: [],
  group: "twin",
  category: "bar", // 数据展示组件
  priority: 0,
  children: [],
  props: [
    {
      name: "name",
      title: "标题",
      defaultValue: "Access From",
    },
  ],
  configure: {
    component: {
      isContainer: false,
      isModal: false,
      description: "饼状图",
      device: ["pc", "mobile"],
      nestingRule: {
        childWhitelist: [], // 卡片可以接受任意子元素
        parentWhitelist: [],
      },
    },
    props: [
      {
        name: "name",
        title: "标题",
        defaultValue: "Access From",
        setter: {
          componentName: "StringSetter",
          props: {},
        },
      },
    ],
  },
};

export { barSchema };

export default barSchema;
