// 路由表-创建动态路由
import { createBrowserRouter } from "react-router-dom";
import { Navigate } from "react-router-dom";

const basename = import.meta.env.BASE_URL;

/* 公共页 */
import Base from "@/component/Layout/base";

/* SaaS布局 */
import SaaSLayout from "@/component/Layout/saas";

/* 公共页 */
import PageLayout from "@/component/Layout";

/* 控制台 */
import Dashboard from "@/page/dashboard";

/* 菜单 */
import Menu from "@/page/menu";

/* 权限管理 */
import Permission from "@/page/permission";

/* 设置页 */
import Setting from "@/page/setting";

/* 权限管理 */
import PageList from "@/page/list";

/* 设计页 */
import Design from "@/page/design";

/* 预览页 */
import Preview from "@/page/preview";

/* 运行页 */
import Runtime from "@/page/runtime";

/* 测试页 */
import Test from "@/page/test";

/* 结果页 */
import Result403 from "@/page/result/403";
import Result404 from "@/page/result/404";
import Result500 from "@/page/result/500";

/* 账户页 */
import Login from "@/page/account/login";
import Register from "@/page/account/register";
import Forgot from "@/page/account/forgot-password";
import Profile from "@/page/account/profile";

/* SaaS页面 */
import SaaSDashboard from "@/page/saas/dashboard";
import SaaSApps from "@/page/saas/apps";


const routes = [
  {
    path: "/",
    element: <Navigate to="/saas/dashboard" replace />,
  },
  {
    path: "/saas",
    element: <SaaSLayout />,
    children: [
      {
        index: true,
        element: <Navigate to="dashboard" replace />,
      },
      {
        path: "dashboard",
        element: <SaaSDashboard />,
        meta: {
          label: "数据分析",
          key: "dashboard",
          requiresAuth: true,
        },
      },
      {
        path: "apps",
        element: <SaaSApps />,
        meta: {
          label: "应用管理",
          key: "apps",
          requiresAuth: true,
        },
      },
    ],
  },
  {
    path: "/app",
    element: <Base />,
    children: [
      {
        index: true,
        element: <Navigate to="/saas/dashboard" replace />,
      },
      {
        path: "dashboard",
        element: <Dashboard />,
        meta: {
          label: "控制台",
          key: "login",
          requiresAuth: true,
          hide_in_menu: true,
        },
      },
      {
        path: "page",
        meta: {
          label: "页面管理",
          key: "list",
          hide_in_menu: false,
        },
        children: [
          { index: true, element: <PageList />, meta: { label: '页面列表' } },
          { path: "list", element: <PageList />, meta: { label: '页面列表' } }
        ],
      },
      {
        path: "menu",
        element: <Menu />,
        meta: { label: '导航管理', requiresAuth: true }
      },
      {
        path: "permission",
        element: <Permission />,
        meta: { label: '权限管理', requiresAuth: true },
        children: [
          { index: true, element: <Test />, meta: { label: '角色列表' } },
          { path: "roles/:id", element: <Test />, meta: { label: '编辑角色' } },
          { path: "users", element: <Test />, meta: { label: '用户列表' } }
        ]
      },
      {
        path: "setting",
        element: <Setting />,
        meta: { label: '应用设置', requiresAuth: true }
      },
      {
        path: "profile",
        element: <Profile />,
        meta: {
          label: "个人中心",
          key: "profile",
          hide_in_menu: true,
        },
      },
      {
        path: "404",
        element: <Result404 />,
        meta: { label: '页面不存在' }
      },
      {
        path: "403",
        element: <Result403 />,
        meta: { label: '权限不足' }
      },
      {
        path: "500",
        element: <Result500 />,
        meta: { label: '服务器错误' }
      }
    ],
  },
  {
    path: "/app/:appId/page/:pageId",
    element: <PageLayout />,
    children: [
      { index: true, element: <Design />, meta: { label: '设计页' } },
      { path: "design", element: <Design />, meta: { label: '设计页' } },
      { path: "preview", element: <Preview />, meta: { label: '预览页' } },
      { path: "runtime", element: <Runtime />, meta: { label: '运行页' } },
      { path: "test", element: <Runtime />, meta: { label: '测试页' } }
    ]
  },
  {
    path: "login",
    element: <Login />,
    meta: {
      label: "用户登录",
      key: "login",
      hide_in_menu: true,
    },
  },
  {
    path: "register",
    element: <Register />,
    meta: {
      label: "用户注册",
      key: "register",
      hide_in_menu: true,
    },
  },
  {
    path: "forgot-password",
    element: <Forgot />,
    meta: {
      label: "忘记密码",
      key: "forgot-password",
      hide_in_menu: true,
    },
  },
  {
    path: "*",
    element: <Result404 />,
    meta: {
      label: "404",
      key: "404",
      hide_in_menu: true,
    },
  },
];

const router = createBrowserRouter(routes, {
  basename,
});

export default router;
