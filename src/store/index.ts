import { create } from "zustand";
import { produce } from "immer";
import { Storage } from "@/utils/storage";
import { getComponentSchema } from "@/utils/schema";
import { getObjectById } from "@/utils/index";

const themeMode = Storage.getItem("themeMode") || "light";

type Store = {
  count: number;
  loading: boolean;
  jsonSchema: object;
  selectedId: string;
  selectedSchema: object;
  selectedSomeSchema: object;
  themeMode: any;
  clientMode: string;
  sideExtra: {
    show: boolean;
    current: {
      key: string;
      label: string;
    };
  };
  rightActiveKey: string;
  history: {
    queue: object[];
    index: number;
  };
  position: String;
  params: object;
  inc: () => void;
  updateLoading: (loading: boolean) => void;
  updateSelectedId: (id: string) => void;
  updateJsonSchema: (schema: object) => void;
  toggleTheme: (mode: any) => void;
  setClientMode: (mode: string) => void;
  undo: () => void;
  redo: () => void;
  updatePosition: (key: string) => void;
  updateParams: (params: object) => void;
  updateSideExtra: (sideExtra: {
    show: boolean;
    current: { key: string; label: string };
  }) => void;
  updateRightActiveKey: (key: string) => void;
};

const useStore = create<Store>()((set) => ({
  count: 1,
  loading: false, // 全局 loading
  jsonSchema: [],
  selectedId: "", // 当前选中的 dnd 组件id
  selectedSchema: {}, // 当前选中的组件的 Schema 原始数据-不包含uuid
  selectedSomeSchema: {}, // 上面简化后 Schema 渲染数据-包含uuid
  themeMode: themeMode, // 主题
  clientMode: "desk", // 设计-预览客户端类型: 默认桌面端
  sideExtra: {
    show: false,
    current: {
      key: "",
      label: "",
    },
  },
  rightActiveKey: "props",
  history: { queue: [], index: -1 }, // 历史记录快照队列和当前位置 index = -1 是初始化时操作过 updateJsonSchema
  position: "", // 放置参考位置
  params: {
    // 全局处理路由参数并存储（对外提供方法）
    tenantId: "",
    projectId: "",
    appId: "",
    formId: "",
    pageId: "",
    templateId: "",
    componentId: "",
    fieldId: "",
    dataId: "",
    typeId: "",
    viewId: "",
    mode: "design", // 设计模式-design / preview
  },
  inc: () => set((state) => ({ count: state.count + 1 })),
  updateLoading: (loading) => set(() => ({ loading })),
  updateJsonSchema: (schema) =>
    set((state) =>
      produce(state, (draft) => {
        draft.jsonSchema = schema;
        // 限制历史记录队列长度，避免内存泄漏
        const MAX_HISTORY = 50;
        const TRIM_TO = 25;
        
        if (draft.history.queue.length >= MAX_HISTORY) {
          draft.history.queue = draft.history.queue.slice(-TRIM_TO);
          draft.history.index = Math.min(draft.history.index, TRIM_TO - 1);
        }
        
        // 避免重复添加相同的 schema
        const lastSchema = draft.history.queue[draft.history.queue.length - 1];
        if (JSON.stringify(lastSchema) !== JSON.stringify(schema)) {
          draft.history.queue.push(schema);
          draft.history.index += 1;
        }
      })
    ),
  updateSelectedId: (id) =>
    set((state) => {
      const someSchema = getObjectById(state.jsonSchema, id);
      const selectedSchema = getComponentSchema(someSchema?.componentName);
      return {
        selectedId: id,
        selectedSchema: selectedSchema || {},
        selectedSomeSchema: someSchema || {},
      };
    }),
  toggleTheme: (mode) => set(() => ({ themeMode: mode })),
  setClientMode: (client) => set(() => ({ clientMode: client })),
  undo: () =>
    set((state) => {
      if (state.history.index === 0) return state;
      return produce(state, (draft) => {
        draft.jsonSchema = state.history.queue[state.history.index - 1];
        draft.history.index -= 1;
      });
    }),
  redo: () =>
    set((state) => {
      if (state.history.index === state.history.queue.length - 1) return state;
      return produce(state, (draft) => {
        draft.jsonSchema = state.history.queue[state.history.index + 1];
        draft.history.index += 1;
      });
    }),
  updatePosition: (position) => set(() => ({ position: position })),
  updateParams: (params) =>
    set(
      produce((state) => {
        state.params = params;
      })
    ),
  updateSideExtra: (sideExtra) =>
    set(
      produce((state) => {
        state.sideExtra = sideExtra;
      })
    ),
  updateRightActiveKey: (key) => set(() => ({ rightActiveKey: key })),
}));

export default useStore;
