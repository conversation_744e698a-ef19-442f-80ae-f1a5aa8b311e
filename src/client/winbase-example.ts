/**
 * Winbase Client Usage Examples
 * 
 * This file demonstrates how to use the Winbase client with your custom RESTful API.
 */

import { createWinbaseClient, createWinbaseExtensions } from './winbase';

// Configuration for your Winbase client
const config = {
  baseURL: 'https://your-api.com',
  appUid: 'your-app-uid',
  token: 'optional-bearer-token', // Optional, will use Storage.getItem('token') if not provided
  timeout: 6000 // Optional, defaults to 6000ms
};

// Create the main client (compatible with BaaSClient interface)
const client = createWinbaseClient(config);

// Create extensions client for additional functionality
const extensions = createWinbaseExtensions(config);

// Example usage:

async function examples() {
  try {
    // ===== AUTHENTICATION =====
    
    // Sign in
    const authResult = await client.auth.signIn('<EMAIL>', 'password');
    console.log('Signed in:', authResult);

    // Get current user
    const currentUser = await client.auth.getCurrentUser();
    console.log('Current user:', currentUser);

    // ===== BASIC CRUD OPERATIONS =====
    
    // Create a new record
    const newUser = await client.database.create('users', {
      name: 'John Doe',
      email: '<EMAIL>',
      age: 30
    });
    console.log('Created user:', newUser);

    // Read all records with query
    const users = await client.database.read('users', {
      status: 'active'
    });
    console.log('All users:', users);

    // Read paginated list
    const usersList = await client.database.readList('users', {
      current: 1,
      pageSize: 10,
      sortField: 'created_at',
      sortOrder: 'descend',
      name: 'John' // Search filter
    });
    console.log('Paginated users:', usersList);

    // Read by unique key (UID)
    const userByUid = await client.database.readByUniqueKey('users', 'uid', 'some-uid');
    console.log('User by UID:', userByUid);

    // Update a record
    const updatedUser = await client.database.update('users', '123', {
      name: 'Jane Doe',
      age: 31
    });
    console.log('Updated user:', updatedUser);

    // Update by unique key
    const updatedByUid = await client.database.updateByUniqueKey('users', 'email', '<EMAIL>', {
      status: 'inactive'
    });
    console.log('Updated by email:', updatedByUid);

    // Delete a record
    await client.database.delete('users', '123');
    console.log('User deleted');

    // Delete by unique key
    await client.database.deleteByUniqueKey('users', 'email', '<EMAIL>');
    console.log('User deleted by email');

    // Count records
    const userCount = await client.database.count('users', {
      status: 'active'
    });
    console.log('Active users count:', userCount);

    // Batch update
    await client.database.batchUpdate('users', [
      { where: { id: 1 }, data: { status: 'active' } },
      { where: { id: 2 }, data: { status: 'inactive' } }
    ]);
    console.log('Batch update completed');

    // ===== EXTENDED FUNCTIONALITY =====
    
    // Find by ID (using extensions)
    const userById = await extensions.findById('users', '123');
    console.log('User by ID:', userById);

    // Find unique with custom conditions
    const uniqueUser = await extensions.findUnique('users', {
      email: '<EMAIL>',
      status: 'active'
    });
    console.log('Unique user:', uniqueUser);

    // Tree data operations
    const treeData = await extensions.findTree('categories', {
      parentId: null // Get root categories
    });
    console.log('Tree data:', treeData);

    // Tree pagination
    const treePaginated = await extensions.findTreePaginated('categories', {
      current: 1,
      pageSize: 10,
      parentId: 'root'
    });
    console.log('Tree paginated:', treePaginated);

    // ===== STORAGE OPERATIONS =====
    
    // Upload file
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    if (fileInput?.files?.[0]) {
      const uploadResult = await client.storage.upload('documents', fileInput.files[0], 'uploads/');
      console.log('File uploaded:', uploadResult);
    }

    // Get public URL
    const publicUrl = client.storage.getPublicUrl('documents', 'uploads/file.pdf');
    console.log('Public URL:', publicUrl);

    // Download file
    const fileBlob = await client.storage.download('documents', 'uploads/file.pdf');
    console.log('Downloaded file:', fileBlob);

    // Delete file
    await client.storage.delete('documents', 'uploads/file.pdf');
    console.log('File deleted');

    // ===== METADATA OPERATIONS =====
    
    // Get table field metadata
    const fieldsMeta = await client.meta.getFieldsMeta('users');
    console.log('Fields metadata:', fieldsMeta);

    // ===== AUTHENTICATION STATE MANAGEMENT =====
    
    // Listen to auth state changes
    const unsubscribe = client.auth.onAuthStateChange((user) => {
      if (user) {
        console.log('User signed in:', user);
      } else {
        console.log('User signed out');
      }
    });

    // Later, unsubscribe from auth state changes
    // unsubscribe();

    // Sign out
    await client.auth.signOut();
    console.log('Signed out');

  } catch (error) {
    console.error('Error:', error);
  }
}

// Export for use in other files
export { client, extensions, examples };

// Environment variables you need to set:
// VITE_BAAS_PLATFORM=winbase
// VITE_BAAS_URL=https://your-api.com
// VITE_BAAS_APP_UID=your-app-uid
// VITE_BAAS_TOKEN=optional-token (if you want to set a default token)
