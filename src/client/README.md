# BaaS 客户端使用说明

## 简介

这是一个统一的 BaaS（Backend as a Service）客户端封装，支持 Supabase、Appwrite 和 Winbase 三个平台。通过环境变量配置可以轻松切换不同的后端服务。

### 支持的平台

- **Supabase**: 开源的 Firebase 替代方案
- **Appwrite**: 开源的后端即服务平台
- **Winbase**: 自定义 RESTful API 客户端（类似 Supabase/Appwrite 的接口风格）

## 安装依赖

```bash
npm install @supabase/supabase-js appwrite axios --legacy-peer-deps
```

## 环境配置

1. 复制环境变量示例文件：
```bash
cp .env.example .env
```

2. 配置环境变量：
```env
# 选择 BaaS 平台 (supabase, appwrite, 或 winbase)
VITE_BAAS_PLATFORM=winbase

# Supabase 配置
VITE_BAAS_URL=your-supabase-url
VITE_BAAS_ANON_KEY=your-supabase-anon-key

# Appwrite 配置
VITE_APPWRITE_ENDPOINT=your-appwrite-endpoint
VITE_APPWRITE_PROJECT_ID=your-appwrite-project-id

# Winbase 配置 (自定义 RESTful API)
VITE_BAAS_URL=https://your-api.com
VITE_BAAS_APP_UID=your-app-uid
VITE_BAAS_TOKEN=optional-bearer-token
```

## 使用方法

### 1. 初始化客户端

在应用入口文件（如 `main.tsx`）中初始化客户端：

```typescript
import { initClient } from '@/client';

// 初始化客户端
initClient();
```

### 2. 使用客户端

有两种方式获取客户端实例：

#### 方式一：使用函数获取（推荐）

```typescript
import { getClient } from '@/client';

// 获取客户端实例
const client = getClient();
```

#### 方式二：直接使用导出变量

```typescript
import { baas } from '@/client';

// 直接使用 baas.client
await baas.client.auth.signIn(email, password);
```

### 3. 认证功能

```typescript
// 登录
await client.auth.signIn(email, password);

// 注册
await client.auth.signUp(email, password);

// 登出
await client.auth.signOut();

// 获取当前用户
const user = await client.auth.getCurrentUser();

// 监听认证状态变化
const unsubscribe = client.auth.onAuthStateChange((user) => {
  console.log('用户状态变化:', user);
});

// 取消监听
unsubscribe();
```

### 4. 数据库操作

```typescript
// 创建记录
const newRecord = await client.database.create('users', {
  name: '张三',
  email: '<EMAIL>'
});

// 查询记录
const users = await client.database.read('users', {
  name: '张三'
});

// 更新记录
await client.database.update('users', 'record-id', {
  name: '李四'
});

// 删除记录
await client.database.delete('users', 'record-id');
```

### 5. 文件存储

```typescript
// 上传文件
const file = new File(['content'], 'test.txt');
const uploadResult = await client.storage.upload('avatars', file);

// 下载文件
const blob = await client.storage.download('avatars', 'file-path');

// 获取文件公开访问链接
const publicUrl = client.storage.getPublicUrl('avatars', 'file-path');

// 删除文件
await client.storage.delete('avatars', 'file-path');
```

## Winbase 自定义 API 客户端

### API 端点映射

Winbase 客户端支持以下 RESTful API 端点：

#### 基础 CRUD
- `POST /api/app/:appUid/:collection` - 创建记录
- `DELETE /api/app/:appUid/:collection` - 删除记录（批量）
- `PUT /api/app/:appUid/:collection` - 更新记录
- `GET /api/app/:appUid/:collection/:id` - 根据 ID 查找
- `GET /api/app/:appUid/:collection` - 查找所有记录

#### 高级查询
- `GET /api/app/:appUid/:collection/uid/:uid` - 根据 UID 查找
- `POST /api/app/:appUid/:collection/unique` - 唯一查找（自定义条件）
- `GET /api/app/:appUid/:collection/list/paginate` - 分页列表
- `GET /api/app/:appUid/:collection/list/tree/paginate` - 树形分页
- `GET /api/app/:appUid/:collection/tree/data` - 树形数据

### 扩展功能

除了标准的 BaaSClient 接口，Winbase 还提供了扩展功能：

```typescript
import { createWinbaseExtensions } from '@/client/winbase';

const extensions = createWinbaseExtensions({
  baseURL: 'https://your-api.com',
  appUid: 'your-app-uid'
});

// 根据 ID 查找
const user = await extensions.findById('users', '123');

// 唯一查找
const user = await extensions.findUnique('users', {
  email: '<EMAIL>',
  status: 'active'
});

// 树形数据
const categories = await extensions.findTree('categories');

// 树形分页
const paginatedTree = await extensions.findTreePaginated('categories', {
  current: 1,
  pageSize: 10
});
```

### 直接使用 Winbase 客户端

```typescript
import { createWinbaseClient } from '@/client/winbase';

const client = createWinbaseClient({
  baseURL: 'https://your-api.com',
  appUid: 'your-app-uid',
  token: 'optional-token', // 可选，会使用 Storage.getItem('token')
  timeout: 6000 // 可选，默认 6000ms
});

// 使用客户端
const users = await client.database.readList('users', {
  current: 1,
  pageSize: 10
});
```

## 注意事项

1. 确保在使用客户端之前已经正确初始化
2. 所有异步操作都需要使用 `try/catch` 进行错误处理
3. 不同平台的实现可能会有细微差异，建议查看具体平台的文档
4. 环境变量必须正确配置，否则会抛出错误
5. 推荐使用 `getClient()` 函数方式获取客户端实例，这样可以获得更好的类型提示
6. Winbase 客户端需要确保后端 API 遵循指定的端点格式

## 错误处理

```typescript
try {
  const client = getClient();
  await client.auth.signIn(email, password);
} catch (error) {
  console.error('操作失败:', error);
}
```

## 类型支持

所有 API 都有完整的 TypeScript 类型支持，可以在开发时获得良好的类型提示和检查。 


## 分页查询

```
// 在组件中使用
const getData = async (params) => {
  try {
    const result = await client.database.readList('menu', {
      // 分页参数
      current: 1,
      pageSize: 10,
      
      // 排序参数
      sortField: 'created_at',
      sortOrder: 'descend',
      
      // 查询条件
      name: { type: 'like', value: '测试' },
      status: { type: 'eq', value: 1 },
      sort: { type: 'gte', value: 0 },
      type: { type: 'in', value: ['PC导航', 'H5导航'] }
    });
    
    // 设置表格数据
    setDataSource(result.data);
    // 设置分页信息
    setPagination({
      current: result.current,
      pageSize: result.pageSize,
      total: result.total
    });
  } catch (error) {
    message.error('获取数据失败');
  }
};
```