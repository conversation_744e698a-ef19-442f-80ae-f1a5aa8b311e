/**
 * 判断值是否为空
 * @param value 要判断的值
 * @returns 是否为空
 */
const isEmpty = (value: any): boolean => {
    // 处理 null 和 undefined
    if (value == null) {
      return true;
    }
  
    // 处理空字符串
    if (typeof value === 'string' && value.trim() === '') {
      return true;
    }
  
    // 处理空数组
    if (Array.isArray(value) && value.length === 0) {
      return true;
    }
  
    // 处理空对象
    if (typeof value === 'object' && !Array.isArray(value)) {
      return Object.keys(value).length === 0;
    }
  
    // 处理数字 0
    if (typeof value === 'number' && value === 0) {
      return true;
    }
  
    // 处理布尔值 false
    if (typeof value === 'boolean' && value === false) {
      return true;
    }
  
    return false;
  };

  export { 
    isEmpty
  }