import { createClient } from '@supabase/supabase-js';
import type { BaaSClient } from './types';
import { isEmpty } from './util';

export const createSupabaseClient = (): BaaSClient => {
  const supabaseUrl = import.meta.env.VITE_BAAS_URL;
  const supabaseKey = import.meta.env.VITE_BAAS_ANON_KEY;

  if (!supabaseUrl || !supabaseKey) {
    throw new Error('缺少 Supabase 配置');
  }

  const supabase = createClient(supabaseUrl, supabaseKey);

  return {
    auth: {
      signIn: async (email: string, password: string) => {
        const { data, error } = await supabase.auth.signInWithPassword({
          email,
          password,
        });
        if (error) throw error;
        return data;
      },

      signUp: async (email: string, password: string) => {
        const { data, error } = await supabase.auth.signUp({
          email,
          password,
        });
        if (error) throw error;
        return data;
      },

      signOut: async () => {
        const { error } = await supabase.auth.signOut();
        if (error) throw error;
      },

      getCurrentUser: async () => {
        const { data: { user }, error } = await supabase.auth.getUser();
        if (error) throw error;
        return user;
      },

      onAuthStateChange: (callback) => {
        const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
          callback(session?.user ?? null);
        });
        return () => subscription.unsubscribe();
      },
    },

    database: {
      create: async (table: string, data: any) => {
        const { data: result, error } = await supabase
          .from(table)
          .insert(data)
          .select()
          .single();
        if (error) throw error;
        return result;
      },

      read: async (table: string, query?: any) => {
        let request = supabase.from(table).select();
        if (query) {
          Object.entries(query).forEach(([key, value]) => {
            if (!isEmpty(value)) {
              request = request.ilike(key, `%${value}%`);
            }
          });
        }
        const { data, error } = await request;
        if (error) throw error;
        return data;
      },


      readList: async (table: string, query?: any) => {
        let request = supabase.from(table).select('*', { count: 'exact' });

        // 处理查询条件
        if (query) {
          // 处理分页参数
          const {
            current,
            pageSize,
            sortField,
            sortOrder,
            ...searchParams
          } = query;

          // 添加分页
          if (current && pageSize) {
            const from = (current - 1) * pageSize;
            const to = from + pageSize - 1;
            request = request.range(from, to);
          }

          // 添加排序
          if (sortField && sortOrder) {
            request = request.order(sortField, {
              ascending: sortOrder === 'ascend'
            });
          }

          // 处理搜索条件
          Object.entries(searchParams).forEach(([key, value]) => {
            if (!isEmpty(value)) {
              // 处理不同类型的查询条件
              if (typeof value === 'object') {
                switch (value.type) {
                  case 'like':
                    request = request.ilike(key, `%${value.value}%`);
                    break;
                  case 'eq':
                    request = request.eq(key, value.value);
                    break;
                  case 'gt':
                    request = request.gt(key, value.value);
                    break;
                  case 'gte':
                    request = request.gte(key, value.value);
                    break;
                  case 'lt':
                    request = request.lt(key, value.value);
                    break;
                  case 'lte':
                    request = request.lte(key, value.value);
                    break;
                  case 'in':
                    request = request.in(key, value.value);
                    break;
                  default:
                    request = request.ilike(key, `%${value}%`);
                }
              } else {
                // 默认使用模糊查询
                request = request.ilike(key, `%${value}%`);
              }
            }
          });
        }

        const { data, error, count } = await request;
        if (error) throw error;

        return {
          data,
          total: count || 0,
          current: query?.current || 1,
          pageSize: query?.pageSize || 10,
          sortField: query?.sortField,
          sortOrder: query?.sortOrder
        };
      },

      readByUniqueKey: async (table: string, uniqueKey: string, value: string) => {
        const { data, error } = await supabase
          .from(table)
          .select()
          .eq(uniqueKey, value)
          .single();
        if (error) throw error;
        return data;
      },

      update: async (table: string, id: string, data: any) => {
        const { data: result, error } = await supabase
          .from(table)
          .update(data)
          .eq('id', id)
          .select()
          .single();
        if (error) throw error;
        return result;
      },

      /**
       * 通用批量更新方法
       * @param table 表名
       * @param updates 更新操作数组，每项包含 where 条件和要更新的字段
       * 例：[
       *   { where: { id: 1 }, data: { is_home: false } },
       *   { where: { id: 2 }, data: { is_home: true, status: 1 } }
       * ]
       */
      batchUpdate: async (
        table: string,
        updates: Array<{ where: Record<string, any>; data: Record<string, any> }>
      ) => {
        for (const item of updates) {
          let req = supabase.from(table).update(item.data);
          Object.entries(item.where).forEach(([key, value]) => {
            req = req.eq(key, value);
          });
          const { error } = await req;
          if (error) throw error;
        }
      },

      updateByUniqueKey: async (table: string, uniqueKey: string, value: string, data: any,) => {
        const { data: result, error } = await supabase
          .from(table)
          .update(data)
          .eq(uniqueKey, value)
          .select()
          .single();
        if (error) throw error;
        return result;
      },

      delete: async (table: string, id: string) => {
        const { error } = await supabase
          .from(table)
          .delete()
          .eq('id', id);
        if (error) throw error;
      },

      deleteByUniqueKey: async (table: string, uniqueKey: string, value: string) => {
        const { error } = await supabase
          .from(table)
          .delete()
          .eq(uniqueKey, value);
        if (error) throw error; 
      },
      count: async (table: string, query?: any) => {
        let request = supabase.from(table).select('*', { count: 'exact' });
        if (query) {
          Object.entries(query).forEach(([key, value]) => {
            if (!isEmpty(value)) {
              request = request.ilike(key, `%${value}%`);
            }
          });
        }
        const { error, count } = await request;
        if (error) throw error;
        return count || 0;
      },
    },

    storage: {
      upload: async (bucket: string, file: File, path?: string) => {
        const filePath = path || `${Date.now()}_${file.name}`;
        const { data, error } = await supabase.storage
          .from(bucket)
          .upload(filePath, file);
        if (error) throw error;
        return data;
      },

      download: async (bucket: string, path: string) => {
        const { data, error } = await supabase.storage
          .from(bucket)
          .download(path);
        if (error) throw error;
        return data;
      },

      delete: async (bucket: string, path: string) => {
        const { error } = await supabase.storage
          .from(bucket)
          .remove([path]);
        if (error) throw error;
      },

      getPublicUrl: (bucket: string, path: string) => {
        const { data } = supabase.storage
          .from(bucket)
          .getPublicUrl(path);
        return data.publicUrl;
      },
    },
    meta: {
      // 获取表的所有字段信息
      getFieldsMeta: async (tableName: string) => {
        const { data, error } = await supabase
          .from('field_meta')
          .select("*")
        if (error) throw error;
        return data;
      }
    }
  };
};
