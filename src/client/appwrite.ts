import { Client, Account, Databases, Storage } from 'appwrite';
import type { BaaSClient } from './types';

export const createAppwriteClient = (): BaaSClient => {
  const appwriteEndpoint = import.meta.env.VITE_APPWRITE_ENDPOINT;
  const appwriteProjectId = import.meta.env.VITE_APPWRITE_PROJECT_ID;

  if (!appwriteEndpoint || !appwriteProjectId) {
    throw new Error('缺少 Appwrite 配置');
  }

  const client = new Client()
    .setEndpoint(appwriteEndpoint)
    .setProject(appwriteProjectId);

  const account = new Account(client);
  const databases = new Databases(client);
  const storage = new Storage(client);

  return {
    auth: {
      signIn: async (email: string, password: string) => {
        return await account.createEmailSession(email, password);
      },

      signUp: async (email: string, password: string) => {
        return await account.create('unique()', email, password);
      },

      signOut: async () => {
        await account.deleteSession('current');
      },

      getCurrentUser: async () => {
        return await account.get();
      },

      onAuthStateChange: (callback) => {
        const unsubscribe = client.subscribe('account', (response) => {
          if (response.events.includes('users.*.sessions.*.create')) {
            callback(response.payload);
          } else if (response.events.includes('users.*.sessions.*.delete')) {
            callback(null);
          }
        });
        return unsubscribe;
      },
    },

    database: {
      create: async (table: string, data: any) => {
        return await databases.createDocument(
          'default',
          table,
          'unique()',
          data
        );
      },

      read: async (table: string, query?: any) => {
        const queries = query
          ? Object.entries(query).map(([key, value]) => 
              databases.query.equal(key, value as string)
            )
          : [];
        
        return await databases.listDocuments(
          'default',
          table,
          queries
        );
      },

      readByUniqueKey: async (table: string, uniqueKey: string, value: string) => {
        const { documents } = await databases.listDocuments(
          'default',
          table,
          [databases.query.equal(uniqueKey, value)]
        );
        
        if (!documents.length) {
          throw new Error(`未找到记录: ${value}`);
        }

        return documents[0];
      },

      update: async (table: string, id: string, data: any) => {
        return await databases.updateDocument(
          'default',
          table,
          id,
          data
        );
      },

      updateByUniqueKey: async (table: string, uniqueKey: string, value: string, data: any) => {
        const { documents } = await databases.listDocuments(
          'default',
          table,
          [databases.query.equal(uniqueKey, value)]
        );
        
        if (!documents.length) {
          throw new Error(`未找到记录: ${value}`);
        }

        return await databases.updateDocument(
          'default',
          table,
          documents[0].$id,
          data
        );
      },

      delete: async (table: string, id: string) => {
        await databases.deleteDocument(
          'default',
          table,
          id
        );
      },
    },

    storage: {
      upload: async (bucket: string, file: File, path?: string) => {
        const fileId = path || `unique()`;
        return await storage.createFile(
          bucket,
          fileId,
          file
        );
      },

      download: async (bucket: string, path: string) => {
        const response = await storage.getFileDownload(
          bucket,
          path
        );
        return response;
      },

      delete: async (bucket: string, path: string) => {
        await storage.deleteFile(
          bucket,
          path
        );
      },

      getPublicUrl: (bucket: string, path: string) => {
        return storage.getFileView(
          bucket,
          path
        ).href;
      },
    },
  };
};
