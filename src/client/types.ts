export interface BaaSClient {
  // 认证相关
  auth: {
    signIn: (email: string, password: string) => Promise<any>;
    signUp: (email: string, password: string) => Promise<any>;
    signOut: () => Promise<void>;
    getCurrentUser: () => Promise<any>;
    onAuthStateChange: (callback: (user: any) => void) => () => void;
  };

  // 数据库相关
  database: {
    create: (table: string, data: any) => Promise<any>;
    read: (table: string, query?: any) => Promise<any[]>;
    readList: (table: string, query?: any) => Promise<{ data: any[]; total: number; current: number; pageSize: number; sortField?: string; sortOrder?: string }>;
    readByUniqueKey: (table: string, uniqueKey: string, value: string) => Promise<any>;
    update: (table: string, id: string, data: any) => Promise<any>;
    updateByUniqueKey: (table: string, uniqueKey: string, value: string, data: any) => Promise<any>;
    delete: (table: string, id: string) => Promise<void>;
    deleteByUniqueKey: (table: string, uniqueKey: string, value: string) => Promise<void>;
    count: (table: string, query?: any) => Promise<number>;
    batchUpdate: (
      table: string,
      updates: Array<{ where: Record<string, any>; data: Record<string, any> }>
    ) => Promise<void>;
  };

  // 存储相关
  storage: {
    upload: (bucket: string, file: File, path?: string) => Promise<any>;
    download: (bucket: string, path: string) => Promise<Blob>;
    delete: (bucket: string, path: string) => Promise<void>;
    getPublicUrl: (bucket: string, path: string) => string;
  };

  // 元数据相关
  meta: {
    getFieldsMeta: (tableName: string) => Promise<{
      field_name: string;
      label: string;
      data_type: string;
      is_nullable: boolean;
      default_value: string | null;
      sort: number;
      is_primary: boolean;
      is_foreign: boolean;
      width?: number;
      fixed?: 'left' | 'right';
      align?: 'left' | 'center' | 'right';
      render?: (text: any, record: any) => React.ReactNode;
    }[]>;
  };
}