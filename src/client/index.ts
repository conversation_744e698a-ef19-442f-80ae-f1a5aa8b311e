import { createWinbaseClient } from './winbase';
import { createSupabaseClient } from './supabase';
import { createAppwriteClient } from './appwrite';
import type { BaaSClient } from './types';


const pathname = window.location.pathname;
const [, , appId] = pathname.split('/');

let client: BaaSClient | null = null;

export const initClient = () => {
  if (client) {
    return client;
  }

  const platform = import.meta.env.VITE_BAAS_PLATFORM;

  switch (platform) {
    case 'winbase':
      const winbaseConfig = {
        baseURL: import.meta.env.VITE_BAAS_URL || '',
        appId: appId || import.meta.env.VITE_BAAS_APP_UID,
        token: import.meta.env.VITE_BAAS_TOKEN,
      };

      if (!winbaseConfig.baseURL || !winbaseConfig.appId) {
        throw new Error('缺少 Winbase 配置: VITE_BAAS_URL 和 app_uid 是必需的');
      }

      client = createWinbaseClient(winbaseConfig);
      break;
    case 'supabase':
      client = createSupabaseClient();
      break;
    case 'appwrite':
      client = createAppwriteClient();
      break;
    default:
      throw new Error(`不支持的 BaaS 平台: ${platform}`);
  }

  return client;
};

export const getClient = () => {
  if (!client) {
    return initClient();
  }
  return client;
};

// 直接导出的客户端变量
export const baas = {
  get client() {
    return getClient();
  }
};

// 直接导出客户端创建函数，供高级用户使用
export { createWinbaseClient, createWinbaseExtensions } from './winbase';
export { createSupabaseClient } from './supabase';
export { createAppwriteClient } from './appwrite';
export type { BaaSClient } from './types';
