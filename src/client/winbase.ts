/**
 * Winbase Client - A Supabase/Appwrite-like client for custom RESTful APIs
 *
 * This client provides a unified interface for interacting with your custom API endpoints:
 *
 * Basic CRUD:
 * - POST   /api/app/:appUid/:collection                    - Create record
 * - DELETE /api/app/:appUid/:collection                    - Delete records (batch)
 * - PUT    /api/app/:appUid/:collection                    - Update record
 * - GET    /api/app/:appUid/:collection/:id                - Find by ID
 * - GET    /api/app/:appUid/:collection                    - Find all records
 *
 * Advanced Queries:
 * - GET    /api/app/:appUid/:collection/uid/:uid           - Find by UID
 * - POST   /api/app/:appUid/:collection/unique             - Find unique (custom conditions)
 * - GET    /api/app/:appUid/:collection/list/paginate      - Paginated list
 * - GET    /api/app/:appUid/:collection/list/tree/paginate - Tree pagination
 * - GET    /api/app/:appUid/:collection/tree/data          - Tree data
 *
 * Usage:
 * ```typescript
 * import { createWinbaseClient } from '@/client/winbase';
 *
 * const client = createWinbaseClient({
 *   baseURL: 'https://your-api.com',
 *   appUid: 'your-app-uid',
 *   token: 'optional-token' // Will use Storage.getItem('token') if not provided
 * });
 *
 * // Create a record
 * const newUser = await client.database.create('users', { name: 'John', email: '<EMAIL>' });
 *
 * // Get paginated list
 * const users = await client.database.readList('users', { current: 1, pageSize: 10 });
 *
 * // Update a record
 * const updatedUser = await client.database.update('users', '123', { name: 'Jane' });
 * ```
 */

import axios, { AxiosInstance } from 'axios';
import type { BaaSClient } from './types';
import { Storage } from '@/utils/storage';

interface WinbaseConfig {
  baseURL: string;
  appId: string;
  token?: string;
  timeout?: number;
}

interface QueryParams {
  current?: number;
  pageSize?: number;
  sortField?: string;
  sortOrder?: 'ascend' | 'descend';
  [key: string]: any;
}

export const createWinbaseClient = (config: WinbaseConfig): BaaSClient => {
  const { baseURL, appId, token, timeout = 6000 } = config;

  // Create axios instance
  const instance: AxiosInstance = axios.create({
    baseURL,
    timeout,
  });

  // Add request interceptor
  instance.interceptors.request.use(
    (config) => {
      const authToken = token || Storage.getItem('token') || '';
      if (authToken) {
        config.headers.Authorization = `Bearer ${authToken}`;
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // Add response interceptor
  instance.interceptors.response.use(
    (response) => {
      return response.data;
    },
    (error) => {
      // Handle common errors
      if (error.response?.status === 401) {
        // Handle unauthorized
        Storage.clear();
        // You might want to redirect to login page here
      }
      return Promise.reject(error);
    }
  );

  // Helper function to build API URL
  const buildApiUrl = (collection: string, path?: string): string => {
    const basePath = `/api/app/${appId}/${collection}`;
    return path ? `${basePath}/${path}` : basePath;
  };

  return {
    auth: {
      signIn: async (email: string, password: string) => {
        // This would typically be a separate auth endpoint
        // Implement based on your auth API
        const { data } = await instance.post('/auth/signin', {
          email,
          password,
        });

        if (data.token) {
          Storage.setItem('token', data.token);
        }

        return data;
      },

      signUp: async (email: string, password: string) => {
        // This would typically be a separate auth endpoint
        // Implement based on your auth API
        const { data } = await instance.post('/auth/signup', {
          email,
          password,
        });

        if (data.token) {
          Storage.setItem('token', data.token);
        }

        return data;
      },

      signOut: async () => {
        // Clear local storage
        Storage.clear();

        // Optionally call logout endpoint
        try {
          await instance.post('/auth/signout');
        } catch (error) {
          // Ignore errors on signout
        }
      },

      getCurrentUser: async () => {
        try {
          const { data } = await instance.get('/auth/me');
          return data;
        } catch (error) {
          return null;
        }
      },

      onAuthStateChange: (callback: (user: any) => void) => {
        // This is a simplified implementation
        // In a real app, you might want to use WebSocket or polling
        let intervalId: NodeJS.Timeout;

        const checkAuthState = async () => {
          try {
            const user = await instance.get('/auth/me');
            callback(user.data);
          } catch (error) {
            callback(null);
          }
        };

        // Check immediately
        checkAuthState();

        // Check periodically (every 5 minutes)
        intervalId = setInterval(checkAuthState, 5 * 60 * 1000);

        // Return cleanup function
        return () => {
          if (intervalId) {
            clearInterval(intervalId);
          }
        };
      },
    },

    database: {
      // POST /api/app/:appUid/:collection - Create record
      create: async (table: string, data: any) => {
        const url = buildApiUrl(table);
        const response = await instance.post(url, data);
        return response;
      },

      // GET /api/app/:appUid/:collection - Find all records
      read: async (table: string, query?: any) => {
        const url = buildApiUrl(table);
        const response = await instance.get(url, { params: query });
        return response.data || response;
      },

      // GET /api/app/:appUid/:collection/list/paginate - Paginated list
      readList: async (table: string, query?: QueryParams) => {
        const url = buildApiUrl(table, 'list/paginate');
        const response: any = await instance.get(url, { params: query });

        // Ensure response matches expected format
        return {
          data: response.data || [],
          total: response.total || 0,
          current: response.current || query?.current || 1,
          pageSize: response.pageSize || query?.pageSize || 10,
          sortField: response.sortField || query?.sortField,
          sortOrder: response.sortOrder || query?.sortOrder,
        };
      },

      // GET /api/app/:appUid/:collection/uid/:uid - Find by UID
      readByUniqueKey: async (table: string, _uniqueKey: string, value: string) => {
        // Note: The API uses 'uid' in the path, so we ignore the uniqueKey parameter
        // since the API endpoint is specifically for UID lookups
        const url = buildApiUrl(table, `uid/${value}`);
        const response = await instance.get(url);
        return response.data || response;
      },

      // PUT /api/app/:appUid/:collection - Update record
      update: async (table: string, id: string, data: any) => {
        const url = buildApiUrl(table);
        const updateData = { ...data, id };
        const response = await instance.put(url, updateData);
        return response.data || response;
      },

      // PUT /api/app/:appUid/:collection - Update by unique key
      updateByUniqueKey: async (table: string, uniqueKey: string, value: string, data: any) => {
        const url = buildApiUrl(table);
        const updateData = { ...data, [uniqueKey]: value };
        const response = await instance.put(url, updateData);
        return response.data || response;
      },

      // DELETE /api/app/:appUid/:collection - Delete records (batch)
      delete: async (table: string, id: string) => {
        const url = buildApiUrl(table);
        await instance.delete(url, { data: { ids: [id] } });
      },

      // DELETE /api/app/:appUid/:collection - Delete by unique key
      deleteByUniqueKey: async (table: string, uniqueKey: string, value: string) => {
        const url = buildApiUrl(table);
        await instance.delete(url, { data: { [uniqueKey]: value } });
      },

      // Count records (using read with count parameter)
      count: async (table: string, query?: any) => {
        const url = buildApiUrl(table);
        const countQuery = { ...query, _count: true };
        const response: any = await instance.get(url, { params: countQuery });
        return response.total || response.count || 0;
      },

      // Batch update implementation
      batchUpdate: async (
        table: string,
        updates: Array<{ where: Record<string, any>; data: Record<string, any> }>
      ) => {
        // Since the API doesn't have a specific batch update endpoint,
        // we'll implement it by making multiple update requests
        for (const update of updates) {
          const url = buildApiUrl(table);
          const updateData = { ...update.data, ...update.where };
          await instance.put(url, updateData);
        }
      },
    },

    storage: {
      upload: async (bucket: string, file: File, path?: string) => {
        const formData = new FormData();
        formData.append('file', file);
        if (path) {
          formData.append('path', path);
        }

        const url = `/api/storage/${bucket}/upload`;
        const response: any = await instance.post(url, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });

        return response.data || response;
      },

      download: async (bucket: string, path: string) => {
        const url = `/api/storage/${bucket}/download`;
        const response = await instance.get(url, {
          params: { path },
          responseType: 'blob',
        });

        return response.data;
      },

      delete: async (bucket: string, path: string) => {
        const url = `/api/storage/${bucket}/delete`;
        await instance.delete(url, {
          data: { path },
        });
      },

      getPublicUrl: (bucket: string, path: string) => {
        return `${baseURL}/api/storage/${bucket}/public/${path}`;
      },
    },

    meta: {
      getFieldsMeta: async (tableName: string) => {
        // This endpoint might be different based on your API structure
        // Adjust the URL as needed
        const url = `/api/meta/fields/${tableName}`;
        const response: any = await instance.get(url);
        return response.data || response;
      },
    },
  };
};

// Additional utility functions for extended API functionality
export const createWinbaseExtensions = (config: WinbaseConfig) => {
  const { baseURL, appId, timeout = 6000 } = config;

  const instance: AxiosInstance = axios.create({
    baseURL,
    timeout,
  });

  // Add request interceptor
  instance.interceptors.request.use(
    (config) => {
      const authToken = Storage.getItem('token') || '';
      if (authToken) {
        config.headers.Authorization = `Bearer ${authToken}`;
      }
      return config;
    },
    (error) => Promise.reject(error)
  );

  // Add response interceptor
  instance.interceptors.response.use(
    (response) => response.data,
    (error) => Promise.reject(error)
  );

  const buildApiUrl = (collection: string, path?: string): string => {
    const basePath = `/api/app/${appId}/${collection}`;
    return path ? `${basePath}/${path}` : basePath;
  };

  return {
    // GET /api/app/:appUid/:collection/:id - Find by ID
    findById: async (table: string, id: string) => {
      const url = buildApiUrl(table, id);
      const response = await instance.get(url);
      return response.data || response;
    },

    // POST /api/app/:appUid/:collection/unique - Find unique (custom conditions)
    findUnique: async (table: string, conditions: Record<string, any>) => {
      const url = buildApiUrl(table, 'unique');
      const response = await instance.post(url, conditions);
      return response.data || response;
    },

    // GET /api/app/:appUid/:collection/list/tree/paginate - Tree pagination
    findTreePaginated: async (table: string, query?: QueryParams) => {
      const url = buildApiUrl(table, 'list/tree/paginate');
      const response: any = await instance.get(url, { params: query });

      return {
        data: response.data || [],
        total: response.total || 0,
        current: response.current || query?.current || 1,
        pageSize: response.pageSize || query?.pageSize || 10,
        sortField: response.sortField || query?.sortField,
        sortOrder: response.sortOrder || query?.sortOrder,
      };
    },

    // GET /api/app/:appUid/:collection/tree/data - Tree data
    findTree: async (table: string, query?: any) => {
      const url = buildApiUrl(table, 'tree/data');
      const response = await instance.get(url, { params: query });
      return response.data || response;
    },
  };
};