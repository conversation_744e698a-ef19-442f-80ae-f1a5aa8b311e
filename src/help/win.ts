// 动态加载所有Schema
const schemaModules = import.meta.glob("@/component/**/schema.tsx", {
  // eager: true, // 同步加载
});

// 创建一个对象来存储所有的Schema
const schemas: any[] = [];

// 遍历所有模块并将其添加到schemas数组（同步加载处理方式）
// for (const path in schemaModules) {
//   schemas.push(schemaModules[path]?.default); // 将模块添加到schemas数组
// }

// 遍历所有模块并将其添加到schemas数组（异步加载处理方式）
for (const path in schemaModules) {
  schemaModules[path]()
    .then((module) => {
      schemas.push(module?.default); // 将模块添加到schemas数组
    })
    .catch((error) => {
      console.error(`Error loading module from ${path}:`, error);
    });
}

export const winSchemas = schemas;

export default winSchemas;
