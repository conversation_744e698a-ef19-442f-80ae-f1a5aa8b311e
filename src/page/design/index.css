.design-container {
  position: relative;
}

.mobile {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  height: 736px;
  width: 414px;
  box-shadow: 0px 1px 4px #bbb;
}

.container {
  width: 1200px;
  margin: 0 auto;
}
.main {
  display: flex;
  justify-content: space-between;
}
.main .canvas {
  flex: 1;
  height: calc(100vh - 60px);
  overflow: auto;
}

.main .side {
  width: 40px;
}

.main .tool {
  width: 301px;
}

.App-logo {
  height: 40vmin;
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #09d3ac;
}
