import { useState, useCallback, useMemo } from "react";
import { createPortal } from "react-dom";
import {
  DndContext,
  useSensor,
  MouseSensor,
  TouchSensor,
  KeyboardSensor,
  useSensors,
  DragOverlay,
  closestCenter,
} from "@dnd-kit/core";

import { snapCenterToCursor } from "@dnd-kit/modifiers";

import {
  insertElement,
  moveElement,
  genAlphabetFieldId,
  genUniqueId,
} from "@/utils";

import { getComponentProps, getComponentSchema, getComponentConfigure } from "@/utils/schema";

import { Overlay } from "@/core/dnd/Overlay";

import useStore from "@/store";
import Head from "@/blocks/head";
import Side from "@/blocks/side";
import Stage from "@/blocks/stage";
import Setter from "@/blocks/setter";

import "./index.css";

/**
 * 设计器主页面组件
 * 提供拖拽式可视化页面设计功能
 */
const Design = () => {
  // 当前拖拽中的元素ID
  const [activeId, setActiveId] = useState<string>("");

  // 从全局状态中获取必要的数据和方法
  const {
    position,
    jsonSchema,
    updateSelectedId,
    updateJsonSchema,
    updatePosition,
  } = useStore();

  // 配置拖拽传感器，优化触发条件以提升用户体验
  const sensors = useSensors(
    useSensor(MouseSensor, {
      // 鼠标拖拽需要移动8px且延迟100ms才触发，容错5px
      activationConstraint: { distance: 8, delay: 100, tolerance: 5 },
    }),
    useSensor(TouchSensor, {
      // 触摸拖拽需要移动8px且延迟200ms才触发，容错5px
      activationConstraint: { distance: 8, delay: 200, tolerance: 5 },
    }),
    useSensor(KeyboardSensor)
  );

  // 拖拽修饰器，使拖拽元素跟随鼠标中心
  const modifiers = useMemo(() => [snapCenterToCursor], []);

  /**
   * 拖拽开始事件处理
   */
  const onDragStart = useCallback((event) => {
    setActiveId(event.active.id);
  }, []);

  /**
   * 拖拽结束事件处理
   * 处理组件的添加和移动逻辑
   */
  const onDragEnd = useCallback((event) => {
    const { active, over } = event;
    
    // 如果没有放置目标或拖拽到自身，则取消操作
    if (!over || active.id === over.id) {
      setActiveId("");
      return;
    }

    const { componentName, from } = active.data?.current || {};
    const uniqueId = from === "material" ? genUniqueId() : active.id;

    let newSchema;
    
    // 从物料库拖拽新组件
    if (from === "material") {
      const props = getComponentProps(componentName);
      const schema = getComponentSchema(componentName);
      
      // 创建新的组件实例
      const newMaterial = {
        uuid: uniqueId,
        name: genAlphabetFieldId(),
        field: genAlphabetFieldId(),
        title: schema?.title,
        componentName,
        css: schema?.css,
        version: schema?.version,
        i18n: schema?.i18n,
        events: schema?.events,
        methods: schema?.methods,
        props: { ...props },
        children: schema?.children,
      };

      // 根据放置位置插入新组件
      newSchema = over.id === "dnd_page" 
        ? [...jsonSchema, newMaterial]
        : insertElement(jsonSchema, over.id, newMaterial, position);
    } else {
      // 移动已存在的组件
      newSchema = moveElement(jsonSchema, active.id, over.id, position);
    }

    // 更新全局状态
    updateJsonSchema(newSchema);
    updateSelectedId(String(uniqueId));
    setActiveId("");
  }, [jsonSchema, position, updateJsonSchema, updateSelectedId]);

  /**
   * 拖拽移动过程中的事件处理
   * 根据鼠标位置动态计算放置位置（before/inside/after）
   */
  const onDragMove = useCallback((event) => {
    const { active, over, activatorEvent } = event;
    
    if (!over || active.id === over.id) return;
    
    const x = activatorEvent.clientX + event.delta.x;
    const rect = document.getElementById(over.id)?.getBoundingClientRect();
    
    if (!rect) return;

    const { left, width } = rect;
    const offset = x - left; // 相对于目标元素的X轴偏移量

    // 获取目标组件配置，判断是否为容器组件
    const {
      componentName: overComponentName = "Page",
      nestingRule: overNestingRule,
    } = over?.data?.current || {};
    
    const configure = getComponentConfigure(overComponentName);
    const isContainer = configure?.component?.isContainer;

    // 根据鼠标位置确定放置位置
    let newPosition = "";
    if (offset < width / 4) {
      newPosition = "before"; // 前1/4区域
    } else if (offset < (3 * width) / 4 && isContainer) {
      newPosition = "inside"; // 中间1/2区域，仅容器组件支持
    } else {
      newPosition = "after"; // 后1/4区域
    }

    // 获取拖拽元素的嵌套规则
    const {
      componentName: activeComponentName,
      nestingRule: activeNestingRule,
    } = active?.data?.current || {};

    // 检查父元素白名单限制
    if (activeNestingRule?.parentWhitelist?.length > 0) {
      if (!activeNestingRule.parentWhitelist.includes(overComponentName)) {
        newPosition = "";
      }
    }

    // 检查内部嵌套规则
    if (newPosition === "inside") {
      const hasChildWhitelist = overNestingRule?.childWhitelist?.length > 0;
      const hasParentWhitelist = activeNestingRule?.parentWhitelist?.length > 0;
      const isChildWildcard = overNestingRule?.childWhitelist?.includes("*");
      const isParentWildcard = activeNestingRule?.parentWhitelist?.includes("*");

      // 如果不是自由模式，需要检查嵌套规则
      if (hasChildWhitelist && hasParentWhitelist && !isChildWildcard && !isParentWildcard) {
        const isChildAllowed = overNestingRule.childWhitelist.includes(activeComponentName);
        const isParentAllowed = activeNestingRule.parentWhitelist.includes(overComponentName);
        
        if (!isChildAllowed || !isParentAllowed) {
          newPosition = "";
        }
      }
    }

    // 只有位置发生变化时才更新状态
    if (position !== newPosition) {
      updatePosition(newPosition);
    }
  }, [position, updatePosition]);

  return (
    <div className="design-container">
      {/* 顶部工具栏 */}
      <Head />
      
      {/* 拖拽上下文 */}
      <DndContext
        sensors={sensors}
        modifiers={modifiers}
        onDragStart={onDragStart}
        onDragMove={onDragMove}
        onDragEnd={onDragEnd}
        collisionDetection={closestCenter}
      >
        {/* 左侧工具栏 */}
        <Side />
        
        {/* 中央画布区域 */}
        <Stage />
        
        {/* 拖拽时的覆盖层显示 */}
        {createPortal(
          <DragOverlay dropAnimation={null}>
            {activeId ? <Overlay id={activeId} /> : null}
          </DragOverlay>,
          document.body
        )}
      </DndContext>
      
      {/* 右侧属性设置面板 */}
      <Setter />
    </div>
  );
};

export default Design;
