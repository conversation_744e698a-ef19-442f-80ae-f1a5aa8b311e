import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  message,
  Popconfirm,
  Typography,
  Row,
  Col,
  Statistic
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SettingOutlined,
  AppstoreOutlined
} from '@ant-design/icons';
import { getClient } from '@/client';

const { Title } = Typography;
const { Option } = Select;

interface App {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'inactive' | 'development';
  type: 'web' | 'mobile' | 'desktop';
  created_at: string;
  updated_at: string;
  pages_count?: number;
  users_count?: number;
}

const SaaSApps = () => {
  const [apps, setApps] = useState<App[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingApp, setEditingApp] = useState<App | null>(null);
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const client = getClient();

  // 模拟数据
  const mockApps: App[] = [
    {
      id: '1',
      name: '客户管理系统',
      description: '企业客户关系管理平台',
      status: 'active',
      type: 'web',
      created_at: '2024-01-15',
      updated_at: '2024-01-20',
      pages_count: 12,
      users_count: 45
    },
    {
      id: '2',
      name: '库存管理应用',
      description: '智能库存管理解决方案',
      status: 'development',
      type: 'web',
      created_at: '2024-01-10',
      updated_at: '2024-01-18',
      pages_count: 8,
      users_count: 23
    },
    {
      id: '3',
      name: '移动办公助手',
      description: '移动端办公协作工具',
      status: 'active',
      type: 'mobile',
      created_at: '2024-01-05',
      updated_at: '2024-01-16',
      pages_count: 15,
      users_count: 67
    }
  ];

  useEffect(() => {
    fetchApps();
  }, []);

  const fetchApps = async () => {
    setLoading(true);
    try {
      // 这里应该调用实际的API
      // const response = await client.database.list('apps');
      // setApps(response.data);
      
      // 暂时使用模拟数据
      setTimeout(() => {
        setApps(mockApps);
        setLoading(false);
      }, 500);
    } catch (error) {
      console.error('获取应用列表失败:', error);
      message.error('获取应用列表失败');
      setLoading(false);
    }
  };

  const handleCreate = () => {
    setEditingApp(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (app: App) => {
    setEditingApp(app);
    form.setFieldsValue(app);
    setModalVisible(true);
  };

  const handleDelete = async (id: string) => {
    try {
      // await client.database.delete('apps', id);
      message.success('删除成功');
      fetchApps();
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingApp) {
        // await client.database.update('apps', editingApp.id, values);
        message.success('更新成功');
      } else {
        // await client.database.create('apps', values);
        message.success('创建成功');
      }
      
      setModalVisible(false);
      fetchApps();
    } catch (error) {
      message.error(editingApp ? '更新失败' : '创建失败');
    }
  };

  const handleViewApp = (app: App) => {
    navigate(`/app/${app.id}/dashboard`);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green';
      case 'inactive': return 'red';
      case 'development': return 'orange';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return '运行中';
      case 'inactive': return '已停用';
      case 'development': return '开发中';
      default: return status;
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'web': return 'Web应用';
      case 'mobile': return '移动应用';
      case 'desktop': return '桌面应用';
      default: return type;
    }
  };

  const columns = [
    {
      title: '应用名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: App) => (
        <Space>
          <AppstoreOutlined />
          <span>{text}</span>
        </Space>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => getTypeText(type),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '页面数',
      dataIndex: 'pages_count',
      key: 'pages_count',
      render: (count: number) => count || 0,
    },
    {
      title: '用户数',
      dataIndex: 'users_count',
      key: 'users_count',
      render: (count: number) => count || 0,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: App) => (
        <Space size="middle">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleViewApp(record)}
            title="查看应用"
          />
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            title="编辑应用"
          />
          <Button
            type="text"
            icon={<SettingOutlined />}
            onClick={() => navigate(`/app/${record.id}/setting`)}
            title="应用设置"
          />
          <Popconfirm
            title="确定要删除这个应用吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              title="删除应用"
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 统计数据
  const totalApps = apps.length;
  const activeApps = apps.filter(app => app.status === 'active').length;
  const developmentApps = apps.filter(app => app.status === 'development').length;
  const totalPages = apps.reduce((sum, app) => sum + (app.pages_count || 0), 0);

  return (
    <div>
      <Title level={2}>应用管理</Title>
      
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic title="总应用数" value={totalApps} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="运行中" value={activeApps} valueStyle={{ color: '#3f8600' }} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="开发中" value={developmentApps} valueStyle={{ color: '#cf1322' }} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="总页面数" value={totalPages} />
          </Card>
        </Col>
      </Row>

      {/* 应用列表 */}
      <Card
        title="应用列表"
        extra={
          <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
            新建应用
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={apps}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      {/* 创建/编辑应用模态框 */}
      <Modal
        title={editingApp ? '编辑应用' : '新建应用'}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={() => setModalVisible(false)}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            status: 'development',
            type: 'web'
          }}
        >
          <Form.Item
            label="应用名称"
            name="name"
            rules={[{ required: true, message: '请输入应用名称' }]}
          >
            <Input placeholder="请输入应用名称" />
          </Form.Item>
          
          <Form.Item
            label="应用描述"
            name="description"
            rules={[{ required: true, message: '请输入应用描述' }]}
          >
            <Input.TextArea rows={3} placeholder="请输入应用描述" />
          </Form.Item>
          
          <Form.Item
            label="应用类型"
            name="type"
            rules={[{ required: true, message: '请选择应用类型' }]}
          >
            <Select placeholder="请选择应用类型">
              <Option value="web">Web应用</Option>
              <Option value="mobile">移动应用</Option>
              <Option value="desktop">桌面应用</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            label="状态"
            name="status"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select placeholder="请选择状态">
              <Option value="development">开发中</Option>
              <Option value="active">运行中</Option>
              <Option value="inactive">已停用</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default SaaSApps;
