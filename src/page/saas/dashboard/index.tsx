import { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Typography,
  Table,
  Tag,
  Progress,
  List,
  Avatar,
  Space,
  Button
} from 'antd';
import {
  AppstoreOutlined,
  TeamOutlined,
  UserOutlined,
  DatabaseOutlined,
  TrendingUpOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

const { Title, Text } = Typography;

interface DashboardStats {
  totalApps: number;
  activeApps: number;
  totalTenants: number;
  totalUsers: number;
  totalPages: number;
  storageUsed: number;
}

interface RecentActivity {
  id: string;
  type: 'app_created' | 'user_registered' | 'tenant_added' | 'page_published';
  title: string;
  description: string;
  time: string;
  user: string;
}

interface AppUsage {
  id: string;
  name: string;
  users: number;
  pages: number;
  status: 'active' | 'inactive' | 'maintenance';
  usage: number;
}

const SaaSDashboard = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalApps: 0,
    activeApps: 0,
    totalTenants: 0,
    totalUsers: 0,
    totalPages: 0,
    storageUsed: 0
  });
  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([]);
  const [appUsage, setAppUsage] = useState<AppUsage[]>([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      setTimeout(() => {
        setStats({
          totalApps: 15,
          activeApps: 12,
          totalTenants: 8,
          totalUsers: 234,
          totalPages: 89,
          storageUsed: 65
        });

        setRecentActivities([
          {
            id: '1',
            type: 'app_created',
            title: '新应用创建',
            description: '客户管理系统 v2.0 已创建',
            time: '2小时前',
            user: '张三'
          },
          {
            id: '2',
            type: 'user_registered',
            title: '新用户注册',
            description: '5名新用户加入平台',
            time: '4小时前',
            user: '系统'
          },
          {
            id: '3',
            type: 'tenant_added',
            title: '新租户添加',
            description: 'ABC公司 已加入平台',
            time: '1天前',
            user: '李四'
          },
          {
            id: '4',
            type: 'page_published',
            title: '页面发布',
            description: '订单管理页面已发布上线',
            time: '2天前',
            user: '王五'
          }
        ]);

        setAppUsage([
          {
            id: '1',
            name: '客户管理系统',
            users: 45,
            pages: 12,
            status: 'active',
            usage: 85
          },
          {
            id: '2',
            name: '库存管理应用',
            users: 23,
            pages: 8,
            status: 'active',
            usage: 67
          },
          {
            id: '3',
            name: '移动办公助手',
            users: 67,
            pages: 15,
            status: 'maintenance',
            usage: 45
          },
          {
            id: '4',
            name: '财务管理系统',
            users: 34,
            pages: 10,
            status: 'active',
            usage: 78
          }
        ]);

        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('获取仪表板数据失败:', error);
      setLoading(false);
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'app_created': return <AppstoreOutlined style={{ color: '#52c41a' }} />;
      case 'user_registered': return <UserOutlined style={{ color: '#1890ff' }} />;
      case 'tenant_added': return <TeamOutlined style={{ color: '#722ed1' }} />;
      case 'page_published': return <CheckCircleOutlined style={{ color: '#fa8c16' }} />;
      default: return <ClockCircleOutlined />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green';
      case 'inactive': return 'red';
      case 'maintenance': return 'orange';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return '运行中';
      case 'inactive': return '已停用';
      case 'maintenance': return '维护中';
      default: return status;
    }
  };

  const appUsageColumns = [
    {
      title: '应用名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => (
        <Space>
          <AppstoreOutlined />
          <span>{text}</span>
        </Space>
      ),
    },
    {
      title: '用户数',
      dataIndex: 'users',
      key: 'users',
    },
    {
      title: '页面数',
      dataIndex: 'pages',
      key: 'pages',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '使用率',
      dataIndex: 'usage',
      key: 'usage',
      render: (usage: number) => (
        <Progress percent={usage} size="small" />
      ),
    },
  ];

  return (
    <div>
      <Title level={2}>SaaS 平台概览</Title>
      
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={4}>
          <Card loading={loading}>
            <Statistic
              title="总应用数"
              value={stats.totalApps}
              prefix={<AppstoreOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card loading={loading}>
            <Statistic
              title="运行中应用"
              value={stats.activeApps}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card loading={loading}>
            <Statistic
              title="租户数量"
              value={stats.totalTenants}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card loading={loading}>
            <Statistic
              title="总用户数"
              value={stats.totalUsers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card loading={loading}>
            <Statistic
              title="总页面数"
              value={stats.totalPages}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: '#eb2f96' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card loading={loading}>
            <Statistic
              title="存储使用率"
              value={stats.storageUsed}
              suffix="%"
              prefix={<TrendingUpOutlined />}
              valueStyle={{ color: stats.storageUsed > 80 ? '#cf1322' : '#3f8600' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={16}>
        {/* 应用使用情况 */}
        <Col span={16}>
          <Card
            title="应用使用情况"
            extra={
              <Button type="link" onClick={() => navigate('/saas/apps')}>
                查看全部
              </Button>
            }
          >
            <Table
              columns={appUsageColumns}
              dataSource={appUsage}
              rowKey="id"
              loading={loading}
              pagination={false}
              size="small"
            />
          </Card>
        </Col>

        {/* 最近活动 */}
        <Col span={8}>
          <Card title="最近活动">
            <List
              loading={loading}
              dataSource={recentActivities}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar icon={getActivityIcon(item.type)} />}
                    title={item.title}
                    description={
                      <div>
                        <div>{item.description}</div>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {item.time} · {item.user}
                        </Text>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>

      {/* 快速操作 */}
      <Row gutter={16} style={{ marginTop: 24 }}>
        <Col span={24}>
          <Card title="快速操作">
            <Space size="large">
              <Button type="primary" icon={<AppstoreOutlined />} onClick={() => navigate('/saas/apps')}>
                管理应用
              </Button>
              <Button icon={<TeamOutlined />} onClick={() => navigate('/saas/tenant')}>
                管理租户
              </Button>
              <Button icon={<UserOutlined />} onClick={() => navigate('/saas/user')}>
                管理用户
              </Button>
              <Button icon={<SettingOutlined />} onClick={() => navigate('/saas/system')}>
                系统设置
              </Button>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default SaaSDashboard;
