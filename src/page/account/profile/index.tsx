import { Card, Row, Col, Avatar, Descriptions } from 'antd';
import { UserOutlined } from '@ant-design/icons';

const Profile = () => {
  const userInfo = {
    name: '张三',
    email: 'z<PERSON><PERSON>@example.com',
    phone: '13800138000',
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card title="个人资料" style={{ maxWidth: 800 }}>
        <Row gutter={24}>
          <Col span={8} style={{ textAlign: 'center' }}>
            <Avatar size={120} icon={<UserOutlined />} />
          </Col>
          <Col span={16}>
            <Descriptions column={1}>
              <Descriptions.Item label="姓名">
                <span style={{ fontSize: '16px' }}>{userInfo.name}</span>
              </Descriptions.Item>
              <Descriptions.Item label="邮箱">
                <span style={{ fontSize: '16px' }}>{userInfo.email}</span>
              </Descriptions.Item>
              <Descriptions.Item label="手机号">
                <span style={{ fontSize: '16px' }}>{userInfo.phone}</span>
              </Descriptions.Item>
            </Descriptions>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default Profile;
