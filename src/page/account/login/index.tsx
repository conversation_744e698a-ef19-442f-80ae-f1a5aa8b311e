import { Card, Input, Button, Form, message, Typography, Flex, Space, Divider } from "antd";
import { UserOutlined, LockOutlined } from "@ant-design/icons";
import { useState } from "react";
import { useNavigate, useSearchParams, Link } from "react-router-dom";
import { getClient } from '@/client';

const { Title, Text } = Typography;
const client = getClient();

const Login = () => {
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const redirect = searchParams.get('redirect') || '/';

  const handleLogin = async (values: { email: string; password: string }) => {
    setLoading(true);
    try {
      const { user } = await client.auth.signIn(values.email, values.password);
      message.success("登录成功");
      navigate(redirect);
    } catch (error: any) {
      message.error(error.message || "登录失败");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Flex 
      style={{
        minHeight: '100vh', 
      }}
      align="center"
      justify="center"
    >
      <Card 
        style={{ 
          width: 400, 
          boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
          borderRadius: '12px',
        }}
      >
        <Flex vertical gap="large">
          <Flex vertical align="center">
            <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
              欢迎登录
            </Title>
            <Text type="secondary">请输入您的账户信息</Text>
          </Flex>
          
          <Form onFinish={handleLogin} layout="vertical" size="large">
            <Form.Item
              name="email"
              rules={[
                { required: true, message: "请输入邮箱" },
                { type: "email", message: "请输入有效的邮箱地址" },
              ]}
            >
              <Input 
                prefix={<UserOutlined />}
                placeholder="请输入邮箱"
              />
            </Form.Item>
            
            <Form.Item
              name="password"
              rules={[{ required: true, message: "请输入密码" }]}
            >
              <Input.Password 
                prefix={<LockOutlined />}
                placeholder="请输入密码"
              />
            </Form.Item>
            
            <Form.Item style={{ marginBottom: 16 }}>
              <Button 
                type="primary" 
                htmlType="submit" 
                loading={loading} 
                block
                size="large"
              >
                {loading ? '登录中...' : '立即登录'}
              </Button>
            </Form.Item>
          </Form>
          
          <Space size={24} style={{ justifyContent: 'center', width: '100%' }}>
            <Link to="/forgot-password">忘记密码？</Link>
            <Divider type="vertical" style={{ height: 18, margin: '0 8px' }} />
            <Link to="/register">立即注册</Link>
          </Space>
        </Flex>
      </Card>
    </Flex>
  );
};

export default Login;
