import { Card, Input, Button, Form, message, Typography, Space, Flex } from "antd";
import { UserOutlined } from "@ant-design/icons";
import { useState } from "react";
import { Link } from "react-router-dom";
import { getClient } from '@/client';

const { Title, Text } = Typography;
const client = getClient();

const ForgotPassword = () => {
  const [loading, setLoading] = useState(false);
  const [sent, setSent] = useState(false);

  const handleResetPassword = async (values: { email: string }) => {
    setLoading(true);
    try {
      // 注意：这里需要根据实际的 Supabase 客户端 API 调整
      // await client.auth.resetPasswordForEmail(values.email);
      message.success("重置密码邮件已发送，请检查您的邮箱");
      setSent(true);
    } catch (error: any) {
      message.error(error.message || "发送失败");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Flex
      style={{
        minHeight: '100vh',
      }}
      align="center"
      justify="center"
    >
      <Card
        style={{
          width: 400,
          boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
          borderRadius: '12px',
          border: 'none'
        }}
      >
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div style={{ textAlign: 'center' }}>
            <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
              忘记密码
            </Title>
            <Text type="secondary">
              {sent ? '邮件已发送' : '输入您的邮箱地址，我们将发送重置链接'}
            </Text>
          </div>

          {!sent ? (
            <Form onFinish={handleResetPassword} layout="vertical" size="large">
              <Form.Item
                name="email"
                rules={[
                  { required: true, message: "请输入邮箱" },
                  { type: "email", message: "请输入有效的邮箱地址" },
                ]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder="请输入注册时的邮箱"
                />
              </Form.Item>

              <Form.Item style={{ marginBottom: 16 }}>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  block
                  size="large"
                >
                  {loading ? '发送中...' : '发送重置邮件'}
                </Button>
              </Form.Item>
            </Form>
          ) : (
            <div style={{ textAlign: 'center', padding: '20px 0' }}>
              <Text>
                我们已向您的邮箱发送了密码重置链接，请检查您的邮箱并按照说明操作。
              </Text>
            </div>
          )}

          <div style={{ textAlign: 'center' }}>
            <Link to="/login" style={{ color: '#1890ff' }}>
              返回登录
            </Link>
          </div>
        </Space>
      </Card>
    </Flex>
  );
};

export default ForgotPassword;