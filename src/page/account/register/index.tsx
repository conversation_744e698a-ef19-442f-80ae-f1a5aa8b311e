import { Card, Input, Button, Form, message, Typography, Flex } from "antd";
import { UserOutlined, LockOutlined } from "@ant-design/icons";
import { useState } from "react";
import { useNavigate, Link } from "react-router-dom";
import { getClient } from '@/client';

const { Title, Text } = Typography;
const client = getClient();

const Register = () => {
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleRegister = async (values: { email: string; password: string }) => {
    setLoading(true);
    try {
      await client.auth.signUp(values.email, values.password);
      message.success("注册成功，请检查邮箱验证");
      navigate("/login");
    } catch (error: any) {
      message.error(error.message || "注册失败");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Flex 
      style={{ 
        minHeight: '100vh', 
      }}
      align="center"
      justify="center"
    >
      <Card 
        style={{ 
          width: 400, 
          boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
          borderRadius: '12px',
        }}
      >
        <Flex vertical gap="large">
          <Flex vertical align="center">
            <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
               用户注册
            </Title>
            <Text type="secondary">创建您的新账户</Text>
          </Flex>
          
          <Form onFinish={handleRegister} layout="vertical" size="large">
            <Form.Item
              name="email"
              rules={[
                { required: true, message: "请输入邮箱" },
                { type: "email", message: "请输入有效的邮箱地址" },
              ]}
            >
              <Input 
                prefix={<UserOutlined />}
                placeholder="请输入邮箱"
              />
            </Form.Item>
            
            <Form.Item
              name="password"
              rules={[
                { required: true, message: "请输入密码" },
                { min: 6, message: "密码至少6位" },
              ]}
            >
              <Input.Password 
                prefix={<LockOutlined />}
                placeholder="请输入密码"
              />
            </Form.Item>
            
            <Form.Item
              name="confirmPassword"
              dependencies={['password']}
              rules={[
                { required: true, message: "请确认密码" },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('password') === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('两次输入的密码不一致'));
                  },
                }),
              ]}
            >
              <Input.Password 
                prefix={<LockOutlined />}
                placeholder="请确认密码"
              />
            </Form.Item>
            
            <Form.Item style={{ marginBottom: 16 }}>
              <Button 
                type="primary" 
                htmlType="submit" 
                loading={loading} 
                block
                size="large"
              >
                {loading ? '注册中...' : '立即注册'}
              </Button>
            </Form.Item>
          </Form>
          
          <Flex justify="center" gap="small">
            <Text type="secondary">已有账户？</Text>
            <Link to="/login">立即登录</Link>
          </Flex>
        </Flex>
      </Card>
    </Flex>
  );
};

export default Register;