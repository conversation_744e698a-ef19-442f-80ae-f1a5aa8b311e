import { useEffect, useState } from 'react';
import { Row, Col, Card, Statistic } from 'antd';
import { getClient } from "@/client";
import CountUp from 'react-countup';

const client = getClient();

const formatter = (value) => (
  <CountUp end={value as number} separator="," />
);



const Dashboard = () => {

  const [pageCount, setPageCount] = useState(0)
  const [menuCount, setMenuCount] = useState(0)
  const [fieldCount, setFieldCount] = useState(0)

  useEffect(() => {
    getPageCount()
    getMenuCount()
    getFieldCount()
  }, [])

  const getPageCount = async () => {
    const { total } = await client.database.readList("page", { current: 1, pageSize: 10 })
    setPageCount(total)
  }

  const getMenuCount = async () => {
    const { total } = await client.database.readList("menu", { current: 1, pageSize: 10 })
    setMenuCount(total)
  }

  const getFieldCount = async () => {
    const { total } = await client.database.readList("field_meta", { current: 1, pageSize: 10 })
    setFieldCount(total)
  }

  return (
    <Row gutter={[16, 16]}>
      <Col xs={24} sm={12} md={12} lg={6} xl={6}>
        <Card><Statistic title="活跃用户" value={12} formatter={formatter} /></Card>
      </Col>
      <Col xs={24} sm={12} md={12} lg={6} xl={6}>
        <Card><Statistic title="页面总数" value={pageCount} formatter={formatter} /></Card>
      </Col>
      <Col xs={24} sm={12} md={12} lg={6} xl={6}>
        <Card><Statistic title="导航总数" value={menuCount} formatter={formatter} /></Card>
      </Col>
      <Col xs={24} sm={12} md={12} lg={6} xl={6}>
        <Card><Statistic title="字段总数" value={fieldCount} formatter={formatter} /></Card>
      </Col>
    </Row>
  );
};

export default Dashboard;
