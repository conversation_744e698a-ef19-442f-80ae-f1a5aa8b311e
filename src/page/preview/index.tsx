import { useNavigate, useParams } from "react-router-dom";
import {
  EditOutlined,
  ControlOutlined,
  DesktopOutlined,
  TabletOutlined,
  MobileOutlined,
  CloudOutlined,
} from "@ant-design/icons";
import { Float<PERSON>utton, Tooltip, Spin } from "antd";
import useStore from "@/store/index";
import { IpadSimulation, MobileSimulation } from "@/core/simulation";
import { SchemaRender } from "@/core/render";

import "./index.css";

const Preview = () => {
  const clientMode = useStore((state) => state.clientMode);
  const { setClientMode, jsonSchema, loading } = useStore();
  const naviagate = useNavigate();
  const params = useParams();

  const onClientChange = (mode: string) => {
    setClientMode(mode);
  };

  const onRuntime = () => {
    naviagate(`/app/${params.appId}/page/${params.pageId}/runtime`);
  };

  const onDesign = () => {
    naviagate(`/app/${params.appId}/page/${params.pageId}/design`);
  };

  const Child = () => {
    return (
      <Spin spinning={loading} tip="数据加载中">
        <div style={{ color: "#eee" }}>
          {<SchemaRender schema={jsonSchema} tense="runtime" />}
        </div>
      </Spin>
    );
  };

  return (
    <div>
      {clientMode !== "desk" ? (
        <div className="simulation" style={{ height: "100vh" }}>
          {clientMode === "ipad" && (
            <IpadSimulation>
              <Child />
            </IpadSimulation>
          )}
          {clientMode === "mobile" && (
            <MobileSimulation>
              <Child />
            </MobileSimulation>
          )}
        </div>
      ) : (
        <div>
          <Child />
        </div>
      )}

      <FloatButton.Group
        trigger="click"
        type="primary"
        style={{ insetInlineEnd: 24 }}
        icon={<ControlOutlined />}
      >
        <Tooltip title="手机端" placement="left">
          <FloatButton
            icon={<MobileOutlined />}
            onClick={() => onClientChange("mobile")}
          />
        </Tooltip>

        <Tooltip title="iPad" placement="left">
          <FloatButton
            icon={<TabletOutlined />}
            onClick={() => onClientChange("ipad")}
          />
        </Tooltip>

        <Tooltip title="桌面端" placement="left">
          <FloatButton
            icon={<DesktopOutlined />}
            onClick={() => onClientChange("desk")}
          />
        </Tooltip>

        <Tooltip title="运行时" placement="left">
          <FloatButton icon={<CloudOutlined />} onClick={onRuntime} />
        </Tooltip>

        <Tooltip title="设计页" placement="left">
          <FloatButton icon={<EditOutlined />} onClick={onDesign} />
        </Tooltip>
        <FloatButton.BackTop visibilityHeight={1080} />
      </FloatButton.Group>
    </div>
  );
};

export default Preview;
