import { useNavigate, useParams } from "react-router-dom";
import {
  EditOutlined,
  ControlOutlined,
  DesktopOutlined,
  TabletOutlined,
  MobileOutlined,
} from "@ant-design/icons";
import { Float<PERSON><PERSON>on, Tooltip, Spin } from "antd";
import useStore from "@/store/index";
import { IpadSimulation, MobileSimulation } from "@/core/simulation";
import { SchemaRender } from "@/core/render";
import RuntimeLayout from "@/component/Layout/runtime";

import "./index.css";

const Runtime = () => {
  const clientMode = useStore((state) => state.clientMode);
  const { setClientMode, loading } = useStore();
  const jsonSchema = useStore((state) => state.jsonSchema);
  const params = useParams();
  const naviagate = useNavigate();

  const onClientChange = (mode: string) => {
    setClientMode(mode);
  };

  const onDesign = () => {
    naviagate(`/app/${params.appId}/page/${params.pageId}/design`);
  };

  const Child = () => {
    return (
      <Spin spinning={loading}>
        <div style={{ color: "#eee" }}>
          {<SchemaRender schema={jsonSchema} tense="runtime" />}
        </div>
      </Spin>
    );
  };

  return (
    <div className="runtime">
      {clientMode !== "desk" ? (
        <div className="simulation" style={{ height: "100vh" }}>
          {clientMode === "ipad" && (
            <IpadSimulation>
              <Child schema={jsonSchema} />
            </IpadSimulation>
          )}
          {clientMode === "mobile" && (
            <MobileSimulation>
              <Child schema={jsonSchema} />
            </MobileSimulation>
          )}
        </div>
      ) : (
        <div>
          <RuntimeLayout>
            <Child schema={jsonSchema} />
          </RuntimeLayout>
        </div>
      )}

      <FloatButton.Group
        trigger="click"
        type="primary"
        style={{ insetInlineEnd: 24 }}
        icon={<ControlOutlined />}
      >
        <Tooltip title="手机端" placement="left">
          <FloatButton
            icon={<MobileOutlined />}
            onClick={() => onClientChange("mobile")}
          />
        </Tooltip>

        <Tooltip title="iPad" placement="left">
          <FloatButton
            icon={<TabletOutlined />}
            onClick={() => onClientChange("ipad")}
          />
        </Tooltip>

        <Tooltip title="桌面端" placement="left">
          <FloatButton
            icon={<DesktopOutlined />}
            onClick={() => onClientChange("desk")}
          />
        </Tooltip>

        <Tooltip title="设计页" placement="left">
          <FloatButton icon={<EditOutlined />} onClick={onDesign} />
        </Tooltip>
        <FloatButton.BackTop visibilityHeight={1080} />
      </FloatButton.Group>
    </div>
  );
};

export default Runtime;
