import React, { useRef, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Spin } from "antd";
import { useNavigate, useParams } from "react-router-dom";
import BpmnViewer from "bpmn-js/lib/Viewer"; // 查看器
import "bpmn-js/dist/assets/diagram-js.css";
import "bpmn-js/dist/assets/bpmn-js.css";

import Head from "../components/head";
import "./index.css";

import { EditOutlined } from "@ant-design/icons";

const BPMNPreviewer = (props) => {
  const { xml } = props;
  const canvasRef = useRef(null);
  const naviagate = useNavigate();
  const { appId, workflowId } = useParams();

  // 初始化BPMN实例对象
  let bpmnViewer;

  useEffect(() => {
    // 创建 bpmn-js 查看器实例
    bpmnViewer = new BpmnViewer({
      container: canvasRef.current,
    });

    const loadDiagram = async () => {
      try {
        // 加载 BPMN XML 到查看器
        await bpmnViewer.importXML(`BPMN XML: <?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="Process_1" isExecutable="false">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_16j69xa</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:exclusiveGateway id="Gateway_0ytqp68">
      <bpmn:incoming>Flow_16j69xa</bpmn:incoming>
      <bpmn:outgoing>Flow_153y6jn</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_16j69xa" sourceRef="StartEvent_1" targetRef="Gateway_0ytqp68" />
    <bpmn:task id="Activity_09zvuwx">
      <bpmn:incoming>Flow_153y6jn</bpmn:incoming>
      <bpmn:outgoing>Flow_0r3567i</bpmn:outgoing>
    </bpmn:task>
    <bpmn:sequenceFlow id="Flow_153y6jn" sourceRef="Gateway_0ytqp68" targetRef="Activity_09zvuwx" />
    <bpmn:exclusiveGateway id="Gateway_10hfhwq">
      <bpmn:incoming>Flow_0r3567i</bpmn:incoming>
      <bpmn:outgoing>Flow_0flo5pc</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0r3567i" sourceRef="Activity_09zvuwx" targetRef="Gateway_10hfhwq" />
    <bpmn:endEvent id="Event_0ly7sde">
      <bpmn:incoming>Flow_0flo5pc</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0flo5pc" sourceRef="Gateway_10hfhwq" targetRef="Event_0ly7sde" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="173" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0ytqp68_di" bpmnElement="Gateway_0ytqp68" isMarkerVisible="true">
        <dc:Bounds x="265" y="95" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_09zvuwx_di" bpmnElement="Activity_09zvuwx">
        <dc:Bounds x="380" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_10hfhwq_di" bpmnElement="Gateway_10hfhwq" isMarkerVisible="true">
        <dc:Bounds x="545" y="95" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0ly7sde_di" bpmnElement="Event_0ly7sde">
        <dc:Bounds x="662" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_16j69xa_di" bpmnElement="Flow_16j69xa">
        <di:waypoint x="209" y="120" />
        <di:waypoint x="265" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_153y6jn_di" bpmnElement="Flow_153y6jn">
        <di:waypoint x="315" y="120" />
        <di:waypoint x="380" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0r3567i_di" bpmnElement="Flow_0r3567i">
        <di:waypoint x="480" y="120" />
        <di:waypoint x="545" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0flo5pc_di" bpmnElement="Flow_0flo5pc">
        <di:waypoint x="595" y="120" />
        <di:waypoint x="662" y="120" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
    `);
        console.log("BPMN 图表加载成功");

        // Center the diagram in the viewer
        const canvas = bpmnViewer.get("canvas");
        canvas.zoom("fit-viewport"); // Adjust zoom to fit the viewport
        // canvas.center(); // Center the diagram
      } catch (err) {
        console.error("导入 BPMN XML 时出错:", err);
      }
    };

    loadDiagram();

    return () => {
      // 组件卸载时销毁查看器实例
      if (bpmnViewer) {
        bpmnViewer.destroy();
      }
    };
  }, [xml]);

  const onDesign = () => {
    naviagate(`/app/${appId}/workflow/${workflowId}/design`);
  };

  return (
    <div className="preview-bpmn-container">
      <Head mode="preview" />
      <div className="preview-bpmn-stage">
        <div className="preview-bpmn-canvas" ref={canvasRef}></div>
      </div>

      <FloatButton.Group
        trigger="click"
        type="primary"
        style={{ insetInlineEnd: 24 }}
        onClick={onDesign}
        icon={<EditOutlined />}
      >
        <FloatButton.BackTop visibilityHeight={1080} />
      </FloatButton.Group>
    </div>
  );
};

export default BPMNPreviewer;
