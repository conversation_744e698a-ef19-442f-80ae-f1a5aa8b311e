import React, { useRef } from "react";

import BpmnModeler from "bpmn-js/lib/Modeler";

import {
  BpmnPropertiesPanelModule,
  BpmnPropertiesProviderModule,
  CamundaPlatformPropertiesProviderModule, // Camunda 8 provider
} from "bpmn-js-properties-panel";

// Camunda 8 moddle extension
import CamundaBpmnModdle from "camunda-bpmn-moddle/resources/camunda.json";

import Head from "../components/head";

import "bpmn-js/dist/assets/diagram-js.css";
import "bpmn-js/dist/assets/bpmn-font/css/bpmn.css";
import "bpmn-js/dist/assets/bpmn-font/css/bpmn-codes.css";
import "bpmn-js/dist/assets/bpmn-font/css/bpmn-embedded.css";
import "@bpmn-io/properties-panel/dist/assets/properties-panel.css";

import "./index.css";
import customTranslate from "../utils/i18n/customTranslate";

const BPMNDesigner = () => {
  const canvasRef = useRef<HTMLDivElement>(null);
  const panelRef = useRef<HTMLDivElement>(null);

  // 初始化BPMN实例对象
  let bpmnModeler;

  React.useEffect(() => {
    var customTranslateModule = {
      translate: ["value", customTranslate],
    };

    bpmnModeler = new BpmnModeler({
      container: canvasRef.current,
      propertiesPanel: {
        parent: panelRef.current,
      },
      additionalModules: [
        customTranslateModule,
        BpmnPropertiesPanelModule,
        BpmnPropertiesProviderModule,
        CamundaPlatformPropertiesProviderModule,
      ],
      moddleExtensions: {
        zeebe: CamundaBpmnModdle,
      },
    });

    // 加载默认空白画布或其他指定XML字符串表示的业务流程定义
    bpmnModeler.createDiagram();

    return () => {
      if (bpmnModeler) {
        bpmnModeler.destroy();
      }
    };
  }, []);

  const download = (meta, type = "bpmn", extension = "application/xml") => {
    const blob = new Blob([meta], { type });
    const link = document.createElement("a");
    link.download = `workflow_${Date.now()}.${extension}`;
    link.href = URL.createObjectURL(blob);
    link.click();
    URL.revokeObjectURL(link.href);
  };

  // 导出 XML 并保存
  const handleSave = async () => {
    try {
      const { xml } = await bpmnModeler?.saveXML({ format: true });
      console.log("BPMN XML:", xml);
    } catch (err) {
      console.error("Error saving BPMN diagram", err);
    }
  };

  // 导出 XML 并保存
  const exportSVG = async () => {
    try {
      const svg = await bpmnModeler?.saveSVG();
      download(svg, "image/svg+xml", "svg");
    } catch (err) {
      console.error("Error saving BPMN diagram", err);
    }
  };

  // 下载BPMN文件
  const downloadBpmn = async () => {
    try {
      const { xml } = await bpmnModeler?.saveXML({ format: true });
      download(xml, "application/xml", "bpmn");
    } catch (err) {
      console.error("导出失败:", err);
    }
  };

  const onBtnHandle = (e) => {
    console.log({ e });
    if (e === "save") {
      handleSave();
    }
    if (e === "bpmn") {
      downloadBpmn();
    }
    if (e === "svg") {
      exportSVG();
    }
  };

  return (
    <div className="design-bpmn-container">
      <Head onBtnHandle={onBtnHandle} />
      <div className="design-bpmn-stage">
        <div className="design-bpmn-canvas" ref={canvasRef}></div>
        <div className="design-bpmn-properties-panel" ref={panelRef}></div>
      </div>
    </div>
  );
};

export default BPMNDesigner;
