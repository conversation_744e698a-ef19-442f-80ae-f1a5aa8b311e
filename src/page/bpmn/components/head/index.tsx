import { useState } from "react";
import { useN<PERSON><PERSON>, Link, useParams } from "react-router-dom";
import { Flex, Space, theme, Button, Divider, Dropdown, Avatar } from "antd";
import {
  SaveOutlined,
  EyeOutlined,
  RestOutlined,
  UserOutlined,
  LogoutOutlined,
  ExportOutlined,
  GatewayOutlined,
  FileImageOutlined,
  FilePdfOutlined,
  MediumOutlined,
  HomeOutlined,
} from "@ant-design/icons";

import { modal } from "@/store/hooks";
import logoSvg from "@/assets/logo.png";
import "./index.css";

const { VITE_SAAS_HOST } = import.meta.env;

const Head = (props) => {
  const { onBtnHandle, mode = "design" } = props;

  const {
    token: { colorTextBase },
  } = theme.useToken();

  const naviagate = useNavigate();
  const { workflowId, appId } = useParams();

  const exportItems = [
    {
      label: "BPMN",
      key: "bpmn",
      icon: <GatewayOutlined />,
    },
    {
      type: "divider",
    },
    {
      label: "PNG",
      key: "img",
      icon: <FileImageOutlined />,
    },
    {
      type: "divider",
    },
    {
      label: "SVG",
      key: "svg",
      icon: <MediumOutlined />,
    },
    {
      type: "divider",
    },
    {
      label: "PDF",
      key: "pdf",
      icon: <FilePdfOutlined />,
    },
  ];

  const items = [
    {
      label: (
        <Link to={`${VITE_SAAS_HOST}/app/${appId}/dashboard`} target="_blank">
          项目首页
        </Link>
      ),
      key: "app",
      icon: <HomeOutlined />,
    },
    {
      type: "divider",
    },
    {
      label: (
        <Link
          to={`${VITE_SAAS_HOST}/app/${appId}/project/workflow`}
          target="_blank"
        >
          流程管理
        </Link>
      ),
      key: "workflow",
      icon: <GatewayOutlined />,
    },
    {
      type: "divider",
    },
    {
      label: (
        <Link to={`${VITE_SAAS_HOST}/saas/user/profile`} target="_blank">
          用户中心
        </Link>
      ),
      key: "user",
      icon: <UserOutlined />,
    },
    {
      type: "divider",
    },
    {
      label: "退出登录",
      key: "login",
      icon: <LogoutOutlined />,
    },
  ];

  const onSave = async () => {
    onBtnHandle("save");
  };

  const onPreview = async () => {
    naviagate(`/app/${appId}/workflow/${workflowId}/preview`);
  };

  const onConfirmClear = () => {
    modal.confirm({
      title: "系统提醒",
      content: "即将清空流程且无法恢复，您确认清除吗？",
      onOk: onSureClear,
    });
  };

  const onSureClear = () => {
    onBtnHandle("clear");
  };

  const handleExportClick = (e) => {
    onBtnHandle && onBtnHandle(e?.key);
  };

  return (
    <Flex justify="space-between" align="center" className="design-header">
      <Button
        type="text"
        icon={
          <Avatar
            size={24}
            shape="square"
            src={<img src={logoSvg} alt="avatar" />}
          />
        }
      >
        ASTX
      </Button>

      <Flex justify="space-between" align="center">
        <Space>
          {mode === "design" && (
            <Space>
              <Button type="text" icon={<SaveOutlined />} onClick={onSave}>
                保存
              </Button>
              <Dropdown
                menu={{ items: exportItems, onClick: handleExportClick }}
              >
                <Button type="text" icon={<ExportOutlined />}>
                  导出
                </Button>
              </Dropdown>
              <Button type="text" icon={<EyeOutlined />} onClick={onPreview}>
                预览
              </Button>
              <Button
                type="text"
                icon={<RestOutlined />}
                onClick={onConfirmClear}
              >
                清空
              </Button>
              <Divider type="vertical"></Divider>
            </Space>
          )}

          <Dropdown menu={{ items }}>
            <Button
              type="text"
              icon={
                <Avatar
                  size={24}
                  shape="square"
                  src={<img src={logoSvg} alt="avatar" />}
                />
              }
            >
              winyh
            </Button>
          </Dropdown>
        </Space>
      </Flex>
    </Flex>
  );
};

export default Head;
