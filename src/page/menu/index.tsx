import React, { useEffect, useState } from 'react';
import { Table, Button, Popconfirm, Space, ConfigProvider, Form, Input, Row, Col, Flex, Tooltip, Modal, Select, TreeSelect, Switch } from 'antd';
import type { TablePaginationConfig, FilterValue, SorterResult, TableCurrentDataSource } from 'antd/lib/table/interface';
import { EditOutlined, DeleteOutlined, PlusOutlined, HomeOutlined, SearchOutlined, FolderOpenOutlined, LinkOutlined } from '@ant-design/icons';
import { getClient } from '@/client';
import { message } from "@/store/hooks"
import IconSelect from '@/component/IconSelect';
import IconRender from '@/component/IconRender';
import { useNavigate, useParams } from 'react-router-dom';

const client = getClient();

interface MenuItem {
  id: string;
  name: string;
  path: string;
  icon: string;
  type: string;
  menu_type: string;
  parent_id: string | null;
  sort: number;
  status: 0 | 1;
  created_at: string;
  updated_at: string;
  children?: MenuItem[];
  page_code?: string;
}

interface PageItem {
  id: string;
  name: string;
  code: string;
  created_at: string;
  updated_at: string;
}

interface PaginationParams {
  current: number;
  pageSize: number;
  total: number;
}

interface SearchParams {
  name?: string;
  path?: string;
  type?: string;
  menu_type?: string;
  status?: number;
}

interface TreeNode {
  label: string;
  value: string;
  children?: TreeNode[];
}

const MenuList = () => {
  const { appId, pageId } = useParams();
  const [dataSource, setDataSource] = useState<MenuItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  const [form] = Form.useForm();
  const [modalForm] = Form.useForm();
  const [modalVisible, setModalVisible] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [currentRecord, setCurrentRecord] = useState<MenuItem | null>(null);
  const [homeItem, setHomeItem] = useState<MenuItem | null>(null);
  const [pagination, setPagination] = useState<PaginationParams>({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [parentOptions, setParentOptions] = useState<TreeNode[]>([]);
  const [pageOptions, setPageOptions] = useState<{ label: string; value: string }[]>([]);
  const [pageLoading, setPageLoading] = useState(false);
  const [pageSearchValue, setPageSearchValue] = useState('');
  const [fullTreeDataSource, setFullTreeDataSource] = useState<MenuItem[]>([]);

  const navigate = useNavigate()

  // 导航类型选项
  const typeOptions = [
    { label: 'PC导航', value: 'PC导航' },
    { label: 'H5导航', value: 'H5导航' },
    { label: 'App导航', value: 'App导航' },
    { label: 'Mini导航', value: 'Mini导航' },
    { label: '底部导航', value: '底部导航' },
  ];

  // 菜单类型选项
  const menuTypeOptions = [
    { label: '菜单', value: 'menu' },
    { label: '目录', value: 'directory' },
  ];

  // 状态选项
  const statusOptions = [
    { label: '启用', value: 1 },
    { label: '禁用', value: 0 },
  ];

  // 获取图标组件
  const getIconComponent = (icon: string) => {
    if (!icon) return null;
    return <Button type='text' icon={<IconRender iconName={icon} />} />;
  };

  // 获取完整菜单列表并构建树形结构
  const fetchFullTreeData = async (searchParams?: SearchParams) => {
    setLoading(true);
    try {
      // 从后端获取所有符合搜索条件的菜单数据
      const menus = await client.database.read('menu', searchParams);
      const homeItem = menus?.find(item => item?.is_home)
      const treeData = buildTree(menus);  // 将扁平数据转换为完整的树形结构
      setFullTreeDataSource(treeData); // 存储完整的树形数据
      setHomeItem(homeItem) // 存储首页菜单

      setPagination(prev => ({
        ...prev,
        total: treeData.length, // 总数为顶层菜单的数量
        current: 1 // 搜索后重置到第一页
      }));
    } catch (error) {
      message.error('获取菜单列表失败');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // 构建树形结构
  const buildTree = (items: MenuItem[]): MenuItem[] => {
    const itemMap = new Map(items.map(item => [item.id, { ...item }]));
    const result: MenuItem[] = [];

    items.forEach(item => {
      const node = itemMap.get(item.id)!;
      if (item.parent_id && item.parent_id !== '0') {
        const parent = itemMap.get(item.parent_id);
        if (parent) {
          parent.children = parent.children || [];
          parent.children.push(node);
        } else {
          // 如果父节点不存在，则将其视为顶级节点
          result.push(node);
        }
      } else {
        result.push(node);
      }
    });

    // Recursive function to sort children and clean empty ones
    const sortAndCleanRecursively = (nodes: MenuItem[]): MenuItem[] => {
      // Sort current level in place
      nodes.sort((a, b) => a.sort - b.sort);

      // Create new objects and process children recursively
      return nodes.map(node => {
        const newNode = { ...node }; // Create a new object to avoid direct mutation of original references
        if (newNode.children && newNode.children.length > 0) {
          // Recursively sort and clean children
          newNode.children = sortAndCleanRecursively(newNode.children);
        } else if (newNode.children && newNode.children.length === 0) {
          // Clean up empty children array
          delete newNode.children;
        }
        return newNode;
      });
    };

    // Start the recursive sort and clean process from the top-level nodes
    return sortAndCleanRecursively(result);
  };

  // 初始加载数据
  useEffect(() => {
    fetchFullTreeData();
  }, []);

  // 客户端分页：当完整树形数据或分页参数变化时，更新 dataSource
  useEffect(() => {
    const { current, pageSize } = pagination;
    const startIndex = (current - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedData = fullTreeDataSource.slice(startIndex, endIndex);
    setDataSource(paginatedData);
  }, [fullTreeDataSource, pagination.current, pagination.pageSize]);

  const handleTableChange = (newPagination: TablePaginationConfig, filters: Record<string, FilterValue | null>, sorter: SorterResult<MenuItem> | SorterResult<MenuItem>[], extra: TableCurrentDataSource<MenuItem>) => {
    setPagination(prev => ({
      ...prev,
      current: newPagination.current || 1,
      pageSize: newPagination.pageSize || 10,
    }));
  };

  const handleSearch = (values: SearchParams) => {
    fetchFullTreeData(values); // 触发重新获取完整树形数据
  };

  const handleReset = () => {
    form.resetFields();
    fetchFullTreeData(); // 重置搜索并重新获取完整树形数据
  };

  const handleAdd = () => {
    setModalTitle('新增菜单');
    setCurrentRecord(null);
    modalForm.resetFields();
    // 设置默认值
    modalForm.setFieldsValue({
      parent_id: '0', // 默认为顶级目录
      status: 1, // 默认启用
      sort: 0, // 默认排序
      type: 'PC导航', // 默认导航类型
      menu_type: 'menu', // 默认菜单类型
      page_code: undefined, // 清空关联页面
      icon: undefined, // 清空图标
      path: undefined, // 清空路径
      name: undefined, // 清空名称
    });
    setModalVisible(true);
  };

  const handleGoPreview = () => {
    if (homeItem) {
      navigate(`/app/${appId}/page/${homeItem?.code}/runtime`);
    } else {
      message.warning('请先添加首页菜单');
    }
  }

  const handleEdit = (record: MenuItem) => {
    setModalTitle('修改菜单');
    setCurrentRecord(record);
    modalForm.setFieldsValue({
      ...record,
      parent_id: record.parent_id || '0', // 如果 parent_id 为 null，设置为 '0'
    });
    setModalVisible(true);
  };

  const handleModalOk = async () => {
    try {
      const values = await modalForm.validateFields();

      // 处理 parent_id
      const parentId = values.parent_id === '0' ? null : values.parent_id;

      if (currentRecord) {
        // 更新菜单
        await client.database.update('menu', currentRecord.id, {
          ...values,
          parent_id: parentId
        });
        message.success('更新成功');
      } else {
        // 创建菜单
        await client.database.create('menu', {
          ...values,
          parent_id: parentId,
          sort: values.sort || 0,
          status: values.status || 1
        });
        message.success('创建成功');
      }

      setModalVisible(false);
      fetchFullTreeData(); // 刷新列表
    } catch (error) {
      message.error(currentRecord ? '更新失败' : '创建失败');
      console.error(error);
    }
  };

  const handleModalCancel = () => {
    setModalVisible(false);
    modalForm.resetFields();
  };

  const handleDelete = async (record: MenuItem) => {
    try {
      await client.database.delete('menu', record.id);
      message.success('删除成功');
      fetchFullTreeData(); // 刷新列表
    } catch (error) {
      message.error('删除失败');
      console.error(error);
    }
  };

  // 获取所有可选的父级目录
  const getParentOptions = (data: MenuItem[], currentId?: string): TreeNode[] => {
    // 将扁平数据转换为树形结构
    const flatData = data.reduce((acc: MenuItem[], item) => {
      acc.push(item);
      if (item.children) {
        acc.push(...item.children);
      }
      return acc;
    }, []);

    const options: TreeNode[] = [
      { label: '顶级目录', value: '0' }
    ];

    // 过滤掉当前节点及其子节点
    const filteredData = flatData.filter(item => {
      if (item.id === currentId) return false;
      if (currentId && item.parent_id === currentId) return false;
      return true;
    });

    // 构建选项
    const menuOptions = filteredData.map(item => ({
      label: item.name,
      value: item.id,
      disabled: item.menu_type === 'menu' // 菜单类型不能作为父级
    }));

    if (menuOptions.length > 0) {
      options[0].children = menuOptions;
    }

    return options;
  };

  // 更新父级选项
  useEffect(() => {
    setParentOptions(getParentOptions(dataSource, currentRecord?.id));
  }, [dataSource, currentRecord]);

  // 处理新增子项
  const handleAddChild = (record: MenuItem) => {
    setModalTitle('新增子项');
    setCurrentRecord(null);
    modalForm.resetFields();
    modalForm.setFieldsValue({
      parent_id: record.id,
      type: record.type,
      status: 1,
      sort: 0,
      menu_type: 'menu', // 默认菜单类型
    });
    setModalVisible(true);
  };

  // 获取页面列表
  const fetchPages = async (searchValue?: string) => {
    setPageLoading(true);
    try {
      const pages = await client.database.read('page', searchValue ? {
        name: searchValue
      } : undefined);

      const options = pages.map((page: PageItem) => ({
        label: page.name,
        value: page.code,
        code: page.code
      }));

      setPageOptions(options);
    } catch (error) {
      message.error('获取页面列表失败');
      console.error(error);
    } finally {
      setPageLoading(false);
    }
  };

  // 初始加载页面列表
  useEffect(() => {
    fetchPages();
  }, []);

  // 处理页面搜索
  const handlePageSearch = (value: string) => {
    setPageSearchValue(value);
    fetchPages(value);
  };

  // 处理页面选择
  const handlePageSelect = (value: string, option: any) => {
    // 可以在这里处理选择后的逻辑
    console.log('Selected page:', value, option);
  };

  // 设置首页方法
  const handleSetHome = async (record: MenuItem) => {
    try {
      // 先将所有页面 is_home 设为 false
      await client.database.batchUpdate('menu', [
        { where: { is_home: true }, data: { is_home: false } } // where 为空对象表示不加条件，更新所有数据
      ]);
      // 再将当前菜单 is_home 设为 true
      await client.database.batchUpdate('menu', [
        { where: { id: record.id }, data: { is_home: true } } // where 为空对象表示不加条件，更新所有数据
      ]);
      message.success('设置首页成功');
      fetchFullTreeData(); // 刷新列表
    } catch (error) {
      message.error('设置首页失败');
      console.error(error);
    }
  };

  const columns = [
    {
      title: '菜单名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '图标',
      dataIndex: 'icon',
      key: 'icon',
      render: (icon: string) => getIconComponent(icon),
    },
    {
      title: '导航类型',
      dataIndex: 'type',
      key: 'type',
    },
    {
      title: '菜单类型',
      dataIndex: 'menu_type',
      key: 'menu_type',
      render: (text: string) => <Button type='text' icon={text === "directory" ? <FolderOpenOutlined /> : <LinkOutlined />}>{text === "directory" ? "目录" : "菜单"}</Button>
    },
    {
      title: '是否首页',
      dataIndex: 'is_home',
      key: 'is_home',
      render: (_: any, record: MenuItem) => (
        record.menu_type === 'menu' ? (
          <Switch
            checked={!!record?.is_home}
            checkedChildren="是"
            unCheckedChildren="否"
            onChange={() => handleSetHome(record)}
          />
        ) : null // 目录等非菜单类型不渲染
      )
    },
    {
      title: '排序',
      dataIndex: 'sort',
      key: 'sort',
      width: 80,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: number) => (
        <span style={{ color: status ? '#52c41a' : '#ff4d4f' }}>
          {status ? '启用' : '禁用'}
        </span>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 280,
      render: (_: unknown, record: MenuItem) => (
        <Space>
          <Button
            type="text"
            icon={<PlusOutlined />}
            disabled={record.menu_type !== "directory"}
            onClick={() => handleAddChild(record)}
          >
            新增子项
          </Button>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            修改
          </Button>
          <Popconfirm
            title="删除确认"
            description="确定要删除这条记录吗？"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      {showSearch && (
        <div style={{ padding: "0 16px" }}>
          <ConfigProvider theme={{
            components: {
              Form: {
                itemMarginBottom: 0,
              },
            },
          }}>
            <Form
              form={form}
              onFinish={handleSearch}
              style={{ marginBottom: 16 }}
            >
              <Row gutter={16}>
                <Col span={6}>
                  <Form.Item name="name" label="菜单名称">
                    <Input placeholder="请输入菜单名称" />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item name="type" label="导航类型">
                    <Select
                      placeholder="请选择导航类型"
                      options={typeOptions}
                      allowClear
                    />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item name="menu_type" label="菜单类型">
                    <Select
                      placeholder="请选择菜单类型"
                      options={menuTypeOptions}
                      allowClear
                    />
                  </Form.Item>
                </Col>
                <Col span={6} style={{ textAlign: 'center' }}>
                  <Space>
                    <Button onClick={handleReset}>重置</Button>
                    <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                      查询
                    </Button>
                  </Space>
                </Col>
              </Row>
            </Form>
          </ConfigProvider>
        </div>
      )}

      <Table
        title={() => (
          <Flex justify="space-between" align="center">
            <Space size={16}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAdd}
              >
                新增
              </Button>
              <Tooltip title="跳转当前应用首页">
                <Button
                  icon={<HomeOutlined />}
                  onClick={handleGoPreview}
                >
                  应用
                </Button>
              </Tooltip>

            </Space>
            <Tooltip title={showSearch ? '收起查询' : '展开查询'}>
              <Button
                type="primary"
                shape='circle'
                icon={<SearchOutlined />}
                onClick={() => setShowSearch(!showSearch)}
              >
              </Button>
            </Tooltip>
          </Flex>
        )}
        dataSource={dataSource}
        columns={columns}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条`
        }}
        loading={loading}
        onChange={handleTableChange}
        rowKey="id"
      />

      <Modal
        title={modalTitle}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={600}
        destroyOnHidden
      >
        <Form
          form={modalForm}
          layout="vertical"
        >
          <Form.Item
            name="parent_id"
            label="上级目录"
            rules={[{ required: true, message: '请选择上级目录' }]}
          >
            <TreeSelect
              placeholder="请选择上级目录"
              treeData={parentOptions}
              treeDefaultExpandAll
              disabled={!!currentRecord?.children?.length}
              allowClear
              showSearch
              treeLine
              treeNodeFilterProp="label"
              style={{ width: '100%' }}
            />
          </Form.Item>
          <Form.Item
            name="name"
            label="菜单名称"
            rules={[{ required: true, message: '请输入菜单名称' }]}
          >
            <Input placeholder="请输入菜单名称" />
          </Form.Item>
          <Form.Item
            name="type"
            label="导航类型"
            rules={[{ required: true, message: '请选择导航类型' }]}
          >
            <Select
              placeholder="请选择导航类型"
              options={typeOptions}
            />
          </Form.Item>
          <Form.Item
            name="menu_type"
            label="菜单类型"
            rules={[{ required: true, message: '请选择菜单类型' }]}
          >
            <Select
              placeholder="请选择菜单类型"
              options={menuTypeOptions}
            />
          </Form.Item>
          <Form.Item
            name="icon"
            label="图标"
          >
            <IconSelect
              onChange={(value, iconSource) => {
                modalForm.setFieldsValue({ icon: value, icon_source: iconSource });
              }}
            />
          </Form.Item>
          <Form.Item
            name="icon_source"
            label="图标来源"
            style={{ display: 'none' }}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="sort"
            label="排序"
            rules={[{ required: true, message: '请输入排序' }]}
          >
            <Input type="number" placeholder="请输入排序" />
          </Form.Item>
          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select
              placeholder="请选择状态"
              options={statusOptions}
            />
          </Form.Item>
          <Form.Item
            name="page_code"
            label="关联页面"
            tooltip="选择此菜单关联的页面"
          >
            <Select
              placeholder="请选择关联页面"
              allowClear
              showSearch
              loading={pageLoading}
              options={pageOptions}
              filterOption={false}
              onSearch={handlePageSearch}
              onChange={handlePageSelect}
              virtual
              listHeight={256}
              style={{ width: '100%' }}
              optionFilterProp="label"
              optionLabelProp="label"
              notFoundContent={pageLoading ? '加载中...' : '未找到页面'}
            />
          </Form.Item>
          <Form.Item
            noStyle
            shouldUpdate={(prev, curr) => prev.menu_type !== curr.menu_type}
          >
            {({ getFieldValue }) =>
              getFieldValue('menu_type') === 'menu' ? (
                <Form.Item name="is_home" label="是否首页" valuePropName="checked">
                  <Switch checkedChildren="是" unCheckedChildren="否" />
                </Form.Item>
              ) : null
            }
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default MenuList;