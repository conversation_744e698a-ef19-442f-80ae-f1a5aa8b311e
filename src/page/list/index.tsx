import React, { useEffect, useState } from 'react';
import { Table, Button, Popconfirm, Space, Divider, Form, Input, Card, Row, Col, Flex, Tooltip, Modal, Select, ConfigProvider } from 'antd';
import { EditOutlined, DeleteOutlined, EyeOutlined, CodeOutlined, PlusOutlined, SearchOutlined } from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import { message } from "@/store/hooks"
import dayjs from "dayjs"
import { getClient } from '@/client';

const client = getClient();

interface TableItem {
  id: string;
  name: string;
  code: string;
  description: string;
  category: string;
  create_at: string;
  update_at: string;
}

interface PaginationParams {
  current: number;
  pageSize: number;
  total: number;
}

interface SearchParams {
  name?: string;
  code?: string;
  category?: string;
}

const List = () => {
  const navigate = useNavigate();
  const { appId, pageId } = useParams();
  const [dataSource, setDataSource] = useState<TableItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  const [form] = Form.useForm();
  const [modalForm] = Form.useForm();
  const [modalVisible, setModalVisible] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [currentRecord, setCurrentRecord] = useState<TableItem | null>(null);
  const [pagination, setPagination] = useState<PaginationParams>({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  const engineOptions = [
    { label: 'React', value: 'react' },
    { label: 'Next', value: 'next' },
    { label: 'Vue', value: 'vue' },
    { label: '微信小程序', value: 'weixin' },
    { label: 'Android', value: 'android' },
    { label: 'IOS', value: 'ios' },
    { label: '鸿蒙OS', value: 'huawei' },
    { label: 'Flutter', value: 'flutter' },
  ];

  const getData = async (params?: any) => {
    setLoading(true);
    try {
      const result = await client.database.readList('page', {
        ...params,
        current: params?.current || pagination.current,
        pageSize: params?.pageSize || pagination.pageSize
      });
      setDataSource(result.data);
      setPagination({
        current: result.current,
        pageSize: result.pageSize,
        total: result.total
      });
    } catch (error) {
      message.error('获取页面列表失败');
      console.error(error);
    } finally {
      setLoading(false);
    }
  }

  useEffect(() => {
    getData();
  }, []);

  const handleTableChange = (newPagination: any) => {
    getData({
      current: newPagination.current,
      pageSize: newPagination.pageSize
    });
  };

  const handleSearch = (values: SearchParams) => {
    getData({
      ...values,
      current: 1
    });
  };

  const handleReset = () => {
    form.resetFields();
    getData({
      current: 1
    });
  };

  const handleAdd = () => {
    setModalTitle('新增页面');
    setCurrentRecord(null);
    modalForm.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (record: TableItem) => {
    setModalTitle('修改页面');
    setCurrentRecord(record);
    modalForm.setFieldsValue(record);
    setModalVisible(true);
  };

  const handleModalOk = async () => {
    try {
      const values = await modalForm.validateFields();
      const now = new Date().toISOString();

      if (currentRecord) {
        // 更新页面
        await client.database.update('page', currentRecord.id, {
          ...values,
          updated_at: now
        });
        message.success('更新成功');
      } else {
        // 创建页面
        await client.database.create('page', {
          ...values,
          engine: 'react', // 默认引擎
        });
        message.success('创建成功');
      }

      setModalVisible(false);
      getData(); // 刷新列表
    } catch (error) {
      message.error(currentRecord ? '更新失败' : '创建失败');
      console.error(error);
    }
  };

  const handleModalCancel = () => {
    setModalVisible(false);
    modalForm.resetFields();
  };

  const handleDelete = async (record: TableItem) => {
    try {
      await client.database.delete('page', record.id);
      message.success('删除成功');
      getData(); // 刷新列表
    } catch (error) {
      message.error('删除失败');
      console.error(error);
    }
  };

  const handleDesign = (record: TableItem) => {
    navigate(`/app/${appId}/page/${record.code}/design`);
  };

  const handlePreview = (record: TableItem) => {
    navigate(`/app/${appId}/page/${record.code}/preview`);
  };

  const columns = [
    {
      title: '页面名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '标识',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: '页面分类',
      dataIndex: 'category',
      key: 'category',
    },
    {
      title: '页面描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '渲染引擎',
      dataIndex: 'engine',
      key: 'engine',
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text: string) => dayjs(text).format("YYYY-MM-DD HH:mm:ss")
    },
    {
      title: '修改时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      render: (text: string) => dayjs(text).format("YYYY-MM-DD HH:mm:ss")
    },
    {
      title: '操作',
      key: 'action',
      width: 400,
      render: (_: unknown, record: TableItem) => (
        <Space split={<Divider type="vertical" />}>
          <Space>
            <Button
              type="text"
              icon={<CodeOutlined />}
              onClick={() => handleDesign(record)}
            >
              设计
            </Button>
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handlePreview(record)}
            >
              预览
            </Button>
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            >
              修改
            </Button>
            <Popconfirm
              title="删除确认"
              description="确定要删除这条记录吗？"
              onConfirm={() => handleDelete(record)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              >
                删除
              </Button>
            </Popconfirm>
          </Space>
        </Space>
      ),
    },
  ];

  return (
    <div>
      {showSearch && (
        <div style={{ padding: "0 16px" }}>
          <ConfigProvider theme={{
            components: {
              Form: {
                itemMarginBottom: 0,
              },
            },
          }}>
            <Form
              form={form}
              onFinish={handleSearch}
              style={{ marginBottom: 16 }}
            >
              <Row gutter={16}>
                <Col span={6}>
                  <Form.Item name="name" label="页面名称">
                    <Input placeholder="请输入页面名称" />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item name="engine" label="渲染引擎">
                    <Select
                      placeholder="请选择渲染引擎"
                      options={engineOptions}
                      allowClear
                    />
                  </Form.Item>
                </Col>
                <Col span={6} style={{ textAlign: 'center' }}>
                  <Space>
                    <Button onClick={handleReset}>重置</Button>
                    <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                      查询
                    </Button>
                  </Space>
                </Col>
              </Row>
            </Form>
          </ConfigProvider>
        </div>
      )}

      <Table
        title={() => (
          <Flex justify="space-between" align="center">
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              新增
            </Button>
            <Tooltip title={showSearch ? '收起查询' : '展开查询'}>
              <Button
                type="primary"
                shape='circle'
                icon={<SearchOutlined />}
                onClick={() => setShowSearch(!showSearch)}
              >
              </Button>
            </Tooltip>
          </Flex>
        )}
        rowKey="id"
        dataSource={dataSource}
        columns={columns}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条`
        }}
        loading={loading}
        onChange={handleTableChange}
      />

      <Modal
        title={modalTitle}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={600}
        destroyOnHidden
      >
        <Form
          form={modalForm}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="页面名称"
            rules={[{ required: true, message: '请输入页面名称' }]}
          >
            <Input placeholder="请输入页面名称" />
          </Form.Item>
          <Form.Item
            name="engine"
            label="渲染引擎"
          >
            <Select
              placeholder="请选择渲染引擎"
              options={engineOptions}
            />
          </Form.Item>
          <Form.Item
            name="description"
            label="页面描述"
          >
            <Input.TextArea placeholder="请输入页面描述" rows={4} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default List;
