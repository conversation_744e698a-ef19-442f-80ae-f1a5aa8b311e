import { useState, useEffect } from 'react';
import { Card, Space, Select, Tree, Checkbox, Table, Alert, Input, Spin, Button, Flex, Badge } from 'antd';
import { Splitter } from 'antd';
import type { Key } from 'antd/lib/table/interface';
import { FolderOpenOutlined, LinkOutlined, SearchOutlined, FolderOutlined } from '@ant-design/icons';
import { getClient } from '@/client';
import { message } from "@/store/hooks";

const client = getClient();

interface MenuItem {
  id: string;
  name: string;
  path: string;
  icon: string;
  type: string;
  menu_type: string;
  parent_id: string | null;
  sort: number;
  status: 0 | 1;
  created_at: string;
  updated_at: string;
  children?: MenuItem[];
  page_code?: string;
  key?: string;
  title?: string;
  pageId?: string;
  fields?: string[];
}

const Permission = () => {
  // 模拟数据
  const roles = [
    { id: '1', name: '管理员', description: '拥有所有权限' },
    { id: '2', name: '编辑员', description: '可以编辑内容' },
    { id: '3', name: '访客', description: '只能查看内容' },
  ];

  // 菜单数据
  const [menuPages, setMenuPages] = useState<MenuItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [menuSearchValue, setMenuSearchValue] = useState('');
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);

  // 字段权限数据
  const [fieldPermissions, setFieldPermissions] = useState<Record<string, Record<string, any>>>({
    '1': {
      'dashboard-analysis': {
        'total_users': { visible: true, writable: true, other: 'export' },
        'active_sessions': { visible: true, writable: true, other: 'export' },
      },
      'dashboard-monitor': {
        'system_status': { visible: true, writable: true, other: 'export' },
        'alerts': { visible: true, writable: true, other: 'export' },
      },
      'users-list': {
        'username': { visible: true, writable: true, other: 'export' },
        'email': { visible: true, writable: true, other: 'export' },
        'status': { visible: true, writable: true, other: 'export' },
      },
      'users-profile': {
        'profile': { visible: true, writable: true, other: 'export' },
        'settings': { visible: true, writable: true, other: 'export' },
        'logs': { visible: true, writable: true, other: 'export' },
      }
    },
    '2': {
      'dashboard-analysis': {
        'total_users': { visible: true, writable: false, other: 'export' },
        'active_sessions': { visible: true, writable: false, other: 'none' },
      },
      'dashboard-monitor': {
        'system_status': { visible: true, writable: false, other: 'none' },
        'alerts': { visible: true, writable: false, other: 'none' },
      },
      'users-list': {
        'username': { visible: true, writable: true, other: 'export' },
        'email': { visible: true, writable: true, other: 'none' },
        'status': { visible: true, writable: false, other: 'none' },
      },
      'users-profile': {
        'profile': { visible: true, writable: true, other: 'none' },
        'settings': { visible: true, writable: false, other: 'none' },
        'logs': { visible: false, writable: false, other: 'none' },
      }
    },
    '3': {
      'dashboard-analysis': {
        'total_users': { visible: true, writable: false, other: 'none' },
        'active_sessions': { visible: false, writable: false, other: 'none' },
      },
      'dashboard-monitor': {
        'system_status': { visible: false, writable: false, other: 'none' },
        'alerts': { visible: false, writable: false, other: 'none' },
      },
      'users-list': {
        'username': { visible: true, writable: false, other: 'none' },
        'email': { visible: false, writable: false, other: 'none' },
        'status': { visible: true, writable: false, other: 'none' },
      },
      'users-profile': {
        'profile': { visible: true, writable: false, other: 'none' },
        'settings': { visible: false, writable: false, other: 'none' },
        'logs': { visible: false, writable: false, other: 'none' },
      }
    }
  });

  // 数据范围
  const [dataScopes, setDataScopes] = useState<Record<string, Record<string, string>>>({
    '1': {
      'dashboard-analysis': 'all',
      'dashboard-monitor': 'all',
      'users-list': 'all',
      'users-profile': 'all'
    },
    '2': {
      'dashboard-analysis': 'department',
      'dashboard-monitor': 'department',
      'users-list': 'department',
      'users-profile': 'department'
    },
    '3': {
      'dashboard-analysis': 'self',
      'dashboard-monitor': 'self',
      'users-list': 'self',
      'users-profile': 'self'
    },
  });

  const [selectedRole, setSelectedRole] = useState<string | null>("1");
  const [selectedMenu, setSelectedMenu] = useState<string | null>(null);

  // 添加展开/折叠状态
  const [isExpanded, setIsExpanded] = useState(true);

  // 获取菜单数据
  const fetchMenuData = async (searchValue?: string) => {
    setLoading(true);
    try {
      // 从后端获取菜单数据
      const menus = await client.database.read('menu', searchValue ? {
        name: searchValue
      } : undefined);
      
      // 构建树形结构
      const treeData = buildMenuTree(menus);
      
      // 设置菜单数据
      setMenuPages(treeData);
      
      // 初始化展开节点
      const keys = treeData.map(item => item.id);
      setExpandedKeys(keys);
    } catch (error) {
      message.error('获取菜单数据失败');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // 构建菜单树形结构
  const buildMenuTree = (items: MenuItem[]): MenuItem[] => {
    const itemMap = new Map(items.map(item => [item.id, {
      ...item, 
      key: item.key || item.id, 
      title: item.name,
      pageId: item.page_code ? `page_${item.page_code}` : undefined,
      // 为每个菜单项添加默认字段
      fields: ['field1', 'field2', 'field3'] // 实际应用中应该从后端获取
    }]));
    
    const result: MenuItem[] = [];

    items.forEach(item => {
      const node = itemMap.get(item.id)!;
      if (item.parent_id && item.parent_id !== '0') {
        const parent = itemMap.get(item.parent_id);
        if (parent) {
          parent.children = parent.children || [];
          parent.children.push(node);
        } else {
          // 如果父节点不存在，则将其视为顶级节点
          result.push(node);
        }
      } else {
        result.push(node);
      }
    });

    // 递归排序函数
    const sortAndCleanRecursively = (nodes: MenuItem[]): MenuItem[] => {
      // 按排序字段排序
      nodes.sort((a, b) => a.sort - b.sort);

      // 创建新对象并递归处理子节点
      return nodes.map(node => {
        const newNode = { ...node };
        if (newNode.children && newNode.children.length > 0) {
          // 递归排序子节点
          newNode.children = sortAndCleanRecursively(newNode.children);
        } else if (newNode.children && newNode.children.length === 0) {
          // 清理空子节点数组
          delete newNode.children;
        }
        return newNode;
      });
    };

    // 从顶层节点开始递归排序
    return sortAndCleanRecursively(result);
  };

  // 初始加载数据
  useEffect(() => {
    fetchMenuData();
  }, []);

  // 权限绑定部分
  const handleRoleSelectForBinding = (roleId: string) => {
    setSelectedRole(roleId);
  };

  const handleMenuSelect = (selectedKeys: Key[]) => {
    if (selectedKeys.length > 0) {
      setSelectedMenu(selectedKeys[0] as string);
    }
  };

  // 处理菜单搜索
  const handleMenuSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setMenuSearchValue(value);
    fetchMenuData(value);
  };

  // 获取菜单树数据
  const getMenuTreeData = () => {
    const loop = (data: MenuItem[]): any[] =>
      data.map(item => {
        const isHome = (item as any).is_home;
        const title = (
          <span>
            {item.menu_type === 'directory' ? (
              <FolderOpenOutlined style={{ marginRight: 8 }} />
            ) : <LinkOutlined style={{ marginRight: 8 }} />}
            {item.name}
            {isHome ? (
              <Badge status='processing' style={{ marginLeft: 8 }} />
            ) : null}
          </span>
        );
        if (item.children) {
          return {
            key: item.key || item.id,
            title,
            children: loop(item.children),
          };
        }
        return {
          key: item.key || item.id,
          title,
        };
      });
    return loop(menuPages);
  };
  
  // 处理字段权限变更
  const handleFieldPermissionChange = (menuKey: string, fieldName: string, permType: string, value: boolean | string) => {
    if (selectedRole) {
      setFieldPermissions(prev => {
        const newPermissions = { ...prev };
        if (!newPermissions[selectedRole]) {
          newPermissions[selectedRole] = {};
        }
        if (!newPermissions[selectedRole][menuKey]) {
          newPermissions[selectedRole][menuKey] = {};
        }
        if (!newPermissions[selectedRole][menuKey][fieldName]) {
          newPermissions[selectedRole][menuKey][fieldName] = { visible: false, writable: false, other: 'none' };
        }
        newPermissions[selectedRole][menuKey][fieldName] = {
          ...newPermissions[selectedRole][menuKey][fieldName],
          [permType]: value
        };
        return newPermissions;
      });
    }
  };

  // 处理全选字段权限
  const handleSelectAllFieldPermission = (menuKey: string, permType: string, value: boolean) => {
    if (selectedRole) {
      const fields = getSelectedMenuFields();
      const newPermissions = { ...fieldPermissions };

      if (!newPermissions[selectedRole]) {
        newPermissions[selectedRole] = {};
      }

      if (!newPermissions[selectedRole][menuKey]) {
        newPermissions[selectedRole][menuKey] = {};
      }

      fields.forEach(field => {
        if (!newPermissions[selectedRole][menuKey][field]) {
          newPermissions[selectedRole][menuKey][field] = { visible: false, writable: false, other: 'none' };
        }

        // 如果是可写权限，只有当字段可见时才能设置
        if (permType === 'writable') {
          if (newPermissions[selectedRole][menuKey][field].visible) {
            newPermissions[selectedRole][menuKey][field][permType] = value;
          }
        } else {
          newPermissions[selectedRole][menuKey][field][permType] = value;
        }
      });

      setFieldPermissions(newPermissions);
    }
  };

  // 判断是否所有字段都有某个权限
  const areAllFieldsWithPermission = (menuKey: string, permType: string): boolean => {
    if (!selectedRole) return false;

    const fields = getSelectedMenuFields();
    const rolePermissions = fieldPermissions[selectedRole]?.[menuKey] || {};

    if (fields.length === 0) return false;

    if (permType === 'writable') {
      // 对于可写权限，只检查可见的字段
      const visibleFields = fields.filter(field => rolePermissions[field]?.visible);
      if (visibleFields.length === 0) return false;
      return visibleFields.every(field => rolePermissions[field]?.writable);
    }

    return fields.every(field => rolePermissions[field]?.[permType]);
  };

  // 处理数据范围变更
  const handleDataScopeChange = (menuKey: string, value: string) => {
    if (selectedRole) {
      setDataScopes(prev => {
        const newScopes = { ...prev };
        if (!newScopes[selectedRole]) {
          newScopes[selectedRole] = {};
        }
        newScopes[selectedRole][menuKey] = value;
        return newScopes;
      });
    }
  };

  // 获取当前菜单的字段列表
  const getSelectedMenuFields = () => {
    if (!selectedMenu) return [];

    // 从扁平的菜单结构中查找字段
    const findMenuFields = (menus: MenuItem[]): string[] => {
      for (const menu of menus) {
        if (menu.key === selectedMenu || menu.id === selectedMenu) {
          return menu.fields || [];
        }
        if (menu.children) {
          const fields = findMenuFields(menu.children);
          if (fields.length > 0) return fields;
        }
      }
      return [];
    };

    return findMenuFields(menuPages);
  };

  // 获取所有节点的key
  const getAllKeys = (data: MenuItem[]): React.Key[] => {
    const keys: React.Key[] = [];
    const collect = (items: MenuItem[]) => {
      items.forEach(item => {
        if (item.key) keys.push(item.key);
        else if (item.id) keys.push(item.id);
        if (item.children) {
          collect(item.children);
        }
      });
    };
    collect(data);
    return keys;
  };
  
  // 添加展开/折叠所有节点的函数
  const handleExpandAll = () => {
    // 展开所有节点
    const allKeys = getAllKeys(menuPages);
    setExpandedKeys(allKeys);
    setIsExpanded(true);
  };

  const handleCollapseAll = () => {
    // 折叠所有节点，只保留顶级节点
    setExpandedKeys([]);
    setIsExpanded(false);
  };
  
  // 处理展开/折叠切换
  const toggleExpand = () => {
    if (isExpanded) {
      handleCollapseAll();
    } else {
      handleExpandAll();
    }
  };

  // 处理Tree的展开事件
  const handleExpand = (expandedKeys: React.Key[]) => {
    setExpandedKeys(expandedKeys);
  };

  // 生成字段权限配置区域
  const renderFieldPermissions = () => {
    if (!selectedRole || !selectedMenu) return <Space style={{ width: '100%', padding: 24 }}><Alert message="请先选择角色和导航菜单" type="warning" /></Space>;

    // 找到选中的菜单
    const findMenu = (menus: MenuItem[]): any => {
      for (const menu of menus) {
        if (menu.key === selectedMenu || menu.id === selectedMenu) {
          return menu;
        }
        if (menu.children) {
          const found = findMenu(menu.children);
          if (found) return found;
        }
      }
      return null;
    };

    const currentMenu = findMenu(menuPages);
    if (!currentMenu) return <div style={{ padding: 24 }}>未找到菜单信息</div>;

    const currentRoleFieldPermissions = fieldPermissions[selectedRole]?.[selectedMenu] || {};
    const currentDataScope = dataScopes[selectedRole]?.[selectedMenu] || 'self';
    const fields = getSelectedMenuFields();

    return (
      <Space direction="vertical" style={{ width: '100%', padding: 24 }} size="large">
        <Space direction="vertical" style={{ width: '100%' }}>
          <div style={{ padding: 0, fontSize: 16, fontWeight: 500, marginBottom: 16 }}>数据范围</div>
          <Select
            style={{ width: 280 }}
            value={currentDataScope}
            onChange={(value) => handleDataScopeChange(selectedMenu, value)}
            allowClear
            options={[
              { label: '全部数据', value: 'all' },
              { label: '本部门数据', value: 'department' },
              { label: '仅本人数据', value: 'self' }
            ]}
          />
        </Space>

        <div style={{ padding: 0, fontSize: 16, fontWeight: 500 }}>字段权限</div>

        <Card variant='outlined' styles={{ body: { padding: 0 }, header: { padding: "0 16px" } }}>
          <Table
            dataSource={fields.map(field => ({
              key: field,
              fieldName: field,
              permissions: currentRoleFieldPermissions[field] || { visible: false, writable: false, other: 'none' }
            }))}
            pagination={false}
            style={{ width: '100%' }}
            columns={[
              { title: '字段名称', dataIndex: 'fieldName', key: 'fieldName' },
              {
                title: () => (
                  <Space>
                    <Checkbox
                      checked={areAllFieldsWithPermission(selectedMenu, 'visible')}
                      onChange={e => handleSelectAllFieldPermission(selectedMenu, 'visible', e.target.checked)}
                    />
                    <span>可见</span>
                  </Space>
                ),
                key: 'visible',
                render: (_, record) => (
                  <Checkbox
                    checked={record.permissions.visible}
                    onChange={e => handleFieldPermissionChange(selectedMenu, record.fieldName, 'visible', e.target.checked)}
                  />
                )
              },
              {
                title: () => (
                  <Space>
                    <Checkbox
                      checked={areAllFieldsWithPermission(selectedMenu, 'writable')}
                      onChange={e => handleSelectAllFieldPermission(selectedMenu, 'writable', e.target.checked)}
                    />
                    <span>可写</span>
                  </Space>
                ),
                key: 'writable',
                render: (_, record) => (
                  <Checkbox
                    checked={record.permissions.writable}
                    onChange={e => handleFieldPermissionChange(selectedMenu, record.fieldName, 'writable', e.target.checked)}
                    disabled={!record.permissions.visible}
                  />
                )
              },
              {
                title: '其他操作',
                key: 'other',
                render: (_, record) => (
                  <Select
                    style={{ width: 120 }}
                    value={record.permissions.other || 'none'}
                    onChange={(value) => handleFieldPermissionChange(selectedMenu, record.fieldName, 'other', value)}
                    disabled={!record.permissions.visible}
                    allowClear
                    options={[
                      { label: '无', value: 'none' },
                      { label: '导出', value: 'export' },
                      { label: '导入', value: 'import' },
                      { label: '下载', value: 'download' },
                      { label: '打印', value: 'print' }
                    ]}
                  />
                )
              },
            ]}
          />
        </Card>
      </Space>
    );
  };

  return (
    <Splitter style={{ height: '100%', width: '100%' }}>
      <Splitter.Panel defaultSize="20%" min="20%" max="40%">
        <Space direction="vertical" size="large" style={{ padding: '16px', width: '100%' }}>
          <Select
            style={{ width: '100%' }}
            placeholder="请选择角色"
            options={roles.map(role => ({ label: role.name, value: role.id }))}
            onChange={handleRoleSelectForBinding}
            value={selectedRole}
            allowClear
          />

          <Flex style={{ width: '100%', marginBottom: 8 }} gap={18}>
            <Input
              placeholder="请输入菜单名称"
              allowClear
              style={{ flex: 1 }}
              onChange={handleMenuSearch}
              prefix={<SearchOutlined />}
              value={menuSearchValue}
            />
            <Button 
              icon={isExpanded ? <FolderOpenOutlined /> : <FolderOutlined />} 
              onClick={toggleExpand}
            >
              {isExpanded ? '折叠' : '展开'}全部
            </Button>
          </Flex>

          <Spin spinning={loading}>
            <Tree
              showLine
              onSelect={handleMenuSelect}
              selectedKeys={selectedMenu ? [selectedMenu] : []}
              treeData={getMenuTreeData()}
              expandedKeys={expandedKeys}
              onExpand={handleExpand}
              style={{ width: '100%' }}
            />
          </Spin>
        </Space>
      </Splitter.Panel>

      <Splitter.Panel>
        {renderFieldPermissions()}
      </Splitter.Panel>
    </Splitter>
  );
};

export default Permission;
