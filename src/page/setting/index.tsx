import { useState } from 'react';
import { Card, Form, Input, Button, message, Space } from 'antd';

const defaultSettings = {
  appName: '我的应用',
  appLogo: '',
  appDesc: '',
  appDomain: '',
  appICP: '',
};

const AppBaseSetting = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 假设从后端获取设置，这里用默认值
  const [settings, setSettings] = useState(defaultSettings);

  const handleFinish = async (values: typeof defaultSettings) => {
    setLoading(true);
    // 这里应调用后端保存接口
    setTimeout(() => {
      setSettings(values);
      setLoading(false);
      message.success('保存成功！');
    }, 800);
  };

  return (
    <Card variant="borderless" style={{ maxWidth: 600 }}>
      <Form
        form={form}
        layout="vertical"
        initialValues={settings}
        onFinish={handleFinish}
      >
        <Form.Item label="应用名称" name="appName" rules={[{ required: true, message: '请输入应用名称' }]}> 
          <Input placeholder="请输入应用名称" />
        </Form.Item>
        <Form.Item label="应用 Logo 链接" name="appLogo">
          <Input placeholder="请输入 Logo 图片地址" />
        </Form.Item>
        <Form.Item label="应用描述" name="appDesc">
          <Input.TextArea placeholder="请输入应用描述" rows={3} />
        </Form.Item>
        <Form.Item label="应用域名" name="appDomain">
          <Input placeholder="如 https://yourapp.com" />
        </Form.Item>
        <Form.Item label="ICP备案号" name="appICP">
          <Input placeholder="如 粤ICP备xxxx号" />
        </Form.Item>
        <Form.Item>
          <Space>
            <Button type="primary" htmlType="submit" loading={loading}>保存</Button>
            <Button htmlType="button" onClick={() => form.resetFields()}>重置</Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default AppBaseSetting;
