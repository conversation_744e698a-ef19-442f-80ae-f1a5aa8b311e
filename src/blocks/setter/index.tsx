import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>crumb, Tabs } from "antd";
import { FilePptOutlined } from "@ant-design/icons";

import useStore from "@/store";

import { EventPanel, PropsPanel, SeniorPanel, StylePanel } from "./panel";
import { getComponentConfigurePropsSchema } from "@/utils/schema";

import { getParentPathByUuid } from "@/utils/index";

import "./index.css";

const Setter = () => {
  const { jsonSchema, selectedId, selectedSomeSchema, updateSelectedId } =
    useStore();
  const { rightActiveKey, updateRightActiveKey } = useStore();

  const onChange = (key) => {
    updateRightActiveKey(key);
  };

  const configurePropsSchema = getComponentConfigurePropsSchema(
    selectedSomeSchema?.componentName
  );

  const selectedPath = getParentPathByUuid(jsonSchema, selectedId);

  const items = [
    {
      key: "props",
      label: "属性",
      children: (
        <PropsPanel
          selectedSomeSchema={selectedSomeSchema}
          configurePropsSchema={configurePropsSchema}
        />
      ),
    },
    {
      key: "style",
      label: "样式",
      children: <StylePanel />,
    },
    {
      key: "event",
      label: "事件",
      children: <EventPanel />,
    },
    {
      key: "senior",
      label: "高级",
      children: <SeniorPanel></SeniorPanel>,
    },
  ];

  const transPath = (selectedPath = []) => {
    return selectedPath.map((item) => {
      return {
        title: (
          <Tooltip title={item.label} placement="bottom">
            <a>{item.componentName}</a>
          </Tooltip>
        ),
        onClick: (event) => {
          updateSelectedId(item?.uuid);
        },
      };
    });
  };

  const materialPathItems = [
    {
      title: (
        <Space>
          <FilePptOutlined />
          路径
        </Space>
      ),
    },
    ...transPath(selectedPath),
  ];

  return (
    <div className="design-setter">
      <Flex vertical>
        {selectedPath.length > 0 && (
          <>
            <div className="setter-material-path">
              <Breadcrumb items={materialPathItems} />
            </div>

            <Divider style={{ margin: 0 }} />
          </>
        )}
        <Tabs
          defaultActiveKey="props"
          activeKey={rightActiveKey}
          centered
          items={items}
          onChange={onChange}
        />
      </Flex>
    </div>
  );
};

export default Setter;
