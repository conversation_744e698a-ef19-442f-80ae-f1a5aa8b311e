import StyleSetter from "@/core/setter/StyleSetter";
import useStore from "@/store";
import "./index.css";
const StylePanel = () => {
  const selectedSomeSchema = useStore((state) => state.selectedSomeSchema);
  return (
    <div
      className="setter-panel"
      style={{
        height: `calc(100vh - ${selectedSomeSchema ? 175 : 126}px)`,
        padding: "0 16px",
      }}
    >
      <StyleSetter />
    </div>
  );
};

export { StylePanel };

export default StylePanel;
