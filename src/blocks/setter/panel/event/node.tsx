import { useEffect, useState } from "react";
import { Dropdown, <PERSON>lt<PERSON>, Ta<PERSON>, Mo<PERSON>, ConfigProvider } from "antd";
import { PlusOutlined, MinusOutlined } from "@ant-design/icons";
import {
  Navigate,
  Fetch,
  FormOperation,
  Component,
  Notice,
  Send,
  Broadcast,
  Browser,
  Script,
  Support,
  Other,
} from "./action";
import classNames from "classnames";

import useStore from "@/store";

const CustomeNode = (props) => {
  const { type, label, onOkCallBack, onDeleteCallBack, node, items } = props;
  const {
    store: { data },
  } = node;
  const extra = node.prop("extra");
  const [open, setOpen] = useState(false);
  const [active, setActive] = useState();
  const [formData, setFormData] = useState();

  const { jsonSchema } = useStore();

  const onFormChange = (data) => {
    setFormData(data);
  };

  const tabItems = [
    {
      key: "navigate",
      label: "路由跳转",
      children: <Navigate />,
    },
    {
      key: "fetch",
      label: "数据请求",
      children: <Fetch />,
    },
    {
      key: "form",
      label: "表单操作",
      children: <FormOperation />,
    },
    {
      key: "component",
      label: "组件控制",
      children: <Component />,
    },
    {
      key: "notice",
      label: "消息提醒",
      children: <Notice onFormChange={onFormChange} />,
    },
    {
      key: "send",
      label: "消息发送",
      children: <Send />,
    },
    {
      key: "broadcast",
      label: "广播事件",
      children: <Broadcast />,
    },
    {
      key: "support",
      label: "辅助操作",
      children: <Support />,
    },
    {
      key: "script",
      label: "函数脚本",
      children: <Script />,
    },
    {
      key: "browser",
      label: "浏览器",
      children: <Browser />,
    },
    {
      key: "other",
      label: "其他",
      children: <Other />,
    },
  ];

  const handleTabChange = (active) => {
    let current = {};
    items.map((item) => {
      if (item.key === active) {
        current = item;
      }
    });
    setActive(current);
    setOpen(true);
  };

  const handleMenuClick = ({ key }) => {
    let current = {};
    items.map((item) => {
      if (item.key === key) {
        current = item;
      }
    });
    setActive(current);
    setOpen(true);
  };

  const onHandleOk = () => {
    setOpen(false);
    console.log({ formData, active });
    const actionData = {
      actionType: formData.actionType,
      args: {
        ...formData,
      },
    };
    onOkCallBack && onOkCallBack(props, active, actionData);
  };

  const onHandleCancel = () => {
    setOpen(false);
  };

  const onDeleteNode = () => {
    onDeleteCallBack && onDeleteCallBack(props);
  };

  return (
    <div
      className={classNames(
        type === "start" ? "custome-node-start" : "custome-node"
      )}
    >
      <div className="node-minus" onClick={onDeleteNode}>
        <MinusOutlined />
      </div>
      {data?.label}
      <div className="node-plus">
        <Dropdown
          menu={{ items, onClick: handleMenuClick }}
          placement="bottomLeft"
        >
          <Tooltip title="添加节点">
            <PlusOutlined />
          </Tooltip>
        </Dropdown>
        <ConfigProvider
          theme={{
            token: {
              borderRadius: 2,
            },
          }}
        >
          <Modal
            title="节点操作"
            open={open}
            width="50%"
            closable
            mask={false}
            onOk={onHandleOk}
            onCancel={onHandleCancel}
            zIndex={1201}
          >
            <Tabs
              tabPosition="left"
              items={tabItems}
              activeKey={active?.key}
              onChange={handleTabChange}
            />
          </Modal>
        </ConfigProvider>
      </div>
    </div>
  );
};

export { CustomeNode };

export default CustomeNode;
