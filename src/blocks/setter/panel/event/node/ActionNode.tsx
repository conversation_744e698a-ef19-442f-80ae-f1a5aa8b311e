import { <PERSON>le, Position } from "@xyflow/react";
import { InfoOutlined } from "@ant-design/icons";
import { Tooltip, Dropdown } from "antd";

import ActionTool from "./ActionTool";

import "./index.less";

import {
  genUniqueId,
  findAndInsertAction,
  findAndModifyAction,
  findAndDeleteAction,
} from "@/utils/index";

import useStore from "@/store";

const ActionNode = (props) => {
  const { data, id } = props;
  const { updateJsonSchema, selectedId, jsonSchema } = useStore();

  const onHandleMenuClick = ({ key }) => {
    if (key === "delete") {
      const newSchema = findAndDeleteAction(
        jsonSchema,
        selectedId,
        data?.eventName,
        id
      );
      updateJsonSchema(newSchema);
    }

    if (key === "update") {
      const newSchema = findAndModifyAction(
        jsonSchema,
        selectedId,
        data?.eventName,
        id,
        {}
      );
      updateJsonSchema(newSchema);
    }
  };

  const onOk = (active, actionData) => {
    let newSchema = findAndInsertAction(
      jsonSchema,
      selectedId,
      data?.eventName,
      id,
      {
        actionId: genUniqueId(),
        nodeType: active?.key,
        ...actionData,
      }
    );
    updateJsonSchema(newSchema);
  };

  return (
    <div className="custom-action-node">
      <Handle type="source" position={Position.Right} />
      <Handle type="target" position={Position.Left} />

      <div className="node-minus">
        <Dropdown
          menu={{
            items: [
              {
                key: "delete",
                label: "删除节点",
                danger: true,
              },
              {
                type: "divider",
              },
              {
                key: "update",
                label: "修改节点",
              },
            ],
            onClick: onHandleMenuClick,
          }}
          placement="bottomLeft"
        >
          <Tooltip title="编辑节点">
            <InfoOutlined />
          </Tooltip>
        </Dropdown>
      </div>
      {data?.label}
      <ActionTool onOk={onOk} />
    </div>
  );
};

export { ActionNode };

export default ActionNode;
