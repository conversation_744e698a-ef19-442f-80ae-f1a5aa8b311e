.custom-start-node {
  position: relative;
  height: 60px;
  width: 60px;
  border: 2px solid #333;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-end-node {
  position: relative;
  height: 60px;
  width: 60px;
  border: 2px solid #333;
  background-color: #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-action-node {
  height: 60px;
  width: 120px;
  border: 2px solid #814bd4;
  background-color: #e9d6fe;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-start-node:hover .node-plus {
  opacity: 1;
}

.custom-action-node:hover .node-plus {
  opacity: 1;
}

.node-plus {
  opacity: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  height: 20px;
  width: 20px;
  right: 2px;
  top: 50%;
  font-size: 16px;
  border-radius: 50%;
  box-sizing: border-box;
  background-color: #73d13d;
  border: 2px solid #237804;
  transform: translateY(-50%);
  cursor: pointer;
}

.inner-drawer {
  position: absolute;
}

.event-flow-drawer {
  z-index: 1200;
}

.node-minus {
  opacity: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  height: 20px;
  width: 20px;
  left: 2px;
  top: 50%;
  font-size: 16px;
  border-radius: 50%;
  box-sizing: border-box;
  background-color: #85917fb2;
  border: 2px solid #394136;
  transform: translateY(-50%);
  cursor: pointer;
}

.custom-action-node:hover .node-minus {
  opacity: 1;
}
