import { useState } from "react";
import { PlusOutlined } from "@ant-design/icons";
import { Dropdown, <PERSON>lt<PERSON>, Ta<PERSON>, Mo<PERSON>, ConfigProvider } from "antd";

import {
  Navigate,
  Fetch,
  FormOperation,
  Component,
  Notice,
  Send,
  Broadcast,
  Browser,
  <PERSON>ript,
  Support,
  Other,
} from "../action";

import { nodeTypes } from "@/utils/event";

import "./index.less";

const ActionTool = (props) => {
  const { onOk } = props;
  const [open, setOpen] = useState(false);
  const [active, setActive] = useState();
  const [formData, setFormData] = useState();

  const onFormChange = (data) => {
    setFormData(data);
  };

  const tabItems = [
    {
      key: "navigate",
      label: "路由跳转",
      children: <Navigate />,
    },
    {
      key: "fetch",
      label: "数据请求",
      children: <Fetch />,
    },
    {
      key: "form",
      label: "表单操作",
      children: <FormOperation onFormChange={onFormChange} />,
    },
    {
      key: "component",
      label: "组件控制",
      children: <Component onFormChange={onFormChange} />,
    },
    {
      key: "notice",
      label: "消息提醒",
      children: <Notice onFormChange={onFormChange} />,
    },
    {
      key: "send",
      label: "消息发送",
      children: <Send />,
    },
    {
      key: "broadcast",
      label: "广播事件",
      children: <Broadcast />,
    },
    {
      key: "support",
      label: "辅助操作",
      children: <Support />,
    },
    {
      key: "script",
      label: "函数脚本",
      children: <Script />,
    },
    {
      key: "browser",
      label: "浏览器",
      children: <Browser />,
    },
    {
      key: "other",
      label: "其他",
      children: <Other />,
    },
  ];

  const onHandleTabChange = (active) => {
    let current = {};
    nodeTypes.map((item) => {
      if (item.key === active) {
        current = item;
      }
    });
    setActive(current);
    setOpen(true);
  };

  const onHandleMenuClick = ({ key }) => {
    let current = {};
    nodeTypes.map((item) => {
      if (item.key === key) {
        current = item;
      }
    });
    setActive(current);
    setOpen(true);
  };

  const onHandleOk = () => {
    setOpen(false);
    const actionData = {
      actionType: formData.actionType,
      args: {
        ...formData,
      },
    };
    onOk && onOk(active, actionData);
  };

  const onHandleCancel = () => {
    setOpen(false);
  };

  return (
    <div className="node-plus">
      <Dropdown
        menu={{ items: nodeTypes, onClick: onHandleMenuClick }}
        placement="bottomLeft"
      >
        <Tooltip title="添加节点">
          <PlusOutlined />
        </Tooltip>
      </Dropdown>
      <ConfigProvider
        theme={{
          token: {
            borderRadius: 2,
          },
        }}
      >
        <Modal
          title="节点操作"
          open={open}
          width="50%"
          closable
          mask={false}
          onOk={onHandleOk}
          onCancel={onHandleCancel}
        >
          <Tabs
            tabPosition="left"
            items={tabItems}
            activeKey={active?.key}
            onChange={onHandleTabChange}
          />
        </Modal>
      </ConfigProvider>
    </div>
  );
};

export { ActionTool };

export default ActionTool;
