import { <PERSON><PERSON>, Position } from "@xyflow/react";
import ActionTool from "./ActionTool";

import { genUniqueId, findAndInsertAction } from "@/utils/index";

import "./index.less";
import useStore from "@/store";

const StartNode = (props) => {
  const { data } = props;
  const { updateJsonSchema, selectedId, jsonSchema } = useStore();

  const onOk = (active, actionData) => {
    let newSchema = findAndInsertAction(
      jsonSchema,
      selectedId,
      data?.eventName,
      undefined,
      {
        actionId: genUniqueId(),
        nodeType: active?.key,
        ...actionData,
      }
    );
    updateJsonSchema(newSchema);
  };

  return (
    <div className="custom-start-node">
      <Handle type="source" position={Position.Right} />
      {data?.label}
      <ActionTool onOk={onOk} />
    </div>
  );
};

export { StartNode };

export default StartNode;
