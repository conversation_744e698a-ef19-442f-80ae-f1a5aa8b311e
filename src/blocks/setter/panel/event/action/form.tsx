import {
  ConfigProvider,
  Flex,
  Alert,
  Form,
  Select,
} from "antd";
import { getComponentsByName } from "@/utils/schema"
import useStore from "@/store/index";

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};

const FormOperation = (props) => {
  const [form] = Form.useForm();
  const eventValue = Form.useWatch("event", form);
  const actionValue = Form.useWatch("action", form);
  const { jsonSchema } = useStore();
  const forms = getComponentsByName("Form", jsonSchema)
  const options = forms.map(item => ({label:item?.title, value:item?.uuid}))
  const { onFormChange } = props;

  const onValuesChange = (changedValues, allValues) => {
    allValues.actionType = "form"
    onFormChange?.(allValues);
  };
  

  return (
    <Flex vertical gap={24}>
      <ConfigProvider
        theme={{
          components: {
            Alert: {
              withDescriptionPadding: 12,
            },
          },
        }}
      >
        <Alert
          description="可以选择目标表单，执行表单值获取、表单赋值、表单重置、表单提交、表单验证等操作"
          type="info"
        />
      </ConfigProvider>

      <Form form={form} {...formItemLayout} onValuesChange={onValuesChange}>
        <Form.Item name="target" label="目标表单">
          <Select
            style={{ width: "100%" }}
            placeholder="请选择目标表单"
            options={options}
          />
        </Form.Item>
        <Form.Item name="action" label="表单操作">
          <Select
            style={{ width: "100%" }}
            placeholder="请选择目标表单"
            options={[
              { value: "get", label: "获取值" },
              { value: "set", label: "设置值" },
              { value: "reset", label: "重置" },
              { value: "submit", label: "提交" },
              { value: "validate", label: "校验" },
            ]}
          />
        </Form.Item>
      </Form>
    </Flex>
  );
};

export { FormOperation };

export default FormOperation;
