import {
  ConfigProvider,
  Flex,
  Alert,
  Form,
  Input,
  Select,
  Switch,
  Button,
  Tooltip,
} from "antd";
import { MinusCircleOutlined, PlusOutlined } from "@ant-design/icons";

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};

const formItemLayoutDynamic = {
  labelCol: {
    xs: {
      span: 24,
    },
    sm: {
      span: 4,
    },
  },
  wrapperCol: {
    xs: {
      span: 24,
    },
    sm: {
      span: 20,
    },
  },
};

const formItemLayoutWithOutLabel = {
  wrapperCol: {
    xs: {
      span: 20,
      offset: 4,
    },
    sm: {
      span: 20,
      offset: 4,
    },
  },
};

const Fetch = () => {
  const [form] = Form.useForm();
  const eventValue = Form.useWatch("event", form);
  const actionValue = Form.useWatch("action", form);

  return (
    <Flex vertical gap={24}>
      <ConfigProvider
        theme={{
          components: {
            Alert: {
              withDescriptionPadding: 12,
            },
          },
        }}
      >
        <Alert description="文件下载时，后端接口必须返回Blob对象" type="info" />
      </ConfigProvider>

      <Form form={form} {...formItemLayout}>
        <Form.Item name="type" label="请求类型">
          <Select
            style={{ width: "100%" }}
            options={[
              { value: "http", label: "HTTP" },
              { value: "websocket", label: "WebSocket" },
            ]}
          />
        </Form.Item>

        <Form.Item name="apiUrl" label="接口地址">
          <Select
            style={{ width: "100%" }}
            options={[
              { value: "/api/download", label: "文件下载" },
              { value: "/api/upload", label: "文件上传" },
            ]}
          />
        </Form.Item>

        <Form.Item name="description" label="接口描述">
          <Input allowClear />
        </Form.Item>

        <Form.Item name="action" label="请求方式">
          <Select
            style={{ width: "100%" }}
            options={[
              { value: "get", label: "GET" },
              { value: "post", label: "POST" },
              { value: "put", label: "PUT" },
              { value: "delete", label: "DELETE" },
              { value: "patch", label: "PATCH" },
            ]}
          />
        </Form.Item>

        <Form.Item name="contentType" label="数据格式">
          <Select
            style={{ width: "100%" }}
            options={[
              { value: "json", label: "JSON" },
              { value: "form", label: "FORM" },
              { value: "form-data", label: "FORM-DATA" },
            ]}
          />
        </Form.Item>

        <Form.Item name="success" label="成功提示">
          <Flex align="center" gap={8}>
            <Input placeholder="操作成功" />
            <Tooltip title="静默状态">
              <Switch />
            </Tooltip>
          </Flex>
        </Form.Item>

        <Form.Item name="error" label="失败提示">
          <Flex align="center" gap={8}>
            <Input placeholder="操作失败" />
            <Tooltip title="静默状态">
              <Switch />
            </Tooltip>
          </Flex>
        </Form.Item>

        <Form.List name="params">
          {(fields, { add, remove }) => (
            <>
              {fields.map(({ key, name, ...restField }, index) => (
                <Form.Item
                  key={key}
                  label={index === 0 ? "请求参数" : ""}
                  {...(index === 0
                    ? formItemLayoutDynamic
                    : formItemLayoutWithOutLabel)}
                >
                  <ConfigProvider
                    theme={{
                      components: {
                        Form: {
                          itemMarginBottom: 0,
                        },
                      },
                    }}
                  >
                    <Flex gap={8}>
                      <Form.Item {...restField} name={[name, "key"]}>
                        <Input placeholder="参数名" />
                      </Form.Item>

                      <Form.Item {...restField} name={[name, "value"]}>
                        <Input placeholder="参数值" />
                      </Form.Item>

                      <MinusCircleOutlined onClick={() => remove(name)} />
                    </Flex>
                  </ConfigProvider>
                </Form.Item>
              ))}
              <Form.Item {...formItemLayoutWithOutLabel}>
                <Button
                  type="dashed"
                  onClick={() => add()}
                  block
                  icon={<PlusOutlined />}
                >
                  新增参数
                </Button>
              </Form.Item>
            </>
          )}
        </Form.List>
      </Form>
    </Flex>
  );
};

export { Fetch };

export default Fetch;
