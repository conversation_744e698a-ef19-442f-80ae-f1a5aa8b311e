import {
  ConfigProvider,
  Flex,
  Alert,
  Form,
  Switch,
  Select,
  TreeSelect,
} from "antd";

import useStore from "@/store/index";
import { getComponentsTreeDataOptions } from "@/utils/schema"

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};

const Component = (props) => {
  const { jsonSchema } = useStore();
  const [form] = Form.useForm();
  const eventValue = Form.useWatch("event", form);
  const actionValue = Form.useWatch("action", form);
  const { onFormChange } = props;
  const treeData = getComponentsTreeDataOptions(jsonSchema)

  const onValuesChange = (changedValues, allValues) => {
    allValues.actionType = "component"
    onFormChange?.(allValues);
  };

  return (
    <Flex vertical gap={24}>
      <ConfigProvider
        theme={{
          components: {
            Alert: {
              withDescriptionPadding: 12,
            },
          },
        }}
      >
        <Alert
          description="控制指定组件的显示/隐藏、启用/禁用、展示态/编辑态、方法调用、变量赋值、改变activeKey等操作"
          type="info"
        />
      </ConfigProvider>

      <Form form={form} {...formItemLayout} onValuesChange={onValuesChange}>
        <Form.Item name="target" label="目标组件">
          <TreeSelect
            treeLine
            style={{ width: "100%" }}
            placeholder="请选择目标组件"
            treeData={treeData}
          />
        </Form.Item>
        <Form.Item name="show" label="组件显隐">
          <Switch />
        </Form.Item>

        <Form.Item name="disable" label="组件禁用">
          <Switch />
        </Form.Item>

        <Form.Item name="refresh" label="组件刷新">
          <Switch />
        </Form.Item>

        <Form.Item name="method" label="组件方法">
          <Select
            style={{ width: "100%" }}
            placeholder="请选择组件方法"
            options={[
              { value: "show", label: "显示" },
              { value: "hide", label: "隐藏" },
              { value: "call", label: "调用方法" },
            ]}
          />
        </Form.Item>
      </Form>
    </Flex>
  );
};

export { Component };

export default Component;
