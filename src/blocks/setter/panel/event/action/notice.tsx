import { ConfigProvider, Flex, Alert, Form, Switch, Select, Input } from "antd";

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};

const Notice = (props) => {
  const { onFormChange } = props;
  const [form] = Form.useForm();

  const onValuesChange = (changedValues, allValues) => {
    onFormChange?.(allValues);
  };

  return (
    <Flex vertical gap={24}>
      <ConfigProvider
        theme={{
          components: {
            Alert: {
              withDescriptionPadding: 12,
            },
          },
        }}
      >
        <Alert
          description="用弹出框，抽屉，确认框，全局提醒，通知用户，包括成功、失败、警告等"
          type="info"
        />
      </ConfigProvider>

      <Form form={form} {...formItemLayout} onValuesChange={onValuesChange}>
        <Form.Item name="actionType" label="消息类型">
          <Select
            style={{ width: "100%" }}
            placeholder="请选择消息类型"
            options={[
              { value: "modal", label: "弹出框" },
              { value: "drawer", label: "抽屉" },
              { value: "confirm", label: "确认框" },
              { value: "message", label: "全局提示" },
              { value: "notification", label: "通知" },
            ]}
          />
        </Form.Item>

        <Form.Item name="msgType" label="消息类型">
          <Select
            style={{ width: "100%" }}
            placeholder="请选择消息类型"
            options={[
              { value: "success", label: "成功" },
              { value: "error", label: "失败" },
              { value: "warning", label: "警告" },
            ]}
          />
        </Form.Item>

        <Form.Item name="content" label="提示内容">
          <Input placeholder="请输入提示内容" />
        </Form.Item>

        <Form.Item name="position" label="展示位置">
          <Select
            style={{ width: "100%" }}
            placeholder="请选择展示位置"
            options={[
              { value: "top", label: "顶部" },
              { value: "bottom", label: "底部" },
              { value: "left", label: "左侧" },
              { value: "right", label: "右侧" },
            ]}
          />
        </Form.Item>

        <Form.Item name="target" label="目标组件">
          <Select
            style={{ width: "100%" }}
            placeholder="请选择目标组件"
            options={[
              { value: "form1", label: "组件1" },
              { value: "form2", label: "组件2" },
            ]}
          />
        </Form.Item>

        <Form.Item name="show" label="弹框操作">
          <Switch checkedChildren="打开" unCheckedChildren="关闭" />
        </Form.Item>

        <Form.Item name="disable" label="抽屉操作">
          <Switch checkedChildren="打开" unCheckedChildren="关闭" />
        </Form.Item>
      </Form>
    </Flex>
  );
};

export { Notice };

export default Notice;
