import {
  ConfigProvider,
  Flex,
  Alert,
  Form,
  Switch,
  Select,
  Input,
  Upload,
} from "antd";

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};

const Support = () => {
  const [form] = Form.useForm();
  const eventValue = Form.useWatch("event", form);
  const actionValue = Form.useWatch("action", form);

  return (
    <Flex vertical gap={24}>
      <ConfigProvider
        theme={{
          components: {
            Alert: {
              withDescriptionPadding: 12,
            },
          },
        }}
      >
        <Alert
          description="可以执行复制到剪切板、打印、预览等操作"
          type="info"
        />
      </ConfigProvider>

      <Form form={form} {...formItemLayout}>
        <Form.Item name="support" label="辅助操作">
          <Select
            style={{ width: "100%" }}
            placeholder="请选择辅助操作"
            options={[
              { value: "copy", label: "复制内容" },
              { value: "print", label: "打印" },
            ]}
          />
        </Form.Item>

        <Form.Item name="content" label="打印内容">
          <Input.TextArea rows={6} placeholder="一般为JS脚本获取元素" />
        </Form.Item>

        <Form.Item name="content" label="复制内容">
          <Input.TextArea rows={6} placeholder="请输入复制内容" />
        </Form.Item>
      </Form>
    </Flex>
  );
};

export { Support };

export default Support;
