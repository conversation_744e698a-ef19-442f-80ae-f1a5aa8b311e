import { useState } from "react";
import {
  ConfigProvider,
  Flex,
  Alert,
  Form,
  Switch,
  Select,
  Input,
  Upload,
} from "antd";

import WinCode from "@/component/Code";

const formItemLayout = {
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 24 },
  },
};

const Script = () => {
  const [form] = Form.useForm();
  const [initialValue, setInitialValue] = useState(`/**
* 触发动作后，会执行该函数
* 上下文: context
* 变量: variable
* 事件流参数: eventParams
*/
function main(){
    const store = useStore();
    return context.eventParams;
}`);
  const eventValue = Form.useWatch("event", form);
  const actionValue = Form.useWatch("action", form);

  const onCodeChange = (delta, content) => {
    // console.log({ delta, content });
  };

  return (
    <Flex vertical gap={24}>
      <ConfigProvider
        theme={{
          components: {
            Alert: {
              withDescriptionPadding: 12,
            },
          },
        }}
      >
        <Alert
          description="执行原生脚本，比如修改参数，获取变量、表单值、Store数据、数据处理、复杂逻辑处理、组件状态控制、执行前置后置条件等。"
          type="info"
        />
      </ConfigProvider>

      <Form form={form} {...formItemLayout}>
        <Form.Item name="type">
          <WinCode
            initialValue={initialValue}
            options={{ useWorker: false }}
            mode="javascript"
            style={{ height: "calc(100vh - 560px)" }}
            onChange={onCodeChange}
          />
        </Form.Item>
      </Form>
    </Flex>
  );
};

export { Script };

export default Script;
