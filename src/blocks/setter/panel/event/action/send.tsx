import {
  ConfigProvider,
  Flex,
  Alert,
  Form,
  Select,
  Input,
  Upload,
  Button,
} from "antd";
import { UploadOutlined } from "@ant-design/icons";

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};

const Send = () => {
  const [form] = Form.useForm();
  const eventValue = Form.useWatch("event", form);
  const actionValue = Form.useWatch("action", form);

  const props = {
    action: "https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload",
    onChange({ file, fileList }) {
      if (file.status !== "uploading") {
        console.log(file, fileList);
      }
    },
    defaultFileList: [
      {
        uid: "1",
        name: "xxx.png",
        status: "uploading",
        url: "http://www.baidu.com/xxx.png",
        percent: 33,
      },
      {
        uid: "2",
        name: "yyy.png",
        status: "done",
        url: "http://www.baidu.com/yyy.png",
      },
      {
        uid: "3",
        name: "zzz.png",
        status: "error",
        response: "Server Error 500",
        // custom error message to show
        url: "http://www.baidu.com/zzz.png",
      },
    ],
  };

  return (
    <Flex vertical gap={24}>
      <ConfigProvider
        theme={{
          components: {
            Alert: {
              withDescriptionPadding: 12,
            },
          },
        }}
      >
        <Alert description="用邮件或系统内部消息发送通知" type="info" />
      </ConfigProvider>

      <Form form={form} {...formItemLayout}>
        <Form.Item name="type" label="消息类型">
          <Select
            style={{ width: "100%" }}
            placeholder="请选择消息类型"
            options={[
              { value: "email", label: "邮件" },
              { value: "message", label: "消息" },
              { value: "ding", label: "钉钉" },
            ]}
          />
        </Form.Item>

        <Form.Item name="target" label="收件人">
          <Input style={{ width: "100%" }} placeholder="请输入收件人" />
        </Form.Item>

        <Form.Item name="subject" label="主题">
          <Input style={{ width: "100%" }} placeholder="请输入主题" />
        </Form.Item>

        <Form.Item name="body" label="正文">
          <Input style={{ width: "100%" }} placeholder="请输入正文" />
        </Form.Item>

        <Form.Item name="attachments" label="附件">
          <Upload {...props}>
            <Button icon={<UploadOutlined />}>附件上传</Button>
          </Upload>
        </Form.Item>
      </Form>
    </Flex>
  );
};

export { Send };

export default Send;
