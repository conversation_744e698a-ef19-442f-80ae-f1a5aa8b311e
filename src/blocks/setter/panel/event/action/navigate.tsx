import {
  ConfigProvider,
  Flex,
  Alert,
  Form,
  Input,
  Select,
  InputNumber,
} from "antd";

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};

const Navigate = () => {
  const [form] = Form.useForm();
  const modeValue = Form.useWatch("mode", form);

  return (
    <Flex vertical gap={24}>
      <ConfigProvider
        theme={{
          components: {
            Alert: {
              withDescriptionPadding: 12,
            },
          },
        }}
      >
        <Alert
          description="跳转到指定页面，浏览器回退、前进、后退、刷新等"
          type="info"
        />
      </ConfigProvider>

      <Form form={form} {...formItemLayout}>
        <Form.Item name="mode" label="跳转方式">
          <Select
            style={{ width: "100%" }}
            options={[
              { value: "route", label: "内部跳转" },
              { value: "link", label: "超链接跳转" },
            ]}
          />
        </Form.Item>

        <Form.Item name="url" label="页面地址">
          <Input allowClear />
        </Form.Item>

        {modeValue === "link" && (
          <Form.Item name="target" label="打开方式">
            <Select
              style={{ width: "100%" }}
              options={[
                { value: "_self", label: "当前窗口" },
                { value: "_blank", label: "新窗口" },
              ]}
            />
          </Form.Item>
        )}
      </Form>
    </Flex>
  );
};

export { Navigate };

export default Navigate;
