import {
  ConfigProvider,
  Flex,
  Alert,
  Form,
  Switch,
  Select,
  Input,
  Upload,
} from "antd";

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};

const Other = () => {
  const [form] = Form.useForm();
  const eventValue = Form.useWatch("event", form);
  const actionValue = Form.useWatch("action", form);

  return (
    <Flex vertical gap={24}>
      <ConfigProvider
        theme={{
          components: {
            Alert: {
              withDescriptionPadding: 12,
            },
          },
        }}
      >
        <Alert
          description="没指定组件ID时，所有组件都监听。指定了组件数组，则在对应组件监听"
          type="info"
        />
      </ConfigProvider>

      <Form form={form} {...formItemLayout}>
        <Form.Item name="type" label="广播类型">
          <Select
            style={{ width: "100%" }}
            placeholder="请选择广播类型"
            options={[
              { value: "broadcast", label: "广播" },
              { value: "single", label: "单播" },
            ]}
          />
        </Form.Item>

        <Form.Item name="target" label="广播目标">
          <Select
            style={{ width: "100%" }}
            placeholder="请输入广播目标"
            options={[
              { value: "all", label: "全部" },
              { value: "custom", label: "自定义" },
            ]}
          />
        </Form.Item>

        <Form.Item name="subject" label="单播目标">
          <Input style={{ width: "100%" }} placeholder="请输入单播目标" />
        </Form.Item>
      </Form>
    </Flex>
  );
};

export { Other };

export default Other;
