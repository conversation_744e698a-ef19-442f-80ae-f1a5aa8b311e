import {
  ConfigProvider,
  Flex,
  Alert,
  Form,
  Input,
  Select,
  InputNumber,
} from "antd";

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};

const Browser = () => {
  const [form] = Form.useForm();
  const eventValue = Form.useWatch("event", form);
  const actionValue = Form.useWatch("action", form);

  return (
    <Flex vertical gap={24}>
      <ConfigProvider
        theme={{
          components: {
            Alert: {
              withDescriptionPadding: 12,
            },
          },
        }}
      >
        <Alert
          description="跳转到指定页面，浏览器回退、前进、后退、刷新等"
          type="info"
        />
      </ConfigProvider>

      <Form form={form} {...formItemLayout}>
        <Form.Item name="action" label="响应动作">
          <Select
            style={{ width: "100%" }}
            options={[
              { value: "goback", label: "回退N步" },
              { value: "forward", label: "前进" },
              { value: "back", label: "后退" },
              { value: "refresh", label: "刷新" },
            ]}
          />
        </Form.Item>

        {actionValue === "goback" && (
          <Form.Item name="steps" label="回退步数">
            <InputNumber style={{ width: "100%" }} />
          </Form.Item>
        )}
      </Form>
    </Flex>
  );
};

export { Browser };

export default Browser;
