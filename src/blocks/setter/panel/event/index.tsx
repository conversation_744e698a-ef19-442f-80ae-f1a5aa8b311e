import { useEffect, useState } from "react";
import {
  Table,
  Badge,
  Space,
  Switch,
  Flex,
  Button,
  Divider,
  Select,
  Row,
  Col,
  Drawer,
  Tooltip,
} from "antd";
import {
  FunctionOutlined,
  DeleteOutlined,
  NodeIndexOutlined,
  PlusOutlined,
  CloseOutlined,
} from "@ant-design/icons";
import { message } from "@/store/hooks";
import useStore from "@/store";
import { getComponentSchema } from "@/utils/schema";
import { eventEnums, getEventArray } from "@/utils/event";
import FlowDesign from "./flow";
import "./index.css";

const EventPanel = (props) => {
  const { selectedSomeSchema } = useStore();
  const [open, setOpen] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState("");
  const [eventOptions, setEventOptions] = useState([]);
  const [eventName, setEventName] = useState("");
  const [dataSourceEvents, setDataSourceEvents] = useState([]);
  const [dataSourceMethods, setDataSourceMethods] = useState([]);

  useEffect(() => {
    const selectedSchema = getComponentSchema(
      selectedSomeSchema?.componentName
    );

    const results = getEventArray(selectedSchema?.events);
    setDataSourceMethods(results.filter((item) => item.isCustom));
    setDataSourceEvents(results.filter((item) => !item.isCustom));

    const waitLoadEvents = eventEnums.filter((item) => {
      return ![...Object.keys(selectedSomeSchema?.events || {})].includes(
        item.value
      );
    });

    setEventOptions(waitLoadEvents);
    setSelectedEvent(waitLoadEvents[0]?.value);
  }, [selectedSomeSchema]);

  const onAddEvent = () => {
    if (eventOptions.length) {
      setOpen(true);
    } else {
      message.error("没有可用事件");
    }
  };

  const columns = [
    {
      title: "组件事件",
      dataIndex: "name",
      key: "name",
      render: (text) => (
        <Space>
          <Badge status="processing"></Badge>
          {text}
        </Space>
      ),
    },
    {
      title: "配置",
      dataIndex: "action",
      key: "action",
      width: 140,
      render: (text, row) => (
        <Space size="middle">
          <Switch size="small" defaultChecked />
          {/* 响应事件的逻辑 */}
          <Button
            icon={<FunctionOutlined />}
            size="small"
            type="text"
            onClick={() => onEventFlow(text, row)}
          >
            事件流
          </Button>
        </Space>
      ),
    },
  ];

  const onEventFlow = (text, row) => {
    setEventName(row?.name);
    setOpen(true);
  };

  const customeColumns = [
    {
      title: "自定义事件",
      dataIndex: "name",
      key: "name",
      render: (text, row) => (
        <Space>
          <Badge status="processing"></Badge>
          <Tooltip title={row.description}>{row.name}</Tooltip>
        </Space>
      ),
    },
    {
      title: "配置",
      dataIndex: "action",
      key: "action",
      width: 140,
      render: (text, row) => (
        <Space size="middle">
          <Button
            icon={<DeleteOutlined />}
            size="small"
            type="text"
            danger
          ></Button>

          <Button
            icon={<NodeIndexOutlined />}
            size="small"
            type="text"
            onClick={() => onEventFlow(text, row)}
          >
            事件流
          </Button>
        </Space>
      ),
    },
  ];

  const onClose = () => {
    setOpen(false);
  };

  return (
    <div className="setter-panel">
      <Flex vertical gap={12}>
        <Table
          dataSource={dataSourceEvents}
          columns={columns}
          pagination={false}
          size="small"
        />

        <Divider
          style={{
            margin: 8,
          }}
        >
          自定义事件
        </Divider>

        <Row gutter={24}>
          <Col flex="auto">
            <Select
              value={selectedEvent}
              style={{ width: "100%" }}
              options={eventOptions}
            />
          </Col>
          <Col flex="160px">
            <Button type="primary" icon={<PlusOutlined />} onClick={onAddEvent}>
              添加事件
            </Button>
          </Col>
        </Row>

        <Table
          dataSource={dataSourceMethods}
          columns={customeColumns}
          pagination={false}
          size="small"
        />

        <Drawer
          title="事件流"
          placement="top"
          closable={false}
          onClose={onClose}
          open={open}
          height="100%"
          zIndex={1201}
          extra={
            <Space>
              <Button
                type="default"
                onClick={onClose}
                icon={<CloseOutlined />}
              ></Button>
            </Space>
          }
        >
          <FlowDesign eventName={eventName} />
        </Drawer>
      </Flex>
    </div>
  );
};

export { EventPanel };

export default EventPanel;
