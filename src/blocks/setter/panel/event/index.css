.flow-design {
  height: 100%;
}

@keyframes ant-line {
  to {
    stroke-dashoffset: -1000;
  }
}

.custome-node {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  width: 120px;
  height: 60px;
  background-color: #efdbff;
  border-radius: 4px;
  box-sizing: border-box;
  border: 2px solid #9254de;
}

.custome-node-start {
  box-sizing: border-box;
  height: 80px;
  width: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #ffa940;
  background-color: #ffd591;
  border-radius: 50%;
}

.custome-node:hover .node-plus {
  opacity: 1;
}

.custome-node-start:hover .node-plus {
  opacity: 1;
}

.node-plus {
  opacity: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  height: 20px;
  width: 20px;
  right: 2px;
  top: 50%;
  font-size: 16px;
  border-radius: 50%;
  box-sizing: border-box;
  background-color: #73d13d;
  border: 2px solid #237804;
  transform: translateY(-50%);
  cursor: pointer;
}

.inner-drawer {
  position: absolute;
}

.event-flow-drawer {
  z-index: 1200;
}

.node-minus {
  opacity: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  height: 20px;
  width: 20px;
  left: 2px;
  top: 50%;
  font-size: 16px;
  border-radius: 50%;
  box-sizing: border-box;
  background-color: #85917fb2;
  border: 2px solid #394136;
  transform: translateY(-50%);
  cursor: pointer;
}

.custome-node:hover .node-minus {
  opacity: 1;
}
