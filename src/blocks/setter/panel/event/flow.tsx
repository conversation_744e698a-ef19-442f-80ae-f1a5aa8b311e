import { use<PERSON><PERSON>back, useMemo, useEffect } from "react";
import {
  Background,
  ReactFlow,
  MiniMap,
  Controls,
  useNodesState,
  useEdgesState,
  addEdge,
  MarkerType,
} from "@xyflow/react";
import "@xyflow/react/dist/style.css";

import StartNode from "./node/StartNode";
import ActionNode from "./node/ActionNode";
import EndNode from "./node/EndNode";

import { getObjectById } from "@/utils";
import { getNodeTypeName } from "@/utils/event";
import useStore from "@/store";

const FlowDesign = (props) => {
  const { selectedId, jsonSchema } = useStore();
  const { eventName } = props;

  const selectedSomeSchema = getObjectById(jsonSchema, selectedId);

  const actions = selectedSomeSchema?.events?.[eventName]?.actions || [];

  // 生成 actions 节点
  const actionNodes = useMemo(() => {
    return actions.map((action, index) => ({
      id: action?.actionId,
      sourcePosition: "right",
      targetPosition: "left",
      type: "action",
      data: {
        label: getNodeTypeName(action?.nodeType) || `节点${index + 2}`,
        eventName,
      },
      position: { x: 200 * (index + 1), y: 80 },
    }));
  }, [actions]);

  // 生成初始节点，包含开始节点、actions 节点和结束节点
  const initialNodes = useMemo(() => {
    return [
      {
        id: "start-node",
        sourcePosition: "right",
        type: "start",
        data: { label: "开始", eventName },
        position: { x: 0, y: 80 },
      },
      ...actionNodes,
      {
        id: "end-node",
        targetPosition: "left",
        data: { label: "结束", eventName },
        type: "end",
        position: { x: 200 * (actions.length + 1), y: 80 },
      },
    ];
  }, [actionNodes, actions]);

  // 生成连接 actions 节点的边
  const actionEdges = useMemo(() => {
    const edges = [];
    if (actionNodes.length === 0) {
      // 当没有 action 节点时，直接连接开始节点和结束节点
      edges.push({
        id: "e-start-end",
        source: "start-node",
        type: "smoothstep",
        target: "end-node",
        markerEnd: { type: MarkerType.Arrow },
        animated: true,
      });
    } else {
      for (let i = 0; i < actionNodes.length; i++) {
        if (i === 0) {
          // 开始节点到第一个 action 节点的边
          edges.push({
            id: `e-start-${actionNodes[i].id}`,
            source: "start-node",
            type: "smoothstep",
            target: actionNodes[i].id,
            markerEnd: { type: MarkerType.Arrow },
            animated: true,
          });
        }
        if (i < actionNodes.length - 1) {
          // 相邻 action 节点之间的边
          edges.push({
            id: `e-${actionNodes[i].id}-${actionNodes[i + 1].id}`,
            source: actionNodes[i].id,
            type: "smoothstep",
            target: actionNodes[i + 1].id,
            markerEnd: { type: MarkerType.Arrow },
            animated: true,
          });
        }
        if (i === actionNodes.length - 1) {
          // 最后一个 action 节点到结束节点的边
          edges.push({
            id: `e-${actionNodes[i].id}-end`,
            source: actionNodes[i].id,
            type: "smoothstep",
            target: "end-node",
            markerEnd: { type: MarkerType.Arrow },
            animated: true,
          });
        }
      }
    }
    return edges;
  }, [actionNodes]);

  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(actionEdges);

  const nodeTypes = useMemo(
    () => ({ start: StartNode, action: ActionNode, end: EndNode }),
    []
  );

  const onConnect = useCallback(
    (params) => setEdges((els) => addEdge(params, els)),
    []
  );

  // 监听 events 数组的变化，更新节点和边
  useEffect(() => {
    setNodes(initialNodes);
    setEdges(actionEdges);
  }, [actions, initialNodes, actionEdges]);

  return (
    <ReactFlow
      nodes={nodes}
      edges={edges}
      nodeTypes={nodeTypes}
      onNodesChange={onNodesChange}
      onEdgesChange={onEdgesChange}
      onConnect={onConnect}
      fitView
      attributionPosition="bottom-left"
    >
      <Background />
      <MiniMap />
      <Controls />
    </ReactFlow>
  );
};

export default FlowDesign;
