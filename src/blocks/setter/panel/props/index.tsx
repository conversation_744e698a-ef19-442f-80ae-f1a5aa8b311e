import { useState, useEffect, useCallback } from "react";
import {
  Row,
  Col,
  Typography,
  Input,
  Empty,
  Flex,
  Switch,
  Space,
  Divider,
  Tooltip,
} from "antd";
import { debounce } from "lodash-es";
import SetterRender from "@/core/setter";
import {
  updateProperty,
  updateProps,
  isString,
  parsePathParams,
} from "@/utils";
import useStore from "@/store";
import "./index.css";

const { Text } = Typography;

/**
  * @description: 属性面板
  * @param {Object} schema json schema
  * @return {React.ReactElement}
  */
const PropsPanel = ({ selectedSomeSchema, configurePropsSchema }) => {
  const {
    uuid,
    title,
    name,
    field,
    props, // props 当前组件实际挂载的属性
    children,
    show: isShow,
    componentName,
  } = selectedSomeSchema || {};

  const originaProps = configurePropsSchema; // originaProps 当前组件所有的属性（包含未挂载）

  const { jsonSchema, updateJsonSchema } = useStore();
  const { type } = parsePathParams();

  const [lock, setLock] = useState(false);
  const [show, setShow] = useState(isShow);
  const [materialTitle, setMeterialTitle] = useState(title);
  const [materialField, setMeterialField] = useState(field);
  const [materialText, setMeterialText] = useState("");

  const [boolPropsList, noBoolPropsList] = originaProps.reduce(
    (previousValue, currentValue) => {
      if (currentValue.setter?.componentName === "BooleanSetter") {
        previousValue[0].push(currentValue);
      } else {
        previousValue[1].push(currentValue);
      }
      return previousValue;
    },
    [[], []]
  );

  useEffect(() => {
    setLock(false);
    setShow(isShow !== false);
  }, [isShow]);

  useEffect(() => {
    if (isString(children)) {
      setMeterialText(children);
    }
    setMeterialTitle(title);
    setMeterialField(field);
  }, [title, field, children]);

  const onPropertyChange = (e) => {
    let { value } = e.target;
    const newSchema = updateProperty(jsonSchema, uuid, "title", value);
    updateJsonSchema(newSchema);
  };

  const onTitleChange = (e) => {
    let { value } = e.target;
    setMeterialTitle(value);
    const newSchema = updateProperty(jsonSchema, uuid, "title", value);
    updateJsonSchema(newSchema);
  };

  const onShowChange = (checked) => {
    const newSchema = updateProperty(jsonSchema, uuid, "show", checked);
    setShow(checked);
    updateJsonSchema(newSchema);
  };

  const onLockChange = (checked) => {
    const newSchema = updateProperty(jsonSchema, uuid, "lock", checked);
    setLock(checked);
    updateJsonSchema(newSchema);
  };

  const onFieldChange = (e) => {
    let { value } = e.target;
    setMeterialField(value);
    const newSchema = updateProperty(jsonSchema, uuid, "field", value);
    updateJsonSchema(newSchema);
  };

  const onTextChange = (e) => {
    let { value } = e.target;
    setMeterialText(value);
    const newSchema = updateProperty(jsonSchema, uuid, "children", value);
    updateJsonSchema(newSchema);
  };

  // 使用 useCallback 和 lodash-es debounce 创建防抖函数
  const debouncedSetterChange = useCallback(
    debounce((value, item) => {
      if (value === undefined) {
        console.log(`属性:${item.name} -> 值未设置(${value})`);
        return;
      }
      const newSchema = updateProps(jsonSchema, uuid, item.name, value);
      console.log(`属性:${item.name} -> 最新值:${value}`, newSchema);
      updateJsonSchema(newSchema);
    }, 300), // 建议 300ms 防抖延迟
    [jsonSchema, uuid, updateJsonSchema]
  );

  const onSetterChange = (value, item) => {
    debouncedSetterChange(value, item);
  };

  return (
    <div className="setter-panel">
      {uuid ? (
        <Flex vertical gap={12}>
          <Row gutter={12}>
            <Col span={6} className="label-flex">
              UUID
            </Col>
            <Col span={18}>
              <Input
                value={uuid}
                addonAfter={<Text copyable={{ text: uuid }} />}
                disabled
              />
            </Col>
          </Row>
          <Row gutter={12}>
            <Col span={6} className="label-flex">
              物料标签
            </Col>
            <Col span={18}>
              <Input
                value={componentName}
                disabled
                addonAfter={<Text copyable={{ text: componentName }} />}
              />
            </Col>
          </Row>

          <Row gutter={12}>
            <Col span={6} className="label-flex">
              物料名称
            </Col>
            <Col span={18}>
              <Input value={materialTitle} onChange={onTitleChange} />
            </Col>
          </Row>

          <Row gutter={12}>
            <Col span={6} className="label-flex">
              显示 / 锁定
            </Col>
            <Col span={18}>
              <Space size="large">
                <Switch
                  unCheckedChildren="隐藏"
                  checkedChildren="显示"
                  checked={show}
                  onChange={(checked) => onShowChange(checked)}
                />
                <Switch
                  checkedChildren="锁定"
                  unCheckedChildren="解锁"
                  checked={lock}
                  onChange={(checked) => onLockChange(checked)}
                />
                <Tooltip title="组件name">
                  <Input
                    value={name}
                    disabled
                    addonAfter={<Text copyable={{ text: name }} />}
                  />
                </Tooltip>
              </Space>
            </Col>
          </Row>

          {type && <Divider style={{ margin: 8 }} />}

          {type === "form" && (
            <Row gutter={12}>
              <Col span={6} className="label-flex">
                字段 label
              </Col>
              <Col span={18}>
                <Input value={title} onChange={onPropertyChange} />
              </Col>
            </Row>
          )}

          <Row gutter={12}>
            <Col span={6} className="label-flex">
              字段键名
            </Col>
            <Col span={18}>
              <Input
                value={materialField}
                onChange={onFieldChange}
                addonAfter={<Text copyable={{ text: materialField }} />}
              />
            </Col>
          </Row>

          {isString(children) ? (
            <Row gutter={12}>
              <Col span={6} className="label-flex">
                文字内容
              </Col>
              <Col span={18}>
                <Input value={materialText} onChange={onTextChange} />
              </Col>
            </Row>
          ) : null}

          {/* style 和 className 抽离到 样式栏设置 */}

          {noBoolPropsList
            .filter(
              (item) =>
                item.name !== "style" &&
                item.name !== "className" &&
                item.name !== name
            )
            .map((item, index) => {
              return (
                <Row gutter={12} key={item.name + index}>
                  <Col span={6} className="label-flex">
                    {item.title}
                  </Col>
                  <Col span={18}>
                    <SetterRender
                      itemProps={item} // 当前组件所有的属性枚举中的一项
                      haveProps={props}
                      onChange={(value) => onSetterChange(value, item)}
                    />
                  </Col>
                </Row>
              );
            })}

          {boolPropsList?.length > 0 && <Divider style={{ margin: 8 }} />}

          <Row gutter={24}>
            {boolPropsList.map((item, index) => {
              return (
                <Col
                  key={item.name + index}
                  span={12}
                  style={{ marginBottom: 16 }}
                >
                  <Row gutter={24}>
                    <Col span={12} className="label-flex">
                      {item.title}
                    </Col>
                    <Col span={12}>
                      <SetterRender
                        itemProps={item} // 当前组件所有的属性枚举中的一项
                        haveProps={props}
                        onChange={(value) => onSetterChange(value, item)}
                      />
                    </Col>
                  </Row>
                </Col>
              );
            })}
          </Row>
        </Flex>
      ) : (
        <Empty description="暂无物料信息" />
      )}
    </div>
  );
};

export { PropsPanel };

export default PropsPanel;
