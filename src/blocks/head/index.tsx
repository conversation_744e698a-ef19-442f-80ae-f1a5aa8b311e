import { useState } from "react";
import { useNavigate, Link, useParams } from "react-router-dom";
import {
  Flex,
  Space,
  theme,
  Button,
  Divider,
  Segmented,
  Dropdown,
  Avatar,
} from "antd";
import {
  ArrowLeftOutlined,
  ArrowRightOutlined,
  SaveOutlined,
  EyeOutlined,
  RestOutlined,
  UserOutlined,
  DesktopOutlined,
  TabletOutlined,
  MobileOutlined,
  ProductOutlined,
  LogoutOutlined,
} from "@ant-design/icons";

import useStore from "@/store/index";
import { message, modal } from "@/store/hooks";
import logoSvg from "@/assets/logo.png";
import { getClient } from '@/client';

const client = getClient();
import "./index.css";


const Head = () => {
  const {
    history,
    jsonSchema,
    clientMode,
    setClientMode,
    updateJsonSchema,
    updateSelectedId,
  } = useStore();

  const undo = useStore((state) => state.undo);
  const redo = useStore((state) => state.redo);

  const canUndo = history.index > 0;
  const canRedo = history.index < history.queue.length - 1;

  const {
    token: { colorTextBase },
  } = theme.useToken();

  const naviagate = useNavigate();
  const params = useParams();

  const items = [
    {
      label: (
        <Link
          to={`/dashboard`}
        >
          应用管理
        </Link>
      ),
      key: "app",
      icon: <ProductOutlined />,
    },
    {
      type: "divider",
    },
    {
      label: (
        <Link to={`/profile`}>
          用户中心
        </Link>
      ),
      key: "user",
      icon: <UserOutlined />,
    },
    {
      type: "divider",
    },
    {
      label: "退出登录",
      key: "login",
      icon: <LogoutOutlined />,
    },
  ];

  // 撤销
  const onUndo = () => {
    undo();
  };

  // 恢复
  const onRedo = () => {
    redo();
  };

  const onSave = async () => {
    // 更新页面
    await client.database.updateByUniqueKey('page', "uid", params.pageId, {
      schema: jsonSchema,
    });
    message.success('保存成功');

    // message[status ? "success" : "error"](
    //   `${status ? "保存成功" : "保存失败"}`
    // );
  };

  const onPreview = async () => {
    await onSave();
    updateSelectedId("");
    naviagate(`/app/${params.appId}/page/${params.pageId}/preview`);
  };

  const onConfirmClear = () => {
    modal.confirm({
      title: "系统提醒",
      content: "即将清空画布且无法恢复，您确认清除吗？",
      onOk: onSureClear,
    });
  };

  const onSureClear = () => {
    localStorage.removeItem("JSONSchema");
    updateJsonSchema([]);
    message.success("清空成功");
  };

  const onClientChange = (client: string) => {
    setClientMode(client);
  };

  const goHome = () => {
    naviagate("/dashboard")
  }

  return (
    <Flex justify="space-between" align="center" className="design-header">
      <Button
        type="text"
        onClick={goHome}
        icon={
          <Avatar
            size={24}
            shape="square"
            src={<img src={logoSvg} alt="avatar" />}
          />
        }
      >
        ASTX
      </Button>

      <Flex justify="space-between" align="center">
        <Space>
          <Segmented
            options={[
              {
                label: "桌面",
                value: "desk",
                icon: <DesktopOutlined />,
              },
              {
                label: "iPad",
                value: "ipad",
                icon: <TabletOutlined />,
              },
              {
                label: "手机",
                value: "mobile",
                icon: <MobileOutlined />,
              },
            ]}
            value={clientMode}
            onChange={onClientChange}
          />
          <Divider type="vertical"></Divider>
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={onUndo}
            disabled={!canUndo}
          >
            撤销
          </Button>
          <Button
            type="text"
            icon={<ArrowRightOutlined />}
            onClick={onRedo}
            disabled={!canRedo}
          >
            恢复
          </Button>
          <Divider type="vertical"></Divider>
          <Button type="text" icon={<SaveOutlined />} onClick={onSave}>
            保存
          </Button>
          <Button type="text" icon={<EyeOutlined />} onClick={onPreview}>
            预览
          </Button>
          <Button type="text" icon={<RestOutlined />} onClick={onConfirmClear}>
            清空
          </Button>
          <Divider type="vertical"></Divider>

          <Dropdown menu={{ items }}>
            <Button
              type="text"
              icon={
                <Avatar
                  size={24}
                  shape="square"
                  src={<img src={logoSvg} alt="avatar" />}
                />
              }
            >
              winyh
            </Button>
          </Dropdown>
        </Space>
      </Flex>
    </Flex>
  );
};

export default Head;
