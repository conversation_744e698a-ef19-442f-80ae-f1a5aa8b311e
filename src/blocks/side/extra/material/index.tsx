import React,{ useState, useEffect } from "react";
import { Flex, Input, Tabs, Row, Col, Collapse, ConfigProvider } from "antd";
import winSchemas from "@/help/win";
import { Draggable } from "@/core/dnd";
import { genUniqueId, categorizeMaterials } from "@/utils";
import "./index.css";

// 分类配置数组，包含排序
const categoryList = [
  { key: "basic", label: "基础组件", sort: 1 },
  { key: "display", label: "数据展示", sort: 2 },
  { key: "layout", label: "布局", sort: 3 },
  { key: "entry", label: "表单录入", sort: 4 },
  { key: "container", label: "容器", sort: 5 },
  { key: "feedback", label: "反馈", sort: 6 },
  { key: "navigation", label: "导航", sort: 7 },
  { key: "others", label: "其他", sort: 8 },
  { key: "pie", label: "饼状图", sort: 9 },
  { key: "line", label: "折线图", sort: 10 },
  { key: "bar", label: "柱状图", sort: 11 },
  { key: "gauge", label: "仪表盘", sort: 12 },
  { key: "funnel", label: "漏斗图", sort: 13 },
  { key: "media", label: "多媒体", sort: 14 },
  { key: "list", label: "列表", sort: 15 },
  { key: "title", label: "标题", sort: 16 },
  { key: "button", label: "按钮", sort: 17 },
  { key: "cockpit", label: "驾驶舱", sort: 18 },
  { key: "popup", label: "弹窗", sort: 19 },
  { key: "border", label: "边框", sort: 20 },
  { key: "background", label: "背景", sort: 21 },
];

const Meterial = () => {
  const [activeKey, setActiveKey] = useState("common");
  const [components, setComponents] = useState<any>([]);

  let componentCache = winSchemas
    .map((item) => {
      return {
        ...item,
        uuid: genUniqueId(),
      };
    })
    .sort((a, b) => a.priority - b.priority);

  useEffect(() => {
    let nextComponents = componentCache.filter((item) => 
      item.group?.includes?.(activeKey) ?? false
    );

    setComponents(nextComponents);
  }, [activeKey]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let { value } = e.target;
    let nextComponents = [];
    if (value) {
      nextComponents = componentCache.filter(
        (item) =>
          item.title.includes(value) ||
          item.componentName?.toLowerCase().includes(value.toLowerCase())
      );
    } else {
      nextComponents = componentCache;
    }

    setComponents(nextComponents);
  };

  const handleTabChange = (key:string) => {
    setActiveKey(key);
  };

  // 根据 item 里的 group 字段分类，同一类放在 Collapse 组件里
  const categories = categorizeMaterials(components);

  type Category = {
    category: string;
    sort?: number;
    data: any[];
  };

  const CommonChild = ({ categories = [] }: { categories: Category[] }) => {
    const items = [...categories]
      .sort((a, b) => {
        const ca = categoryList.find(c => c.key === a.category);
        const cb = categoryList.find(c => c.key === b.category);
        return (ca?.sort ?? 999) - (cb?.sort ?? 999);
      })
      .map((sub) => {
        const label = categoryList.find(c => c.key === sub.category)?.label ?? sub.category;
        return {
          key: sub.category,
          label,
          children: (
            <Row gutter={[8, 8]}>
              {[...(sub?.data ?? [])]
                .sort((a, b) => (a.sort ?? 0) - (b.sort ?? 0))
                .map((item, index) => {
                  return (
                    <Col span={8} key={index}>
                      <div className="material-item">
                        <Draggable item={item}></Draggable>
                      </div>
                    </Col>
                  );
                })}
            </Row>
          ),
        };
      });

    return (
      <div style={{ height: "calc(100vh - 226px)", overflowY: "auto" }}>
        <ConfigProvider
          theme={{
            components: {
              Collapse: {
                headerPadding: "8px 0",
                contentPadding: 0,
              },
            },
          }}
        >
          <Collapse
            ghost
            items={items}
            defaultActiveKey={categoryList.map(c => c.key)}
          />
        </ConfigProvider>
      </div>
    );
  };

  const items = [
    {
      key: "common",
      label: "常用组件",
      children: <CommonChild categories={categories} />,
    },
    {
      key: "chart",
      label: "图表组件",
      children: <CommonChild categories={categories} />,
    },
    {
      key: "twin", // 数字孪生
      label: "大屏组件",
      children: <CommonChild categories={categories} />,
    },
    {
      key: "custome",
      label: "自定义组件",
      children: "自定义组件",
    },
    {
      key: "origin",
      label: "原子组件",
      children: "原子组件",
    },
  ];

  return (
    <Flex vertical gap={16}>
      <Input placeholder="搜索组件" allowClear onChange={handleSearchChange} />
      <Tabs activeKey={activeKey} items={items} onChange={handleTabChange} />
    </Flex>
  );
};

export { Meterial };

export default Meterial;