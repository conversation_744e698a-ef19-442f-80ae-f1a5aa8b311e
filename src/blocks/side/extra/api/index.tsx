import { useState } from "react";
import {
  Flex,
  Button,
  Input,
  Table,
  Tag,
  Space,
  Tabs,
  Popconfirm,
  Modal,
  Form,
  InputNumber,
  Alert,
  Switch,
  Select,
} from "antd";

import { PlusOutlined, SafetyCertificateOutlined } from "@ant-design/icons";

import "./index.css";

const { Search } = Input;

const DataApi = ({}) => {
  const [open, setOpen] = useState(false);
  const [interceptorOpen, setInterceptorOpen] = useState(false);

  const onChange = (key: string) => {
    // console.log(key);
  };

  const onEdit = () => {
    setOpen(true);
  };

  const onConfirm = () => {};

  const items = [
    {
      key: "1",
      label: "接口信息",
      children: (
        <Form
          name="basic"
          labelCol={{
            span: 8,
          }}
          wrapperCol={{
            span: 16,
          }}
          style={{
            maxWidth: 600,
          }}
          initialValues={{
            remember: true,
          }}
          autoComplete="off"
        >
          <Form.Item label="接口名称" name="name">
            <Input style={{ width: "100%" }} placeholder="接口名称" />
          </Form.Item>

          <Form.Item label="请求地址" name="url">
            <Input placeholder="请求地址" style={{ width: "100%" }} />
          </Form.Item>

          <Form.Item label="请求方式" name="method">
            <Select
              style={{ width: "100%" }}
              options={[
                {
                  label: "GET",
                  value: "get",
                },
                {
                  label: "POST",
                  value: "post",
                },
                {
                  label: "PUT",
                  value: "put",
                },
                {
                  label: "DELETE",
                  value: "delete",
                },
              ]}
            />
          </Form.Item>

          <Form.Item label="数据格式" name="format">
            <Select
              style={{ width: "100%" }}
              options={[
                {
                  label: "JSON",
                  value: "json",
                },
                {
                  label: "Form-Data",
                  value: "form-data",
                },
                {
                  label: "Form",
                  value: "form",
                },
              ]}
            />
          </Form.Item>

          <Form.Item label="参数替换" name="replace">
            <Select
              style={{ width: "100%" }}
              options={[
                {
                  label: "合并参数",
                  value: "merge",
                },
                {
                  label: "覆盖参数",
                  value: "override",
                },
                {
                  label: "保留参数",
                  value: "preserve",
                },
              ]}
            />
          </Form.Item>

          <Form.Item label="发送参数" name="headers">
            <Space>
              <Input style={{ width: "100%" }} placeholder="key" />
              <Input style={{ width: "100%" }} placeholder="value" />
            </Space>
          </Form.Item>

          <Form.Item
            label="开启代理"
            name="proxy"
            extra="开启接口代理对解决跨域问题很有用"
          >
            <Switch />
          </Form.Item>
        </Form>
      ),
    },
    {
      key: "2",
      label: "返回结构",
      children: (
        <Flex vertical gap={24}>
          <Alert
            message="用来定义接口返回结构，推荐结构：{ code: 0, data: {}, msg: '' }，如果没有结构，可以删除code/data/msg配置"
            type="info"
          />

          <Form
            name="basic"
            labelCol={{
              span: 8,
            }}
            wrapperCol={{
              span: 16,
            }}
            style={{
              maxWidth: 600,
            }}
            initialValues={{
              remember: true,
            }}
            autoComplete="off"
          >
            <Form.Item
              label="业务码"
              name="code"
              extra="接口返回业务状态码，默认是：code"
            >
              <Input style={{ width: "100%" }} placeholder="code" />
            </Form.Item>

            <Form.Item
              label="成功值"
              name="success_value"
              extra="接口返回成功时对应的状态码值，默认是：0"
            >
              <InputNumber placeholder="0" style={{ width: "100%" }} />
            </Form.Item>

            <Form.Item
              label="结果字段"
              name="data"
              extra="接口返回成功时对应的数据字段，默认是：data"
            >
              <Input style={{ width: "100%" }} />
            </Form.Item>

            <Form.Item
              label="报错字段"
              name="msg"
              extra="接口返回失败时对应的数据字段，默认是：msg"
            >
              <Input style={{ width: "100%" }} />
            </Form.Item>
          </Form>
        </Flex>
      ),
    },
    {
      key: "3",
      label: "拦截器",
      children: (
        <Form
          name="basic"
          labelCol={{
            span: 8,
          }}
          wrapperCol={{
            span: 16,
          }}
          style={{
            maxWidth: 600,
          }}
          initialValues={{
            remember: true,
          }}
          autoComplete="off"
        >
          <Form.Item label="请求头参数" name="headers">
            <Space>
              <Input style={{ width: "100%" }} placeholder="key" />
              <Input style={{ width: "100%" }} placeholder="value" />
            </Space>
          </Form.Item>

          <Form.Item label="请求超时" name="timeout">
            <InputNumber suffix="秒" style={{ width: "100%" }} />
          </Form.Item>

          <Form.Item label="超时提示" name="timeout_info">
            <Input style={{ width: "100%" }} />
          </Form.Item>

          <Form.Item label="请求适配" name="request_adapter">
            <Input.TextArea rows={4} />
          </Form.Item>

          <Form.Item label="返回适配" name="response_adapter">
            <Input.TextArea rows={4} />
          </Form.Item>
        </Form>
      ),
    },
    {
      key: "4",
      label: "消息提醒",
      children: (
        <Form
          name="basic"
          labelCol={{
            span: 8,
          }}
          wrapperCol={{
            span: 16,
          }}
          style={{
            maxWidth: 600,
          }}
          autoComplete="off"
        >
          <Form.Item label="默认成功提示" name="success_info">
            <Input style={{ width: "100%" }} placeholder="请求成功" />
          </Form.Item>

          <Form.Item label="默认失败提示" name="fail_info">
            <Input style={{ width: "100%" }} placeholder="请求失败" />
          </Form.Item>

          <Form.Item
            label="接口成功提示"
            name="interface_success_info"
            extra="开启接口成功提示后，会优先使用接口返回成功信息"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            label="接口失败提示"
            name="interface_fail_info"
            extra="开启接口失败提示后，会优先使用接口返回失败信息"
          >
            <Switch />
          </Form.Item>
        </Form>
      ),
    },
    {
      key: "5",
      label: "失败重试",
      children: (
        <Flex vertical gap={24}>
          <Alert
            message="如果服务端接口不稳定，可配置重试次数和间隔，当接口请求失败后，会继续尝试发起调用"
            type="info"
          />
          <Space>
            <InputNumber
              style={{ width: 200 }}
              placeholder="请输入重试次数"
              suffix="次"
            />
            <InputNumber
              style={{ width: 200 }}
              placeholder="请输入重试间隔"
              suffix="秒"
            />
          </Space>
        </Flex>
      ),
    },
  ];

  const dataSource = [
    {
      key: "1",
      name: "/api/article",
      description: "很棒的工具",
    },
    {
      key: "2",
      name: "/api/message/:id",
      description: "加油吧～",
    },
  ];

  const columns = [
    {
      title: "接口",
      dataIndex: "name",
      key: "name",
      render: (text) => {
        return (
          <Flex>
            <Tag color="blue" variant={false}>
              POST
            </Tag>
            {text}
          </Flex>
        );
      },
    },
    {
      title: "操作",
      dataIndex: "action",
      key: "action",
      width: 100,
      render: () => {
        return (
          <Space>
            <a onClick={onEdit}>修改</a>
            <Popconfirm
              title="系统提醒"
              description="您确定要删除该接口吗？"
              onConfirm={onConfirm}
            >
              <a>删除</a>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  const onInterceptor = () => {
    setInterceptorOpen(true);
  };

  const onAddApi = () => {
    setOpen(true);
  };

  const onFinish = (values) => {
    console.log("Success:", values);
  };
  const onFinishFailed = (errorInfo) => {
    console.log("Failed:", errorInfo);
  };

  return (
    <Flex gap={12} vertical>
      <Flex justify="space-between" gap={12}>
        <Button icon={<SafetyCertificateOutlined />} onClick={onInterceptor}>
          拦截器
        </Button>
        <Search placeholder="搜索接口" />

        <Modal
          title="拦截器配置"
          open={interceptorOpen}
          width="50%"
          closable
          mask={false}
          onOk={() => {}}
          onCancel={() => {
            setInterceptorOpen(false);
          }}
        >
          <Form
            name="basic"
            labelCol={{
              span: 8,
            }}
            wrapperCol={{
              span: 16,
            }}
            style={{
              maxWidth: 600,
            }}
            initialValues={{
              remember: true,
            }}
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
            autoComplete="off"
          >
            <Form.Item label="请求头参数" name="headers">
              <Space>
                <Input style={{ width: "100%" }} placeholder="key" />
                <Input style={{ width: "100%" }} placeholder="value" />
              </Space>
            </Form.Item>

            <Form.Item label="请求超时" name="timeout">
              <InputNumber suffix="秒" style={{ width: "100%" }} />
            </Form.Item>

            <Form.Item label="超时提示" name="timeout_info">
              <Input style={{ width: "100%" }} />
            </Form.Item>

            <Form.Item label="请求适配" name="request_adapter">
              <Input.TextArea rows={4} />
            </Form.Item>

            <Form.Item label="返回适配" name="response_adapter">
              <Input.TextArea rows={4} />
            </Form.Item>
          </Form>
        </Modal>
      </Flex>
      <Table
        dataSource={dataSource}
        columns={columns}
        size="small"
        virtual
        scroll={{ y: 600 }}
        pagination={false}
      />

      <Button icon={<PlusOutlined />} block onClick={onAddApi}>
        新增
      </Button>

      <Modal
        title="新增接口"
        open={open}
        width="50%"
        closable
        mask={false}
        onOk={() => {}}
        onCancel={() => {
          setOpen(false);
        }}
      >
        <Tabs defaultActiveKey="1" items={items} onChange={onChange} />
      </Modal>
    </Flex>
  );
};

export { DataApi };

export default DataApi;
