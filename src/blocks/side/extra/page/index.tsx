import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Flex, Button, Input, Table } from "antd";
import { DiffOutlined } from "@ant-design/icons";
import { getClient } from '@/client';
import {message} from "@/store/hooks"
const client = getClient();
import "./index.css";

const { Search } = Input;

const Page = ({}) => {
  // 使用状态来记录当前选中的行的索引
  const navigate = useNavigate();
  const { appId, pageId } = useParams();
  const [loading, setLoading] = useState(false);
  const [pageMeta, setPageMeta] = useState({
    list: [],
    pageSize: 10,
    current: 1,
    total: 0,
  });

  useEffect(() => {
    getData();
  }, [pageId]);


  const getData = async (params) => {
    setLoading(true);
    try {
      const pages = await client.database.read('page', params);
      setPageMeta(prev => ({
        ...prev,
        list:pages,
        total: pages.length
      }));
    } catch (error) {
      message.error('获取页面列表失败');
      console.error(error);
    } finally {
      setLoading(false);
    }
  }

  const onSearch = (value) => {
    getData({name:value});
  };

  const columns = [
    {
      title: "页面",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "描述",
      dataIndex: "description",
      key: "description",
    },
  ];

  // 行点击事件处理函数
  const onRowClick = (record, index) => {
    const { code } = record;
    const url = `/app/${appId}/page/${code}/design`;
    navigate(url);
  };

  const onRowClassName = (record, index) => {
    const {code} = record
    return code === pageId ? "high-bg" : null;
  };

  const onPageManage = () => {
    navigate("/page")
  };

  const onPaginationChange = (current, pageSize) => {
    setPageMeta((pre) => ({ ...pre, current, pageSize }));
    getData({ current, pageSize });
  };

  const onShowSizeChange = (current, pageSize) => {
    setPageMeta((pre) => ({ ...pre, current, pageSize }));
    getData({ current, pageSize });
  };

  return (
    <Flex vertical gap={16}>

      <Flex justify="space-between" gap={12}>
        <Button icon={<DiffOutlined />} onClick={onPageManage}>
          管理
        </Button>
        <Search
          placeholder={`搜索页面`}
          allowClear
          onSearch={onSearch}
        />
      </Flex>

      <Table
        onRow={(record, index) => ({
          onClick: () => onRowClick(record, index), // 绑定点击事件
        })}
        rowHoverable={false}
        rowClassName={(record, index) => onRowClassName(record, index)}
        rowKey={(record) => record.uid || record.id}
        dataSource={pageMeta.list}
        columns={columns}
        size="small"
        virtual
        scroll={{ y: 600 }}
        loading={loading}
        pagination={
          pageMeta.list.length > pageMeta.pageSize && {
            position: ["bottomCenter"],
            showSizeChanger: true,
            showQuickJumper: true,
            onChange: onPaginationChange,
            onShowSizeChange: onShowSizeChange,
            pageSize: pageMeta.pageSize, // 每页显示记录数
            current: pageMeta.current, // 当前页码
            total: pageMeta.total, // 总记录数
          }
        }
      />
    </Flex>
  );
};

export { Page };

export default Page;
