import { useState, useEffect } from "react";
import { <PERSON><PERSON>, Button, Tabs } from "antd";
import { SaveOutlined, RestOutlined } from "@ant-design/icons";
import useStore from "@/store/index";
import WinCode from "@/component/Code";

import "./index.css";

const Schema = ({}) => {
  const [initialValue, setInitialValue] = useState("");
  const [activeKey, setActiveKey] = useState("page");
  const jsonSchema = useStore((state) => state.jsonSchema);
  const selectedSomeSchema = useStore((state) => state.selectedSomeSchema);

  const onCodeChange = (delta, content) => {
    // console.log({ delta, content });
  };

  useEffect(() => {
    let textJsonSchema = JSON.stringify(jsonSchema, null, 2);
    let textSelectedSomeSchema = JSON.stringify(selectedSomeSchema, null, 2);
    let textSelectedProps = JSON.stringify(selectedSomeSchema?.props, null, 2);

    let value = textJsonSchema;

    if (activeKey === "component") {
      value = textSelectedSomeSchema;
    }

    if (activeKey === "props") {
      value = textSelectedProps;
    }

    setInitialValue(value);
  }, [activeKey]);

  const onChange = (key) => {
    setActiveKey(key);
  };

  return (
    <Flex vertical>
      <Tabs
        activeKey={activeKey}
        onChange={onChange}
        items={[
          {
            label: "页面 Schema",
            key: "page",
          },
          {
            label: "组件 Schema",
            key: "component",
          },
          {
            label: "组件 Props",
            key: "props",
          },
        ]}
      />

      <WinCode
        initialValue={initialValue}
        options={{ useWorker: false }}
        mode="json"
        style={{ height: "calc(100vh - 220px)" }}
        onChange={onCodeChange}
      />
      <div>
        <Flex justify="space-between">
          <Button icon={<RestOutlined />}>重置</Button>
          <Button type="primary" icon={<SaveOutlined />}>
            保存
          </Button>
        </Flex>
      </div>
    </Flex>
  );
};

export { Schema };

export default Schema;
