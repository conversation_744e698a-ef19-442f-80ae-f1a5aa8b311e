import { useEffect, useState } from "react";
import { Flex, Select, Input, Space, Button, Tree } from "antd";
import type { GetProps } from "antd";
import { FilterOutlined } from "@ant-design/icons";
import useStore from "@/store/index";
import { transformTree, moveElement, updateProperty } from "@/utils/index";

type DirectoryTreeProps = GetProps<typeof Tree.DirectoryTree>;

const { DirectoryTree } = Tree;

const { Option } = Select;

import "./index.css";

const SchemaTree = ({}) => {
  const jsonSchema = useStore((state) => state.jsonSchema);
  const [treeData, setTreeData] = useState(() => transformTree(jsonSchema));

  const { selectedSomeSchema, updateJsonSchema, updateSelectedId } = useStore();

  useEffect(() => {
    setTreeData(transformTree(jsonSchema));
  }, [jsonSchema]);

  const selectAfter = (
    <Select
      defaultValue="all"
      suffixIcon={<FilterOutlined />}
      style={{ width: "98px" }}
    >
      <Option value="all">全选</Option>
      <Option value="condition">条件渲染</Option>
      <Option value="circulate">循环渲染</Option>
      <Option value="locked">已锁定</Option>
      <Option value="hidden">已隐藏</Option>
    </Select>
  );

  const handleChange = (e) => {
    let { value } = e.target;
    if (!value) {
      setTreeData(transformTree(jsonSchema));
    } else {
      let nextJsonSchema = jsonSchema.filter(
        (item) =>
          item.componentName?.includes(value) ||
          item.label?.includes(value) ||
          (item.children &&
            Array.componentNameArray(item.children) &&
            item.children?.some(
              (child) =>
                child.componentName?.includes(value) ||
                child.label?.includes(value)
            ))
      );

      setTreeData(transformTree(nextJsonSchema));
    }
  };

  const onSelect: DirectoryTreeProps["onSelect"] = (keys, info) => {
    const {
      node: { uuid },
    } = info;
    updateSelectedId(`${uuid}`);
  };

  const onExpand: DirectoryTreeProps["onExpand"] = (keys, info) => {
    // console.log("Trigger Expand", keys, info);
  };

  const onDragEnter = () => {};

  const onDrop = (info) => {
    const { node, dragNode } = info;
    const { uuid: unqiueId } = dragNode;
    const { dragOver, dragOverGapBottom, dragOverGapTop, uuid } = node;

    // inside 0, top -1, bottom 1
    // const dropPos = node.pos.split("-");
    // const dropPosition =
    //   info.dropPosition - Number(dropPos[dropPos.length - 1]);

    let position =
      (dragOverGapTop && "before") ||
      (dragOverGapBottom && "after") ||
      (dragOver && "inside");

    let newSchema = moveElement(jsonSchema, unqiueId, uuid, position);
    updateJsonSchema(newSchema);
  };

  const onLeafShow = (event, node) => {
    event.stopPropagation();
    const newSchema = updateProperty(jsonSchema, node?.uuid, "show", true);
    updateJsonSchema(newSchema);
  };

  const renderNodeTitle = (node: any) => (
    <>
      <Flex justify="space-between" align="center">
        <span>{node.title}</span>
        {node?.show === false && (
          <Space>
            <Button
              size="small"
              color="primary"
              variant="filled"
              onClick={(event) => onLeafShow(event, node)}
            >
              显示
            </Button>
          </Space>
        )}
      </Flex>
    </>
  );

  return (
    <Flex vertical gap={16}>
      <Input
        placeholder="过滤节点"
        allowClear
        addonAfter={selectAfter}
        onChange={handleChange}
      />
      <DirectoryTree
        showLine
        showIcon={false}
        defaultExpandAll
        selectedKeys={[selectedSomeSchema?.uuid]}
        onSelect={onSelect}
        onExpand={onExpand}
        draggable
        height={800}
        onDragEnter={onDragEnter}
        onDrop={onDrop}
        treeData={treeData.map((node) => {
          const mapNode = (node) => ({
            ...node,
            title: renderNodeTitle(node),
            children: node.children?.map(mapNode),
          });
          return mapNode(node);
        })}
      />
    </Flex>
  );
};

export { SchemaTree };

export default SchemaTree;
