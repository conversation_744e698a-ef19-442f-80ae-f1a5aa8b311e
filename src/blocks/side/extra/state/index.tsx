import { Flex, Space, Button } from "antd";
import { SaveOutlined, RestOutlined } from "@ant-design/icons";
import WinCode from "@/component/Code";

import "./index.css";

const State = ({}) => {
  const onCodeChange = (delta, content) => {
    // console.log({ delta, content });
  };

  return (
    <Flex vertical gap={8}>
      <WinCode
        initialValue={`{ "themeMode": "dark" }`}
        options={{ useWorker: false }}
        mode="json"
        onChange={onCodeChange}
      />
      <Flex justify="space-between">
        <Button size="small" icon={<RestOutlined />}>
          重置
        </Button>
        <Button size="small" type="primary" icon={<SaveOutlined />}>
          保存
        </Button>
      </Flex>
    </Flex>
  );
};

export { State };

export default State;
