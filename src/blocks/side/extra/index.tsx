import { Button, Flex } from "antd";
import { CloseOutlined } from "@ant-design/icons";
import useStore from "@/store";
import Meterial from "./material";
import SchemaTree from "./tree";
import OriginCode from "./code";
import Dnd from "./dnd";
import JSONSchema from "./schema";
import AppState from "./state";
import AppPage from "./page";
import AppHelp from "./help";
import Agent from "./agent";
import DataApi from "./api";

import "./index.css";

const SideExtra = () => {
  const { sideExtra, updateSideExtra } = useStore();
  const { show, current } = sideExtra;

  const handleClose = () => {
    updateSideExtra({
      show: false,
      current: { key: "", label: "" },
    });
  };

  return (
    show && (
      <div className="side-extra" style={{ width: current?.key === "dnd" ? 800 : 360 }}>
        <div className="side-extra-top">
          <Flex justify="space-between">
            <div className="side-extra-top-title">{current.label}</div>
            <Button
              type="text"
              size="small"
              icon={<CloseOutlined />}
              onClick={handleClose}
            ></Button>
          </Flex>
        </div>
        <div className="side-extra-content">
          {current?.key === "material" && <Meterial />}
          {current?.key === "tree" && <SchemaTree />}
          {current?.key === "code" && <OriginCode />}
          {current?.key === "dnd" && <Dnd />}
          {current?.key === "schema" && <JSONSchema />}
          {current?.key === "state" && <AppState />}
          {current?.key === "page" && <AppPage />}
          {current?.key === "help" && <AppHelp />}
          {current?.key === "api" && <DataApi />}
          {current?.key === "agent" && <Agent />}
        </div>
      </div>
    )
  );
};

export { SideExtra };

export default SideExtra;
