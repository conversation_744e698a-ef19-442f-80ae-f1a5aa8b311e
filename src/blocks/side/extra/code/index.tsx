import { useState } from "react";
import { Flex, Space, Button, Tabs } from "antd";
import { SaveOutlined, RestOutlined } from "@ant-design/icons";

import WinCode from "@/component/Code";

import "./index.css";

const Code = ({}) => {
  const [mode, setMode] = useState("javascript");
  const [initialValue, setInitialValue] = useState('console.log("winyh")');
  const [activeKey, setActiveKey] = useState("tsx");

  const onCodeChange = (delta, content) => {
    // console.log({ delta, content });
  };

  const items = [
    {
      key: "tsx",
      label: "index.tsx",
    },
    {
      key: "css",
      label: "index.css",
    },
  ];

  const onTabChange = (key) => {
    setActiveKey(key);

    if (key === "tsx") {
      setMode("javascript");
      setInitialValue('console.log("winyh")');
    }

    if (key === "css") {
      setMode("css");
      setInitialValue(".style { margin:12px 0; }");
    }
  };

  return (
    <Flex vertical>
      <Tabs items={items} activeKey={activeKey} onChange={onTabChange} />
      <WinCode
        initialValue={initialValue}
        options={{ useWorker: false }}
        mode={mode}
        style={{ height: "calc(100vh - 220px)" }}
        onChange={onCodeChange}
      />
      <Flex justify="space-between">
        <Button icon={<RestOutlined />}>重置</Button>
        <Button type="primary" icon={<SaveOutlined />}>
          保存
        </Button>
      </Flex>
    </Flex>
  );
};

export { Code };

export default Code;
