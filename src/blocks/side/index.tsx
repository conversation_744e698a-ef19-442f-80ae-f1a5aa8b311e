import { <PERSON><PERSON>, <PERSON><PERSON>, Di<PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import {
  PlusCircleOutlined,
  ApartmentOutlined,
  ApiOutlined,
  DatabaseOutlined,
  QuestionCircleOutlined,
  NumberOutlined,
  CodeOutlined,
  FilePptOutlined,
  OpenAIOutlined,
  UngroupOutlined,
} from "@ant-design/icons";

import useStore from "@/store";
import { SideExtra } from "./extra";

import "./index.css";

const Side = () => {
  const { sideExtra, updateSideExtra } = useStore();

  const onExtraExpand = (item: any) => {
    const { key, label } = item;
    updateSideExtra({
      show: true,
      current: { key, label },
    });
  };

  const sideActionTop = [
    {
      label: "组件库",
      key: "material",
      icon: <PlusCircleOutlined />,
    },
    {
      label: "层级树",
      key: "tree",
      icon: <ApartmentOutlined />,
    },
    {
      label: "拖拽辅助",
      key: "dnd",
      icon: <UngroupOutlined />,
    },
    {
      label: "源码",
      key: "code",
      icon: <CodeOutlined />,
    },
    {
      label: "状态机",
      key: "state",
      icon: <DatabaseOutlined />,
    },
    {
      label: "API 接口",
      key: "api",
      icon: <ApiOutlined />,
    },
    {
      label: "divider",
      key: "divider-page",
    },
    {
      label: `页面集合`,
      key: "page",
      icon: <FilePptOutlined />,
    },
  ];

  const sideActionBottom = [
    {
      label: "AI 功能",
      key: "agent",
      icon: <OpenAIOutlined />,
    },
    {
      label: "JSON 数据",
      key: "schema",
      icon: <NumberOutlined />,
    },
    {
      label: "帮助",
      key: "help",
      icon: <QuestionCircleOutlined />,
    },
  ];

  const { current } = sideExtra;

  return (
    <Flex
      vertical
      justify="space-between"
      align="center"
      className="design-sider"
    >
      <Flex vertical justify="space-between" align="center" gap={16}>
        {sideActionTop.map((item) => {
          if (item.label !== "divider") {
            return (
              <Tooltip title={item.label} placement="right" key={item.key}>
                <Button
                  type={current.key === item.key ? "primary" : "text"}
                  icon={item.icon}
                  onClick={() => onExtraExpand(item)}
                ></Button>
              </Tooltip>
            );
          } else {
            return <Divider key={item.key} style={{ margin: 8 }}></Divider>;
          }
        })}
      </Flex>

      <Flex vertical justify="space-between" align="center" gap={12}>
        {sideActionBottom.map((item) => {
          if (item.label !== "divider") {
            return (
              <Tooltip title={item.label} placement="right" key={item.key}>
                <Button
                  type={current.key === item.key ? "primary" : "text"}
                  icon={item.icon}
                  onClick={() => onExtraExpand(item)}
                ></Button>
              </Tooltip>
            );
          } else {
            return <Divider key={item.key}></Divider>;
          }
        })}
      </Flex>

      <SideExtra />
    </Flex>
  );
};

export default Side;
