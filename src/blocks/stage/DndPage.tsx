import { useEffect } from "react";
import { SchemaRender } from "@/core/render";
import { Droppable } from "@/core/dnd";
import EmptyHint from "@/component/EmptyHint";
import { ShortcutManager } from "@/utils/keyboard";
import { Spin } from "antd";

import useStore from "@/store/index";
import { removeElementByUuid, getFatherOrSiblingByUuid } from "@/utils";

const DndPage = ({ schema = [] }) => {
  const {
    loading,
    jsonSchema,
    updateSelectedId,
    updateJsonSchema,
    selectedSomeSchema,
  } = useStore();

  // 删除元素
  const handleDelete = () => {
    const nextComponent = getFatherOrSiblingByUuid(
      jsonSchema,
      selectedSomeSchema?.uuid
    );
    if (nextComponent) {
      updateSelectedId(`${nextComponent?.uuid}`);
    }
    const newSchema = removeElementByUuid(jsonSchema, selectedSomeSchema?.uuid);
    updateJsonSchema(newSchema);
  };

  // 在 React 组件中使用
  useEffect(() => {
    const target = document.getElementById("dnd_page");
    const shortcutManager = new ShortcutManager(target);
    const cleanup = shortcutManager.registerShortcut(
      "delete",
      { key: "Backspace", windows: ["Delete"], mac: ["Backspace"] },
      handleDelete
    );

    return () => shortcutManager.unregisterShortcut("delete"); // 组件卸载时清理
  }, []);

  return (
    <Droppable id="dnd_page">
      <Spin spinning={loading} tip="数据加载中">
        <div id="dnd_page">
          {schema.length > 0 ? <SchemaRender schema={schema} /> : <EmptyHint />}
        </div>
      </Spin>
    </Droppable>
  );
};

export { DndPage };

export default DndPage;
