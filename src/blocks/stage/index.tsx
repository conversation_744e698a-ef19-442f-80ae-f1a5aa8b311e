import { IpadSimulation, MobileSimulation } from "@/core/simulation";
import useStore from "@/store/index";

import DndPage from "./DndPage";

import "./index.css";

const Stage = ({}) => {
  const {
    jsonSchema,
    clientMode,
    selectedId,
    updateSelectedId,
    updateRightActiveKey,
  } = useStore();

  const handleActiveClick = (event) => {
    event.stopPropagation();
    var elementWithDnd = event.target.closest("[data-dnd]");
    if (elementWithDnd) {
      var id = elementWithDnd.getAttribute("data-dnd");
      updateSelectedId(id);
      if (selectedId !== id) {
        updateRightActiveKey("props");
      }
    } else {
      console.log("data-dnd not found.");
    }
  };

  return (
    <div className="design-stage" id="design-stage" onClick={handleActiveClick}>
      {clientMode !== "desk" ? (
        <div className="simulation">
          {clientMode === "ipad" && (
            <IpadSimulation>{<DndPage schema={jsonSchema} />}</IpadSimulation>
          )}
          {clientMode === "mobile" && (
            <MobileSimulation>
              {<DndPage schema={jsonSchema} />}
            </MobileSimulation>
          )}
        </div>
      ) : (
        <DndPage schema={jsonSchema} />
      )}
    </div>
  );
};

export default Stage;
