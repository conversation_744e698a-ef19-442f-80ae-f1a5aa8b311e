import { useState } from "react";
import { Segmented } from "antd";

const SegmentedSetter = (props) => {
  const { value, options = [], onChange } = props;
  const [segment, setSegment] = useState("");
  const onSegmentedChange = (changedValue) => {
    setSegment(changedValue);
    onChange?.(changedValue);
  };

  return (
    <Segmented
      block
      options={options}
      value={segment || value}
      onChange={onSegmentedChange}
    />
  );
};

export { SegmentedSetter };
export default SegmentedSetter;
