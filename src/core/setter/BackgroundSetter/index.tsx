import { useState } from "react";
import { Row, Col, Image, ColorPicker, Upload, Input } from "antd";
import { CloudUploadOutlined } from "@ant-design/icons";

const BackgroundSetter = (props) => {
  const { value, onChange } = props;
  const [backgroundColor, setBackgroundColor] = useState(
    value?.["background-color"]
  );
  const [backgroundImage, setBackgroundImage] = useState(
    value?.["background-image"]
  );

  const triggerChange = (changedValue) => {
    let targetValue = {
      "background-color": backgroundColor,
      "background-image": backgroundImage,
      ...changedValue,
    };
    if (
      targetValue?.["background-color"] === undefined &&
      targetValue?.["background-image"] === undefined
    ) {
      targetValue = undefined;
    }

    onChange?.(targetValue);
  };

  const onColorChange = (changedValue) => {
    if (changedValue?.cleared) {
      triggerChange({
        "background-color": undefined,
      });
    } else {
      let value = changedValue.toHexString();
      triggerChange({
        "background-color": value,
      });
      setBackgroundColor(value);
    }
  };

  const onBackgroundImageChange = async ({ file }) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj);
    }
    setBackgroundImage(file.url || file.preview);
    triggerChange({
      "background-image": file.preview,
    });
  };

  const getBase64 = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = (error) => reject(error);
    });
  };

  return (
    <Row>
      <Col span={10}>
        <ColorPicker
          defaultValue="#1677ff"
          value={backgroundColor}
          showText
          allowClear
          style={{ width: "100%", justifyContent: "start" }}
          onChange={onColorChange}
        />
      </Col>
      <Col span={13} offset={1}>
        <Input
          value={backgroundImage}
          allowClear
          onChange={(e) => {
            setBackgroundImage(e.target.value);
          }}
          addonAfter={
            backgroundImage ? (
              <Image width={30} height={30} src={backgroundImage} />
            ) : (
              <Upload
                onChange={onBackgroundImageChange}
                accept="image/*"
                fileList={[]}
              >
                <CloudUploadOutlined style={{ cursor: "pointer" }} />
              </Upload>
            )
          }
        />
      </Col>
    </Row>
  );
};

export { BackgroundSetter };
export default BackgroundSetter;
