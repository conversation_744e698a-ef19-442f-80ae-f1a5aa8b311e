import {
  Select,
  Input,
  Tooltip,
  Divider,
  InputNumber,
  Form,
  ConfigProvider,
  Row,
  Col,
} from "antd";
import { useEffect, useState } from "react";
import { BarsOutlined, AppstoreOutlined } from "@ant-design/icons";

import {
  updateProps,
  styleToObject,
  objectToStyle,
  classNameStr2Arr,
} from "@/utils/index";

import {
  getComponentConfigurePropsSchema,
  haveKeyInComponent,
} from "@/utils/schema";

import "./index.css";

import { NumberUnitSetter } from "../NumberUnitSetter";
import { SegmentedSetter } from "../SegmentedSetter";
import { MarginPaddingSetter } from "../MarginPaddingSetter";
import { FontSetter } from "../FontSetter";
import { ShadowSetter } from "../ShadowSetter";
import { BorderSetter } from "../BorderSetter";
import { BorderRadiusSetter } from "../BorderRadiusSetter";
import { BackgroundSetter } from "../BackgroundSetter";
import { OverflowSetter } from "../OverflowSetter";

import useStore from "@/store";

const StyleSetter = () => {
  const [form] = Form.useForm();
  const [pseudo, setPseudo] = useState("default");
  const [className, setClassName] = useState([]);
  const [style, setStyle] = useState("");
  const [classNameOptions, setClassNameOptions] = useState([]);
  const { selectedSomeSchema, jsonSchema, selectedId, updateJsonSchema } =
    useStore();

  const [initialValues, setInitialValues] = useState({
    default: {
      pseudo: "default",
      width: 0,
      heiht: 0,
      display: "block",
      margin: "",
      padding: "",
      font: "",
      background: "",
      "box-shadow": "",
      border: "none",
      opacity: 1,
      cursor: "default",
    },
    active: { cursor: "default" },
    focus: { cursor: "default" },
    hover: { cursor: "default" },
  });

  useEffect(() => {
    let { props, name } = selectedSomeSchema;
    if (!name) return;
    let { style, className } = props || {};
    let classOptions = classNameStr2Arr(className);
    let init = props[name] || {};
    setStyle(objectToStyle(style));
    setClassName(classOptions.map((item) => item.value));
    setClassNameOptions(classOptions);
    let values = { ...init?.default, pseudo: "default" };
    setPseudo("default");

    // Update initialValues and reset form fields
    setInitialValues((prev) => ({ ...prev, default: { ...values } }));
    form.resetFields(); // Reset form fields to reflect new initial values
    console.log({ values });
    if (!values.cursor) {
      values.cursor = "default";
    }
    form.setFieldsValue(values);
  }, [selectedId]);

  // 当前组件所有 props，包含未挂载的
  const originaProps = getComponentConfigurePropsSchema(
    selectedSomeSchema?.componentName
  );

  const haveStyle = haveKeyInComponent(originaProps, "style");
  const haveClassName = haveKeyInComponent(originaProps, "className");

  // 字段值更新触发回调
  const onValuesChange = (_, allValues) => {
    let { name } = selectedSomeSchema;
    let result = {
      ...initialValues,
      [pseudo]: allValues,
    };
    setInitialValues(result);
    let newSchema = updateProps(jsonSchema, selectedId, name, result);
    console.log(result);
    updateJsonSchema(newSchema);
  };

  // 行内样式
  const onStyleChange = (event) => {
    const { value } = event.target;
    let newSchema = updateProps(
      jsonSchema,
      selectedId,
      "style",
      styleToObject(value)
    );
    console.log({ newSchema });
    setStyle(value);
    updateJsonSchema(newSchema);
  };

  // 伪类监听
  const onPseudoChange = (value) => {
    let { props, name } = selectedSomeSchema;
    if (!name) return;
    let init = props[name] || {};
    let values = { ...init?.[value] };
    if (!values.cursor) {
      values.cursor = "default";
    }
    let result = {
      ...initialValues,
      [value]: values,
    };
    setPseudo(value);
    setInitialValues(result);
    form.resetFields();
    form.setFieldsValue(values);
  };

  const onClassNameChange = (value) => {
    let newSchema = updateProps(
      jsonSchema,
      selectedId,
      "className",
      value.join(" ")
    );
    updateJsonSchema(newSchema);
    setClassName(value);
  };

  return (
    <ConfigProvider
      theme={{
        components: {
          Form: {
            itemMarginBottom: 12,
          },
        },
      }}
    >
      {haveStyle && (
        <Row align="middle" style={{ marginBottom: 12 }}>
          <Col span={6}>行内</Col>
          <Col span={18}>
            <Input.TextArea
              value={style}
              rows={1}
              onChange={onStyleChange}
              style={{ width: "100%" }}
              allowClear
              placeholder="请输入行内样式"
            />
          </Col>
        </Row>
      )}

      {haveClassName && (
        <Row align="middle" style={{ marginBottom: 12 }}>
          <Col span={6}>样式类</Col>
          <Col span={18}>
            <Select
              allowClear
              style={{ width: "100%" }}
              mode="multiple"
              value={className}
              onChange={onClassNameChange}
              placeholder="请选择className"
              options={classNameOptions}
            />
          </Col>
        </Row>
      )}

      {(haveStyle || haveClassName) && <Divider style={{ margin: 16 }} />}

      <Row align="middle" style={{ marginBottom: 12 }}>
        <Col span={6}>状态</Col>
        <Col span={18}>
          <Select
            value={pseudo}
            onChange={onPseudoChange}
            style={{ width: "100%" }}
            options={[
              {
                label: "默认",
                value: "default",
              },
              {
                label: ":hover",
                value: "hover",
              },
              {
                label: ":focus",
                value: "focus",
              },
              {
                label: ":active",
                value: "active",
              },
            ]}
          />
        </Col>
      </Row>

      <Form
        form={form}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
        labelAlign="left"
        colon={false}
        onValuesChange={onValuesChange}
      >
        <Form.Item name="width" label="宽度">
          <NumberUnitSetter
            min={0}
            options={[
              {
                label: "px",
                value: "px",
              },
              {
                label: "%",
                value: "%",
              },
              {
                label: "em",
                value: "em",
              },
              {
                label: "rem",
                value: "rem",
              },
              {
                label: "vw",
                value: "vw",
              },
              {
                label: "vh",
                value: "vh",
              },
            ]}
          />
        </Form.Item>

        <Form.Item name="height" label="高度">
          <NumberUnitSetter
            min={0}
            options={[
              {
                label: "px",
                value: "px",
              },
              {
                label: "%",
                value: "%",
              },
              {
                label: "em",
                value: "em",
              },
              {
                label: "rem",
                value: "rem",
              },
              {
                label: "vw",
                value: "vw",
              },
              {
                label: "vh",
                value: "vh",
              },
            ]}
          />
        </Form.Item>

        <Form.Item name="display" label="显示">
          <SegmentedSetter
            options={[
              {
                value: "block",
                icon: (
                  <Tooltip title="block">
                    <BarsOutlined />
                  </Tooltip>
                ),
              },
              {
                value: "inline-block",
                icon: (
                  <Tooltip title="inline-block">
                    <AppstoreOutlined />
                  </Tooltip>
                ),
              },
              {
                value: "inline",
                icon: (
                  <Tooltip title="inline">
                    <BarsOutlined />
                  </Tooltip>
                ),
              },
              {
                value: "flex",
                icon: (
                  <Tooltip title="flex">
                    <AppstoreOutlined />
                  </Tooltip>
                ),
              },
            ]}
          />
        </Form.Item>

        <Form.Item name="margin" label="外边距">
          <MarginPaddingSetter
            options={[
              {
                label: "无",
                value: "none",
              },
              {
                label: "自定义",
                value: "custom",
              },
              {
                label: "大",
                value: "large",
              },
              {
                label: "中",
                value: "middle",
              },
              {
                label: "小",
                value: "small",
              },
            ]}
          />
        </Form.Item>

        <Form.Item name="padding" label="内边距">
          <MarginPaddingSetter
            options={[
              {
                label: "无",
                value: "none",
              },
              {
                label: "自定义",
                value: "custom",
              },
              {
                label: "大",
                value: "large",
              },
              {
                label: "中",
                value: "middle",
              },
              {
                label: "小",
                value: "small",
              },
            ]}
          />
        </Form.Item>

        <Form.Item label="文字" name="font">
          <FontSetter
            options={[
              {
                label: "默认",
                value: "none",
              },
              {
                label: "自定义",
                value: "custom",
              },
              {
                label: "一级标题",
                value: "h1",
              },
              {
                label: "二级标题",
                value: "h2",
              },
              {
                label: "三级标题",
                value: "h3",
              },
            ]}
          />
        </Form.Item>

        <Form.Item label="背景" name="background">
          <BackgroundSetter />
        </Form.Item>

        <Form.Item label="阴影" name="box-shadow">
          <ShadowSetter
            options={[
              {
                label: "无",
                value: "none",
              },
              {
                label: "自定义",
                value: "custom",
              },
              {
                label: "大",
                value: "large",
              },
              {
                label: "中",
                value: "middle",
              },
              {
                label: "小",
                value: "small",
              },
            ]}
          />
        </Form.Item>

        <Form.Item label="边框" name="border">
          <BorderSetter
            options={[
              {
                label: "无",
                value: "none",
              },
              {
                label: "自定义",
                value: "custom",
              },
            ]}
          />
        </Form.Item>

        <Form.Item label="圆角" name="border-radius">
          <BorderRadiusSetter
            options={[
              {
                label: "无",
                value: "none",
              },
              {
                label: "自定义",
                value: "custom",
              },
            ]}
          />
        </Form.Item>

        <Form.Item label="溢出" name="overflow">
          <OverflowSetter
            options={[
              {
                label: "默认",
                value: "none",
              },
              {
                label: "自定义",
                value: "custom",
              },
            ]}
          />
        </Form.Item>

        <Form.Item label="不透明度" name="opacity">
          <InputNumber max={1} min={0} step={0.1} style={{ width: "100%" }} />
        </Form.Item>

        <Form.Item label="鼠标手势" name="cursor">
          <Select
            allowClear
            style={{ width: "100%" }}
            options={[
              {
                label: "默认",
                value: "default",
              },
              {
                label: "手型",
                value: "pointer",
              },
              {
                label: "问号",
                value: "help",
              },
            ]}
          />
        </Form.Item>
      </Form>
    </ConfigProvider>
  );
};

export { StyleSetter };
export default StyleSetter;
