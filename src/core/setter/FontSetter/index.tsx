import { useState } from "react";
import { <PERSON>, <PERSON>, Toolt<PERSON>, Select, Flex, ColorPicker } from "antd";
import { NumberUnitSetter } from "../NumberUnitSetter";

const FontSetter = (props) => {
  const { value, options = [], onChange } = props;
  const [selectedValue, setSelectedValue] = useState(
    value?.selectedValue || "none"
  );
  const [fontSize, setFontSize] = useState({
    number: value?.fontSize?.number || 14,
    unit: "px",
  });
  const [fontFamily, setFontFamily] = useState(value?.fontFamily || "Arial");
  const [fontWeight, setFontWeight] = useState(value?.fontWeight || "normal");
  const [color, setColor] = useState(value?.color || "#1677ff");
  const [letterSpacing, setLetterSpacing] = useState({
    number: value?.letterSpacing?.number || 0,
    unit: "px",
  });
  const [lineHeight, setLineHeight] = useState({
    number: value?.lineHeight?.number || 0,
    unit: "px",
  });
  const [textAlign, setTextAlign] = useState(value?.textAlign || "left");
  const [textDecoration, setTextDecoration] = useState(
    value?.textDecoration || "none"
  );
  const [writingMode, setWritingMode] = useState(
    value?.writingMode || "horizontal-tb"
  );
  const [fontStyle, setFontStyle] = useState(value?.fontStyle || "normal");

  const onSelectChange = (selectedValue) => {
    let fontSizeCache = {};
    switch (selectedValue) {
      case "h1":
        fontSizeCache = { number: 32, unit: "px" };
        setFontSize(fontSizeCache);
        setFontWeight("bold");
        break;

      case "h2":
        fontSizeCache = { number: 24, unit: "px" };
        setFontSize(fontSizeCache);
        setFontWeight("bold");
        break;

      case "h3":
        fontSizeCache = { number: 18, unit: "px" };
        setFontSize(fontSizeCache);
        setFontWeight("bold");
        break;
      case undefined:
        selectedValue = "none";
        break;

      default:
        break;
    }

    setSelectedValue(selectedValue);
    triggerChange({
      fontSize: fontSizeCache,
      selectedValue: selectedValue,
    });
  };

  const triggerChange = (changedValue) => {
    let triggerValue = {
      fontFamily,
      fontWeight,
      fontSize,
      color,
      letterSpacing,
      lineHeight,
      textAlign,
      textDecoration,
      writingMode,
      fontStyle,
      selectedValue,
      ...changedValue,
    };

    if (
      changedValue.selectedValue === "none" ||
      changedValue.selectedValue === undefined
    ) {
      triggerValue = undefined;
    }

    if (
      changedValue.selectedValue === "h1" ||
      changedValue.selectedValue === "h2" ||
      changedValue.selectedValue === "h3"
    ) {
      triggerValue = {
        fontSize: triggerValue.fontSize,
        selectedValue: triggerValue.selectedValue,
      };
    }

    onChange?.(triggerValue);
  };

  const onFontFamilyChange = (changedValue) => {
    setFontFamily(changedValue);
    triggerChange({
      fontFamily: changedValue,
    });
  };

  const onFontWeightChange = (changedValue) => {
    setFontWeight(changedValue);
    triggerChange({
      fontWeight: changedValue,
    });
  };

  const onFontSizeChange = (changedValue) => {
    setFontSize(changedValue);
    triggerChange({
      left: changedValue,
    });
  };

  const onColorChange = (changedValue) => {
    setColor(changedValue);
    triggerChange({
      color: changedValue,
    });
  };

  const onLetterSpacingChange = (changedValue) => {
    setLetterSpacing(changedValue);
    triggerChange({
      letterSpacing: changedValue,
    });
  };

  const onLineHeightChange = (changedValue) => {
    setLineHeight(changedValue);
    triggerChange({
      lineHeight: changedValue,
    });
  };

  const onTextAlignChange = (changedValue) => {
    setTextAlign(changedValue);
    triggerChange({
      textAlign: changedValue,
    });
  };

  const onTextDecorationChange = (changedValue) => {
    setTextDecoration(changedValue);
    triggerChange({
      textDecoration: changedValue,
    });
  };

  const onWritingModeChange = (changedValue) => {
    setWritingMode(changedValue);
    triggerChange({
      writingMode: changedValue,
    });
  };

  const onFontStyleChange = (changedValue) => {
    setFontStyle(changedValue);
    triggerChange({
      fontStyle: changedValue,
    });
  };

  return (
    <Row>
      <Select
        allowClear
        style={{ width: "100%" }}
        options={options}
        value={selectedValue}
        onChange={onSelectChange}
      />
      {selectedValue === "custom" && (
        <Col span={24}>
          <div className="secondary-setter">
            <Flex vertical gap={12}>
              <Row gutter={12}>
                <Col span={8} className="label-flex label-flex-secondary">
                  <Tooltip title="font-family">字体</Tooltip>
                </Col>
                <Col span={16}>
                  <Select
                    style={{ width: "100%" }}
                    defaultValue="Arial"
                    onChange={onFontFamilyChange}
                    options={[
                      {
                        label: "宋体",
                        value: "Arial",
                      },
                      {
                        label: "微软雅黑",
                        value: "microsoft yahei",
                      },
                      {
                        label: "仿宋",
                        value: "FangSong",
                      },
                      {
                        label: "楷体",
                        value: "Kai",
                      },
                    ]}
                  />
                </Col>
              </Row>

              <Row gutter={12}>
                <Col span={8} className="label-flex label-flex-secondary">
                  <Tooltip title="font-weight">字重</Tooltip>
                </Col>
                <Col span={16}>
                  <Select
                    style={{ width: "100%" }}
                    defaultValue="normal"
                    onChange={onFontWeightChange}
                    options={[
                      {
                        label: "正常",
                        value: "normal", // 400等值
                      },
                      {
                        label: "加粗",
                        value: "bold", // 700等值
                      },
                      {
                        label: "继承更细",
                        value: "lighter",
                      },
                      {
                        label: "继承更粗",
                        value: "bolder",
                      },
                      {
                        label: "500",
                        value: "500",
                      },
                      {
                        label: "600",
                        value: "600",
                      },
                    ]}
                  />
                </Col>
              </Row>

              <Row gutter={12}>
                <Col span={8} className="label-flex label-flex-secondary">
                  <Tooltip title="color">字体颜色</Tooltip>
                </Col>
                <Col span={16}>
                  <ColorPicker
                    defaultValue="#1677ff"
                    showText
                    allowClear
                    onChange={onColorChange}
                    style={{ width: "100%", justifyContent: "start" }}
                  />
                </Col>
              </Row>

              <Row gutter={12}>
                <Col span={8} className="label-flex label-flex-secondary">
                  <Tooltip title="font-size">字体大小</Tooltip>
                </Col>
                <Col span={16}>
                  <NumberUnitSetter
                    value={fontSize || value.fontSize}
                    onChange={onFontSizeChange}
                    options={[
                      {
                        label: "px",
                        value: "px",
                      },
                      {
                        label: "%",
                        value: "%",
                      },
                      {
                        label: "em",
                        value: "em",
                      },
                    ]}
                  />
                </Col>
              </Row>

              <Row gutter={12}>
                <Col span={8} className="label-flex label-flex-secondary">
                  <Tooltip title="letter-spacing">间距</Tooltip>
                </Col>
                <Col span={16}>
                  <NumberUnitSetter
                    value={fontSize || value.fontSize}
                    onChange={onLetterSpacingChange}
                    options={[
                      {
                        label: "px",
                        value: "px",
                      },
                      {
                        label: "%",
                        value: "%",
                      },
                      {
                        label: "em",
                        value: "em",
                      },
                    ]}
                  />
                </Col>
              </Row>

              <Row gutter={12}>
                <Col span={8} className="label-flex label-flex-secondary">
                  <Tooltip title="line-hight">行高</Tooltip>
                </Col>
                <Col span={16}>
                  <NumberUnitSetter
                    value={fontSize || value.fontSize}
                    onChange={onLineHeightChange}
                    options={[
                      {
                        label: "px",
                        value: "px",
                      },
                      {
                        label: "%",
                        value: "%",
                      },
                      {
                        label: "em",
                        value: "em",
                      },
                    ]}
                  />
                </Col>
              </Row>

              <Row gutter={12}>
                <Col span={8} className="label-flex label-flex-secondary">
                  <Tooltip title="text-align">对齐</Tooltip>
                </Col>
                <Col span={16}>
                  <Select
                    style={{ width: "100%" }}
                    defaultValue="left"
                    onChange={onTextAlignChange}
                    options={[
                      {
                        label: "居左",
                        value: "left",
                      },
                      {
                        label: "居中",
                        value: "center",
                      },
                      {
                        label: "居右",
                        value: "right",
                      },
                      {
                        label: "适配",
                        value: "jsutify",
                      },
                    ]}
                  />
                </Col>
              </Row>

              <Row gutter={12}>
                <Col span={8} className="label-flex label-flex-secondary">
                  <Tooltip title="text-decoration">修饰</Tooltip>
                </Col>
                <Col span={16}>
                  <Select
                    style={{ width: "100%" }}
                    defaultValue="none"
                    onChange={onTextDecorationChange}
                    options={[
                      {
                        label: "无",
                        value: "none",
                      },
                      {
                        label: "下划线",
                        value: "underline",
                      },
                      {
                        label: "中划线",
                        value: "line-through",
                      },
                    ]}
                  />
                </Col>
              </Row>

              <Row gutter={12}>
                <Col span={8} className="label-flex label-flex-secondary">
                  <Tooltip title="text-vertical">垂直</Tooltip>
                </Col>
                <Col span={16}>
                  <NumberUnitSetter
                    value={fontSize || value.fontSize}
                    onChange={onWritingModeChange}
                    options={[
                      {
                        label: "px",
                        value: "px",
                      },
                      {
                        label: "%",
                        value: "%",
                      },
                      {
                        label: "em",
                        value: "em",
                      },
                    ]}
                  />
                </Col>
              </Row>

              <Row gutter={12}>
                <Col span={8} className="label-flex label-flex-secondary">
                  <Tooltip title="font-style">斜体</Tooltip>
                </Col>
                <Col span={16}>
                  <Select
                    style={{ width: "100%" }}
                    defaultValue="normal"
                    onChange={onFontStyleChange}
                    options={[
                      {
                        label: "正常",
                        value: "normal",
                      },
                      {
                        label: "斜体",
                        value: "italic",
                      },
                    ]}
                  />
                </Col>
              </Row>
            </Flex>
          </div>
        </Col>
      )}
    </Row>
  );
};

export { FontSetter };
export default FontSetter;
