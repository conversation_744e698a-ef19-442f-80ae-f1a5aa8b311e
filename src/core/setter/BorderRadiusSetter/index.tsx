import { useState } from "react";
import { Row, Col, Toolt<PERSON>, Select, Flex } from "antd";
import { NumberUnitSetter } from "../NumberUnitSetter";

const BorderRadiusSetter = (props) => {
  const { value, options = [], onChange } = props;
  const [selectedValue, setSelectedValue] = useState(
    value?.selectedValue || "none"
  );

  const [topRight, setTopRight] = useState({
    number: value?.["border-top-right-radius"]?.number || 0,
    unit: "px",
  });
  const [bottomRight, setBottomRight] = useState({
    number: value?.["border-bottom-right-radius"]?.number || 0,
    unit: "px",
  });
  const [bottomLeft, setBottomLeft] = useState({
    number: value?.["border-bottom-left-radius"]?.number || 0,
    unit: "px",
  });
  const [topLeft, setTopLeft] = useState({
    number: value?.["border-top-left-radius"]?.number || 0,
    unit: "px",
  });

  const onSelectChange = (selectedValue) => {
    setSelectedValue(selectedValue || "none");
    triggerChange({
      selectedValue: selectedValue,
    });
  };

  const triggerChange = (changedValue) => {
    let targetValue = {
      selectedValue,
      "border-top-right-radius": topRight,
      "border-bottom-right-radius": bottomRight,
      "border-bottom-left-radius": bottomLeft,
      "border-top-left-radius": topLeft,
      ...changedValue,
    };
    if (selectedValue === "none" || selectedValue === undefined) {
      targetValue = undefined;
    }

    onChange?.(targetValue);
  };

  const onTopRightChange = (changedValue) => {
    setTopRight(changedValue);
    triggerChange({
      "border-top-right-radius": changedValue,
    });
  };

  const onBottomRightChange = (changedValue) => {
    setBottomRight(changedValue);
    triggerChange({
      "border-bottom-right-radius": changedValue,
    });
  };

  const onBottomLeftChange = (changedValue) => {
    setBottomLeft(changedValue);
    triggerChange({
      "border-bottom-left-radius": changedValue,
    });
  };

  const onTopLeftChange = (changedValue) => {
    setTopLeft(changedValue);
    triggerChange({
      "border-top-left-radius": changedValue,
    });
  };

  return (
    <Row>
      <Select
        style={{ width: "100%" }}
        allowClear
        options={options}
        value={selectedValue}
        onChange={onSelectChange}
      />
      {selectedValue !== "none" && (
        <Col span={24}>
          <div className="secondary-setter">
            <Flex vertical gap={12}>
              <Row gutter={12}>
                <Col span={6} className="label-flex label-flex-secondary">
                  <Tooltip title="top-right">右上角</Tooltip>
                </Col>
                <Col span={18} className="label-flex">
                  <NumberUnitSetter
                    value={topRight}
                    onChange={onTopRightChange}
                    options={[
                      {
                        label: "px",
                        value: "px",
                      },
                    ]}
                  />
                </Col>
              </Row>

              <Row gutter={12}>
                <Col span={6} className="label-flex label-flex-secondary">
                  <Tooltip title="bottom-right">右下角</Tooltip>
                </Col>
                <Col span={18} className="label-flex">
                  <NumberUnitSetter
                    value={bottomRight}
                    onChange={onBottomRightChange}
                    options={[
                      {
                        label: "px",
                        value: "px",
                      },
                    ]}
                  />
                </Col>
              </Row>

              <Row gutter={12}>
                <Col span={6} className="label-flex label-flex-secondary">
                  <Tooltip title="left-bottom">左下角</Tooltip>
                </Col>
                <Col span={18} className="label-flex">
                  <NumberUnitSetter
                    value={bottomLeft}
                    onChange={onBottomLeftChange}
                    options={[
                      {
                        label: "px",
                        value: "px",
                      },
                    ]}
                  />
                </Col>
              </Row>

              <Row gutter={12}>
                <Col span={6} className="label-flex label-flex-secondary">
                  <Tooltip title="left-top">左上角</Tooltip>
                </Col>
                <Col span={18} className="label-flex">
                  <NumberUnitSetter
                    value={topLeft}
                    onChange={onTopLeftChange}
                    options={[
                      {
                        label: "px",
                        value: "px",
                      },
                    ]}
                  />
                </Col>
              </Row>
            </Flex>
          </div>
        </Col>
      )}
    </Row>
  );
};

export { BorderRadiusSetter };
export default BorderRadiusSetter;
