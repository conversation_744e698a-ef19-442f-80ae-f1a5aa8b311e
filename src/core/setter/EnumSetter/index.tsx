import { useState } from "react";
import { Select } from "antd";

const EnumSetter = (props) => {
  const [value, setValue] = useState(props.value || props.defaultValue);
  const { defaultValue } = props;
  const onChange = (val) => {
    setValue(val);
    const { onChange } = props;
    onChange && onChange(val);
  };
  return (
    <Select
      {...props}
      defaultValue={defaultValue}
      value={value}
      style={{ width: "100%" }}
      onChange={onChange}
    ></Select>
  );
};

export { EnumSetter };
export default EnumSetter;
