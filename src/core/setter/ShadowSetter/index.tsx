import { useState } from "react";
import { <PERSON>, <PERSON>, Toolt<PERSON>, Select, Flex, ColorPicker } from "antd";
import { NumberUnitSetter } from "../NumberUnitSetter";

const ShadowSetter = (props) => {
  const { value, options = [], onChange } = props;
  const [selectedValue, setSelectedValue] = useState("none");
  const [color, setColor] = useState("#1677FF");
  const [xaxis, setXaxis] = useState({ number: 0, unit: "px" });
  const [yaxis, setYaxis] = useState({ number: 0, unit: "px" });
  const [blur, setBlur] = useState({ number: 0, unit: "px" });
  const [spread, setSpread] = useState({ number: 0, unit: "px" });

  const onSelectChange = (selectedValue) => {
    if (selectedValue === undefined) {
      selectedValue = "none";
    }
    setSelectedValue(selectedValue);

    let number = 0;
    switch (selectedValue) {
      case "large":
        number = 24;
        break;
      case "middle":
        number = 16;
        break;
      case "small":
        number = 8;
        break;
      case "custom":
        number = 0;
        break;
      default:
        break;
    }

    let xaxis = { number, unit: "px" };
    let yaxis = { number, unit: "px" };
    let blur = { number, unit: "px" };
    let spread = { number, unit: "px" };

    setXaxis(xaxis);
    setYaxis(yaxis);
    setBlur(blur);
    setSpread(spread);

    triggerChange({
      color,
      xaxis,
      yaxis,
      blur,
      spread,
      selectedValue: selectedValue,
    });
  };

  const triggerChange = (changedValue) => {
    let triggerValue = {
      color,
      xaxis,
      yaxis,
      blur,
      spread,
      ...changedValue,
    };

    if (changedValue.selectedValue === "none") {
      triggerValue = undefined;
    }

    onChange?.(triggerValue);
  };

  const onColorChange = (changedValue) => {
    let value = changedValue.toHexString();
    setColor(value);
    triggerChange({
      color: value,
    });
  };

  const onXaxisChange = (changedValue) => {
    setXaxis(changedValue);
    triggerChange({
      xaxis: changedValue,
    });
  };

  const onYaxisChange = (changedValue) => {
    setYaxis(changedValue);
    triggerChange({
      yaxis: changedValue,
    });
  };

  const onBlurChange = (changedValue) => {
    setBlur(changedValue);
    triggerChange({
      blur: changedValue,
    });
  };

  const onSpreadChange = (changedValue) => {
    setSpread(changedValue);
    triggerChange({
      spread: changedValue,
    });
  };

  return (
    <Row>
      <Select
        style={{ width: "100%" }}
        allowClear
        options={options}
        value={selectedValue}
        onChange={onSelectChange}
      />
      {selectedValue === "custom" && (
        <Col span={24}>
          <div className="secondary-setter">
            <Flex vertical gap={12}>
              <Row gutter={12}>
                <Col span={6} className="label-flex label-flex-secondary">
                  <Tooltip title="Color">颜色</Tooltip>
                </Col>
                <Col span={18} className="label-flex">
                  <ColorPicker
                    value={color || value.color}
                    showText
                    allowClear
                    style={{ width: "100%", justifyContent: "start" }}
                    onChange={onColorChange}
                  />
                </Col>
              </Row>

              <Row gutter={12}>
                <Col span={6} className="label-flex label-flex-secondary">
                  <Tooltip title="X轴">X轴</Tooltip>
                </Col>
                <Col span={18} className="label-flex">
                  <NumberUnitSetter
                    value={xaxis || value.xaxis}
                    options={[
                      {
                        label: "px",
                        value: "px",
                      },
                      {
                        label: "%",
                        value: "%",
                      },
                      {
                        label: "em",
                        value: "em",
                      },
                    ]}
                    onChange={onXaxisChange}
                  />
                </Col>
              </Row>

              <Row gutter={12}>
                <Col span={6} className="label-flex label-flex-secondary">
                  <Tooltip title="Y轴">Y轴</Tooltip>
                </Col>
                <Col span={18} className="label-flex">
                  <NumberUnitSetter
                    value={yaxis || value.yaxis}
                    options={[
                      {
                        label: "px",
                        value: "px",
                      },
                      {
                        label: "%",
                        value: "%",
                      },
                      {
                        label: "em",
                        value: "em",
                      },
                    ]}
                    onChange={onYaxisChange}
                  />
                </Col>
              </Row>

              <Row gutter={12}>
                <Col span={6} className="label-flex label-flex-secondary">
                  <Tooltip title="Blur">模糊</Tooltip>
                </Col>
                <Col span={18} className="label-flex">
                  <NumberUnitSetter
                    value={blur || value.blur}
                    options={[
                      {
                        label: "px",
                        value: "px",
                      },
                      {
                        label: "%",
                        value: "%",
                      },
                      {
                        label: "em",
                        value: "em",
                      },
                    ]}
                    onChange={onBlurChange}
                  />
                </Col>
              </Row>

              <Row gutter={12}>
                <Col span={6} className="label-flex label-flex-secondary">
                  <Tooltip title="Spread">扩辐</Tooltip>
                </Col>
                <Col span={18} className="label-flex">
                  <NumberUnitSetter
                    value={spread || value.spread}
                    options={[
                      {
                        label: "px",
                        value: "px",
                      },
                      {
                        label: "%",
                        value: "%",
                      },
                      {
                        label: "em",
                        value: "em",
                      },
                    ]}
                    onChange={onSpreadChange}
                  />
                </Col>
              </Row>
            </Flex>
          </div>
        </Col>
      )}
    </Row>
  );
};

export { ShadowSetter };
export default ShadowSetter;
