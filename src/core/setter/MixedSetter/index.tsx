import { useState } from "react";
import { Select } from "antd";
import { SetterRender } from "../index";

const MixedSetter = (props) => {
  const { setters = [], value, onChange, ...restProps } = props;
  const [currentSetterIndex, setCurrentSetterIndex] = useState(0);

  // 处理 setter 配置，支持字符串和对象两种格式
  const normalizedSetters = setters.map((setter) => {
    if (typeof setter === "string") {
      return { componentName: setter, props: {} };
    }
    return setter;
  });

  const currentSetter = normalizedSetters[currentSetterIndex];

  const handleSetterChange = (index) => {
    setCurrentSetterIndex(index);
    // 切换 setter 时重置值
    onChange && onChange(undefined);
  };

  const handleValueChange = (newValue) => {
    onChange && onChange(newValue);
  };

  if (!normalizedSetters.length) {
    return null;
  }

  return (
    <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
      {normalizedSetters.length > 1 && (
        <Select
          size="small"
          value={currentSetterIndex}
          onChange={handleSetterChange}
          style={{ width: "100%" }}
        >
          {normalizedSetters.map((setter, index) => (
            <Select.Option key={index} value={index}>
              {setter.componentName}
            </Select.Option>
          ))}
        </Select>
      )}
      <SetterRender
        itemProps={{
          setter: currentSetter,
          name: "value",
        }}
        haveProps={{ value }}
        onChange={handleValueChange}
        {...restProps}
      />
    </div>
  );
};

export { MixedSetter };
export default MixedSetter;