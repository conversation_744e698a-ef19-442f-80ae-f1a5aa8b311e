import { useState } from "react";
import { Row, Col, Tooltip, Select, Flex, ColorPicker } from "antd";
import { NumberUnitSetter } from "../NumberUnitSetter";

const BorderSetter = (props) => {
  const { value, options = [], onChange } = props;
  const [selectedValue, setSelectedValue] = useState(
    value?.selectedValue || "none"
  );
  const [position, setPosition] = useState(value?.position || "");
  const [color, setColor] = useState(value?.color || "");
  const [line, setLine] = useState(value?.line || "none");
  const [width, setWidth] = useState(value?.width || { number: 0, unit: "px" });

  const onSelectChange = (selectedValue) => {
    setSelectedValue(selectedValue || "none");
    triggerChange({
      selectedValue: selectedValue,
    });
  };

  const triggerChange = (changedValue) => {
    let targetValue = {
      selectedValue,
      color,
      line,
      width,
      position,
      ...changedValue,
    };
    if (selectedValue === "none" || selectedValue === undefined) {
      targetValue = undefined;
    }

    onChange?.(targetValue);
  };

  const onPositionChange = (value) => {
    setPosition(value);
    triggerChange({
      position: value,
    });
  };

  const onColorChange = (changedValue) => {
    let value = changedValue.toHexString();
    setColor(value);
    triggerChange({
      color: value,
    });
  };

  const onLineChange = (changedValue) => {
    setLine(changedValue);
    triggerChange({
      line: changedValue,
    });
  };

  const onWidthChange = (changedValue) => {
    setWidth(changedValue);
    triggerChange({
      width: changedValue,
    });
  };

  return (
    <Row>
      <Select
        style={{ width: "100%" }}
        allowClear
        options={options}
        value={selectedValue}
        onChange={onSelectChange}
      />
      {selectedValue !== "none" && (
        <Col span={24}>
          <div className="secondary-setter">
            <Flex vertical gap={12}>
              <Row gutter={12}>
                <Col span={8} className="label-flex label-flex-secondary">
                  <Tooltip title="position">位置</Tooltip>
                </Col>
                <Col span={16} className="label-flex">
                  <Select
                    style={{ width: "100%" }}
                    value={position}
                    onChange={onPositionChange}
                    options={[
                      {
                        label: "全部",
                        value: "all",
                      },
                      {
                        label: "上边",
                        value: "top",
                      },
                      {
                        label: "右边",
                        value: "right",
                      },
                      {
                        label: "下边",
                        value: "bottom",
                      },
                      {
                        label: "左边",
                        value: "left",
                      },
                    ]}
                  />
                </Col>
              </Row>

              <Row gutter={12}>
                <Col span={8} className="label-flex label-flex-secondary">
                  <Tooltip title="Width">线宽</Tooltip>
                </Col>
                <Col span={16} className="label-flex">
                  <NumberUnitSetter
                    value={width || value.width}
                    options={[
                      {
                        label: "px",
                        value: "px",
                      },
                      {
                        label: "%",
                        value: "%",
                      },
                      {
                        label: "em",
                        value: "em",
                      },
                    ]}
                    onChange={onWidthChange}
                  ></NumberUnitSetter>
                </Col>
              </Row>

              <Row gutter={12}>
                <Col span={8} className="label-flex label-flex-secondary">
                  <Tooltip title="Line">线形</Tooltip>
                </Col>
                <Col span={16} className="label-flex">
                  <Select
                    style={{ width: "100%" }}
                    value={line}
                    onChange={onLineChange}
                    options={[
                      {
                        label: "无",
                        value: "none",
                      },
                      {
                        label: "点线",
                        value: "dot",
                      },
                      {
                        label: "虚线",
                        value: "dashed",
                      },
                      {
                        label: "实线",
                        value: "solid",
                      },
                    ]}
                  />
                </Col>
              </Row>

              <Row gutter={12}>
                <Col span={8} className="label-flex label-flex-secondary">
                  <Tooltip title="Color">颜色</Tooltip>
                </Col>
                <Col span={16} className="label-flex">
                  <ColorPicker
                    defaultValue="#1677ff"
                    value={color}
                    allowClear
                    showText
                    onChange={onColorChange}
                    style={{ width: "100%", justifyContent: "start" }}
                  />
                </Col>
              </Row>
            </Flex>
          </div>
        </Col>
      )}
    </Row>
  );
};

export { BorderSetter };
export default BorderSetter;
