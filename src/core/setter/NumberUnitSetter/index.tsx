import { useState } from "react";
import { InputNumber, Select } from "antd";

const NumberUnitSetter = (props) => {
  const { value, options = [], onChange, ...restPorps } = props;
  const [number, setNumber] = useState(value?.number);
  const [unit, setUnit] = useState(value?.unit || options[0].unit || "px"); // 单位值：真实-配置-默认

  const triggerChange = (changedValue) => {
    onChange?.({
      number,
      unit,
      ...changedValue,
    });
  };

  const onNumberChange = (num) => {
    // num == null 是一个简写，它会检查 num 是否为 null 或者 undefined
    num = num == null ? undefined : num && num;
    setNumber(num);
    triggerChange({
      number: num,
    });
  };

  const onUnitChange = (unit) => {
    let initUnit = unit || options[0].value;
    setNumber(undefined);
    setUnit(initUnit);
    triggerChange({
      number: undefined,
      unit: initUnit,
    });
  };

  return (
    <InputNumber
      value={number}
      onChange={onNumberChange}
      {...restPorps}
      addonAfter={
        <Select
          allowClear
          value={unit}
          onChange={onUnitChange}
          options={options}
        ></Select>
      }
      style={{
        width: "100%",
      }}
    ></InputNumber>
  );
};

export { NumberUnitSetter };
export default NumberUnitSetter;
