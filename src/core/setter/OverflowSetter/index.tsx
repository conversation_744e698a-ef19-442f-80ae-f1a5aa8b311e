import { useState } from "react";
import { <PERSON>, <PERSON>, <PERSON>lt<PERSON>, Select, Flex } from "antd";

const OverflowSetter = (props) => {
  const { value, options = [], onChange } = props;
  const [selectedValue, setSelectedValue] = useState(
    value?.selectedValue || "none"
  );

  const [xAxis, setXAxis] = useState({
    "overflow-x": value?.["overflow-x"] || "visible",
  });

  const [yAxis, setYAxis] = useState({
    "overflow-y": value?.["overflow-y"] || "visible",
  });

  const onSelectChange = (selectedValue) => {
    setSelectedValue(selectedValue || "none");
    triggerChange({
      selectedValue: selectedValue,
    });
  };

  const onXAxisChange = (value) => {
    setXAxis({
      "overflow-x": value || "visible",
    });
    triggerChange({
      "overflow-x": value,
    });
  };

  const onYAxisChange = (value) => {
    setYAxis({
      "overflow-y": value || "visible",
    });
    triggerChange({
      "overflow-y": value,
    });
  };

  const triggerChange = (changedValue) => {
    let targetValue = {
      selectedValue,
      ...xAxis,
      ...yAxis,
      ...changedValue,
    };
    if (selectedValue === "none" || selectedValue === undefined) {
      targetValue = undefined;
    }

    onChange?.(targetValue);
  };

  return (
    <Row>
      <Select
        style={{ width: "100%" }}
        allowClear
        options={options}
        value={selectedValue}
        onChange={onSelectChange}
      />
      {selectedValue !== "none" && (
        <Col span={24}>
          <div className="secondary-setter">
            <Flex vertical gap={12}>
              <Row gutter={12}>
                <Col span={6} className="label-flex label-flex-secondary">
                  <Tooltip title="x-axis">X轴</Tooltip>
                </Col>
                <Col span={18} className="label-flex">
                  <Select
                    allowClear
                    value={xAxis["overflow-x"]}
                    onChange={onXAxisChange}
                    options={[
                      {
                        label: "默认",
                        value: "visible",
                      },
                      {
                        label: "隐藏",
                        value: "hidden",
                      },
                      {
                        label: "滚动",
                        value: "scroll",
                      },
                      {
                        label: "自动",
                        value: "auto",
                      },
                    ]}
                  />
                </Col>
              </Row>

              <Row gutter={12}>
                <Col span={6} className="label-flex label-flex-secondary">
                  <Tooltip title="y-axis">Y轴</Tooltip>
                </Col>
                <Col span={18} className="label-flex">
                  <Select
                    value={yAxis["overflow-y"]}
                    allowClear
                    onChange={onYAxisChange}
                    options={[
                      {
                        label: "默认",
                        value: "visible",
                      },
                      {
                        label: "隐藏",
                        value: "hidden",
                      },
                      {
                        label: "滚动",
                        value: "scroll",
                      },
                      {
                        label: "自动",
                        value: "auto",
                      },
                    ]}
                  />
                </Col>
              </Row>
            </Flex>
          </div>
        </Col>
      )}
    </Row>
  );
};

export { OverflowSetter };
export default OverflowSetter;
