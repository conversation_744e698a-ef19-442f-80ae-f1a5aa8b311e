import { memo, useEffect, useState } from "react";
// Basic Setters
import BooleanSetter from "./BooleanSetter";
import EnumSetter from "./EnumSetter";
import SelectSetter from "./SelectSetter";
import StringSetter from "./StringSetter";
import NumberSetter from "./NumberSetter";

// Style Setters
import BackgroundSetter from "./BackgroundSetter";
import BorderSetter from "./BorderSetter";
import OverflowSetter from "./OverflowSetter";
import FontSetter from "./FontSetter";
import StyleSetter from "./StyleSetter";
import ShadowSetter from "./ShadowSetter";
import MarginPaddingSetter from "./MarginPaddingSetter";

// Advanced Setters
import SegmentedSetter from "./SegmentedSetter";
import NumberUnitSetter from "./NumberUnitSetter";

// Complex Setters
import MultiSetter from "./MultiSetter";
import MixedSetter from "./MixedSetter";
import RelationSetter from "./RelationSetter";
import ExclusionSetter from "./ExclusionSetter";
import ArraySetter from "./ArraySetter";
import ArraySchemaSetter from "./ArraySchemaSetter";
import ObjectSetter from "./ObjectSetter";
import FunctionSetter from "./FunctionSetter";
import ItemSetter from "./ItemSetter";
import ReactNodeSetter from "./ReactNodeSetter";

const componentMap = {
  // Basic Setters
  BooleanSetter,
  EnumSetter,
  SelectSetter,
  StringSetter,
  NumberSetter,

  // Style Setters
  BackgroundSetter,
  BorderSetter,
  OverflowSetter,
  FontSetter,
  StyleSetter,
  ShadowSetter,
  MarginPaddingSetter,

  // Advanced Setters
  SegmentedSetter,
  NumberUnitSetter,

  // Complex Setters
  MultiSetter,
  MixedSetter,
  RelationSetter,
  ExclusionSetter,
  ArraySetter,
  ArraySchemaSetter,
  ItemSetter,
  FunctionSetter,
  ObjectSetter,
  ReactNodeSetter,
};

/* 渲染 指定组件 */
const SetterRender = memo(({ itemProps, haveProps, children, onChange }) => {
  const [value, setValue] = useState(null);
  let ComponentTag = null;
  let {
    setter: { componentName, props },
    name,
  } = itemProps;

  let defaultValue = haveProps && haveProps[name];
  if (componentName && componentName.indexOf(".") !== -1) {
    const tags = componentName.split(".");
    ComponentTag = componentMap[tags[0]][tags[1]];
  } else {
    ComponentTag = componentMap[componentName];
    if (!ComponentTag) {
      console.error(`${componentName}: 组件类型未设定，请检查！`); // 不中断程序
    }
  }

  if (!ComponentTag) return false;

  const middleChange = (newValue) => {
    onChange && onChange(newValue);
    setValue(newValue);
  };

  useEffect(() => {
    setValue(defaultValue);
  }, [defaultValue]);

  return (
    <ComponentTag
      {...props}
      defaultValue={defaultValue}
      value={value}
      onChange={middleChange}
    >
      {children}
    </ComponentTag>
  );
});

export { SetterRender };

export default SetterRender;
