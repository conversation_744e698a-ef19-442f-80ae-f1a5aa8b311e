import { useState } from "react";
import { Row, Col, Tooltip, Select, Flex } from "antd";
import { NumberUnitSetter } from "../NumberUnitSetter";

const MarginPaddingSetter = (props) => {
  const { value, options = [], title, onChange } = props;
  const [selectedValue, setSelectedValue] = useState(
    value?.selectedValue || "none"
  );
  const [top, setTop] = useState({
    number: value?.top?.number || 0,
    unit: "px",
  });
  const [right, setRight] = useState({
    number: value?.right?.number || 0,
    unit: "px",
  });
  const [bottom, setBottom] = useState({
    number: value?.bottom?.number || 0,
    unit: "px",
  });
  const [left, setLeft] = useState({
    number: value?.left?.number || 0,
    unit: "px",
  });

  const onSelectChange = (selectedValue) => {
    if (selectedValue === undefined) {
      selectedValue = "none"; // 点清除时：恢复成 none 无
    }
    setSelectedValue(selectedValue);
    let number = 0;
    switch (selectedValue) {
      case "large":
        number = 24;
        break;
      case "middle":
        number = 16;
        break;
      case "small":
        number = 8;
        break;
      case "custom":
        number = 0;
        break;
      default:
        break;
    }

    let top = { number, unit: "px" };
    let right = { number, unit: "px" };
    let bottom = { number, unit: "px" };
    let left = { number, unit: "px" };

    setTop(top);
    setRight(right);
    setBottom(bottom);
    setLeft(left);

    triggerChange({ top, right, bottom, left, selectedValue });
  };

  const triggerChange = (changedValue) => {
    let triggerValue = {
      top,
      right,
      bottom,
      left,
      selectedValue,
      ...changedValue,
    };

    if (changedValue.selectedValue === "none") {
      triggerValue = undefined;
    }
    onChange?.(triggerValue);
  };

  const onTopChange = (changedValue) => {
    setTop(changedValue);
    triggerChange({
      top: changedValue,
    });
  };

  const onRightChange = (changedValue) => {
    setRight(changedValue);
    triggerChange({
      right: changedValue,
    });
  };

  const onBottomChange = (changedValue) => {
    setBottom(changedValue);
    triggerChange({
      bottom: changedValue,
    });
  };

  const onLeftChange = (changedValue) => {
    setLeft(changedValue);
    triggerChange({
      left: changedValue,
    });
  };

  return (
    <Row>
      <Select
        allowClear
        style={{ width: "100%" }}
        options={options}
        value={selectedValue}
        onChange={onSelectChange}
      />
      {selectedValue === "custom" && (
        <Col span={24}>
          <div className="secondary-setter">
            <Flex vertical gap={12}>
              <Row gutter={12}>
                <Col span={6} className="label-flex label-flex-secondary">
                  <Tooltip
                    title={title == "Margin" ? "margin-top" : "padding-top"}
                  >
                    上边距
                  </Tooltip>
                </Col>
                <Col span={18} className="label-flex">
                  <NumberUnitSetter
                    value={top || value.top}
                    onChange={onTopChange}
                    options={[
                      {
                        label: "px",
                        value: "px",
                      },
                      {
                        label: "%",
                        value: "%",
                      },
                      {
                        label: "em",
                        value: "em",
                      },
                      {
                        label: "rem",
                        value: "rem",
                      },
                      {
                        label: "vw",
                        value: "vw",
                      },
                      {
                        label: "vh",
                        value: "vh",
                      },
                    ]}
                  />
                </Col>
              </Row>

              <Row gutter={12}>
                <Col span={6} className="label-flex label-flex-secondary">
                  <Tooltip
                    title={title == "Margin" ? "margin-right" : "padding-right"}
                  >
                    右边距
                  </Tooltip>
                </Col>
                <Col span={18} className="label-flex">
                  <NumberUnitSetter
                    value={right || value.right}
                    onChange={onRightChange}
                    options={[
                      {
                        label: "px",
                        value: "px",
                      },
                      {
                        label: "%",
                        value: "%",
                      },
                      {
                        label: "em",
                        value: "em",
                      },
                      {
                        label: "rem",
                        value: "rem",
                      },
                      {
                        label: "vw",
                        value: "vw",
                      },
                      {
                        label: "vh",
                        value: "vh",
                      },
                    ]}
                  />
                </Col>
              </Row>

              <Row gutter={12}>
                <Col span={6} className="label-flex label-flex-secondary">
                  <Tooltip
                    title={
                      title == "Margin" ? "margin-bottom" : "padding-bottom"
                    }
                  >
                    下边距
                  </Tooltip>
                </Col>
                <Col span={18} className="label-flex">
                  <NumberUnitSetter
                    value={bottom || value.bottom}
                    onChange={onBottomChange}
                    options={[
                      {
                        label: "px",
                        value: "px",
                      },
                      {
                        label: "%",
                        value: "%",
                      },
                      {
                        label: "em",
                        value: "em",
                      },
                      {
                        label: "rem",
                        value: "rem",
                      },
                      {
                        label: "vw",
                        value: "vw",
                      },
                      {
                        label: "vh",
                        value: "vh",
                      },
                    ]}
                  />
                </Col>
              </Row>

              <Row gutter={12}>
                <Col span={6} className="label-flex label-flex-secondary">
                  <Tooltip
                    title={title == "Margin" ? "margin-left" : "padding-left"}
                  >
                    左边距
                  </Tooltip>
                </Col>
                <Col span={18} className="label-flex">
                  <NumberUnitSetter
                    value={left || value.left}
                    onChange={onLeftChange}
                    options={[
                      {
                        label: "px",
                        value: "px",
                      },
                      {
                        label: "%",
                        value: "%",
                      },
                      {
                        label: "em",
                        value: "em",
                      },
                      {
                        label: "rem",
                        value: "rem",
                      },
                      {
                        label: "vw",
                        value: "vw",
                      },
                      {
                        label: "vh",
                        value: "vh",
                      },
                    ]}
                  />
                </Col>
              </Row>
            </Flex>
          </div>
        </Col>
      )}
    </Row>
  );
};

export { MarginPaddingSetter };
export default MarginPaddingSetter;
