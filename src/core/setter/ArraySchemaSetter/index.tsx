import { useState } from "react";
import {
  Flex,
  Button,
  Input,
  Popover,
  Form,
  Switch,
  ConfigProvider,
} from "antd";
import {
  HolderOutlined,
  DeleteOutlined,
  EditOutlined,
} from "@ant-design/icons";
import { genAlphabetMaxId } from "@/utils/index";
import { message } from "@/store/hooks";

import useStore from "@/store";

// ArraySchemaSetter Schema 数组配置器
const ArraySchemaSetter = (props) => {
  const [form] = Form.useForm();

  const { selectedSomeSchema } = useStore();
  console.log({ selectedSomeSchema });

  const [items, setItems] = useState([
    {
      id: genAlphabetMaxId(),
      label: "Tab",
      children: "",
    },
  ]);
  const onChange = (e) => {
    const { value } = e.target;
    const { onChange } = props;
    onChange && onChange(value);
  };

  const addItem = () => {
    const newItem = {
      id: genAlphabetMaxId(),
      label: "Tab",
      children: "",
    };
    setItems([...items, newItem]);
  };

  const deleteItem = (item) => {
    if (items.length === 1) {
      message.warning("至少保留一个");
      return;
    }
    setItems(items.filter((i) => i.id !== item.id));
  };

  const editItem = (item) => {
    console.log({ item });
  };

  const onFormItemChange = (changedValues, allValues) => {
    console.log({ changedValues, allValues });
  };

  const content = (
    <ConfigProvider
      theme={{
        components: {
          Form: {
            itemMarginBottom: 12,
          },
        },
      }}
    >
      <Form
        layout="horizontal"
        labelCol={{
          span: 8,
        }}
        wrapperCol={{
          span: 16,
        }}
        form={form}
        initialValues={{
          layout: "",
        }}
        onValuesChange={onFormItemChange}
      >
        <Form.Item name="label" label="标签页名">
          <Input placeholder="请输入标签页名" />
        </Form.Item>
        <Form.Item name="key" label="标签KEY">
          <Input placeholder="请输入标签KEY" />
        </Form.Item>
        <Form.Item name="closable" label="允许关闭">
          <Switch />
        </Form.Item>
        <Form.Item name="disabled" label="是否禁用">
          <Switch />
        </Form.Item>
      </Form>
    </ConfigProvider>
  );

  return (
    <Flex vertical gap={12}>
      {items.map((item) => {
        return (
          <Input
            prefix={
              <Button
                icon={<HolderOutlined />}
                type="text"
                size="small"
              ></Button>
            }
            {...props}
            onChange={onChange}
            style={{ width: "100%" }}
            suffix={
              <Popover content={content} trigger="click">
                <EditOutlined onClick={() => editItem(item)} />
              </Popover>
            }
            addonAfter={
              <DeleteOutlined
                style={{ color: "#ff4d4f", cursor: "pointer" }}
                onClick={() => deleteItem(item)}
              />
            }
          ></Input>
        );
      })}

      <Button block type="primary" onClick={addItem}>
        添加一项
      </Button>
    </Flex>
  );
};

export { ArraySchemaSetter };
export default ArraySchemaSetter;
