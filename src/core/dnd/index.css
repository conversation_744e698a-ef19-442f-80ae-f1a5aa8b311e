.drag-drop-span {
  position: relative;
}

.outline {
  outline: 1px solid #ffa500;
  z-index: 9;
}

.selected {
  outline: 2px solid #ffa500;
  cursor: move;
  z-index: 9;
}

/* 当元素同时具有 .drag-drop 和 .outline 类时，应用动画 */
.drag-drop.outline {
  animation: outlinePulse 0.6s infinite ease-in-out;
}

/* 定义呼吸效果的关键帧 */
@keyframes outlinePulse {
  0%,
  100% {
    outline-width: 1px;
  }
  50% {
    outline-width: 3px;
  }
}

.overlay {
  min-width: 76px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: nowrap;
  background-color: antiquewhite;
  border-radius: 4px;
  position: relative;
  z-index: 99;
  text-align: center;
  font-size: 14px;
}

.draggable-item {
  position: relative;
}

.indicator {
  height: 24px;
  width: 24px;
  border-radius: 50%;
  background-color: #ffa500; /* 主色调：橙色 */
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 100;
  animation: pulse 0.6s infinite ease-in-out; /* 动画设置 */
}

/* 波纹效果 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 111, 97, 0.4); /* 初始状态：小且透明度低 */
  }
  70% {
    box-shadow: 0 0 0 16px rgba(255, 111, 97, 0); /* 扩散状态：大且透明度逐渐消失 */
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 111, 97, 0); /* 结束状态：回到初始大小和透明度 */
  }
}

.before,
.inside,
.after {
  transition: all 0.1s ease-in-out; /* 过渡效果:去掉更直观 */
}

.before {
  left: -12px;
}

.inside {
  left: 50%;
  transform: translateX(-50%);
}

.after {
  right: -12px;
}

.handler {
  opacity: 0;
  cursor: move;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.handlerLeft {
  opacity: 1;
}

.light {
  display: flex;
  flex-wrap: nowrap;
  opacity: 1;
  z-index: 101;
  border-radius: 4px;
  background: #ffa500;
}


.container-min-width{
  min-width: 360px;
  min-height: 80px;
}