import { useDroppable } from "@dnd-kit/core";

const Droppable = (props) => {
  const { id, index } = props;
  const { setNodeRef, isOver } = useDroppable({
    id: id, // [必须] 每个包裹组件的唯一id
    disabled: false, // 禁用放置区域
    data: {
      accepts: [], // 当前只能接收哪些  drag 元素的放置，对应 drag data:{type:"type1"} ["type1", "type2"]
      index: index,
      isContainer: true, // 注入自定义数据
      nestingRule: {
        childWhitelist: ["*"], // 卡片可以接受任意子元素
        parentWhitelist: ["*"],
      },
    },
  });

  return (
    <div 
      id={id} 
      data-dnd={id} 
      ref={setNodeRef}
    >
      {props.children}
    </div>
  );
};

export { Droppable };

export default Droppable;
