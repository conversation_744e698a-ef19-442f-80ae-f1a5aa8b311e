import { useDraggable } from "@dnd-kit/core";
import { Button } from "antd";

import { getComponentNestingRule } from "@/utils/schema";

const Draggable = (props) => {
  const {
    item: { uuid: id, title, componentName },
  } = props;

  const nestingRule = getComponentNestingRule(componentName);

  const { attributes, listeners, setNodeRef } = useDraggable({
    id: id,
    data: {
      type: "", // 传递数据
      componentName: componentName,
      from: "material",
      nestingRule: nestingRule,
    },
  });

  return (
    <div className="draggable-item">
      <Button
        {...listeners}
        {...attributes}
        ref={setNodeRef}
        aria-label="Draggable"
        data-cypress="draggable-item"
        className="handler handlerLeft"
      >
        {title}
      </Button>
    </div>
  );
};

export { Draggable };
