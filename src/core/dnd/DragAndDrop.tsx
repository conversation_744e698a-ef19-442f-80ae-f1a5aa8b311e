import { useEffect } from "react";
import { useDraggable, useDroppable } from "@dnd-kit/core";
import classNames from "classnames";
import { DragOutlined } from "@ant-design/icons";
import { SelectedBar } from "@/component/SelectedBar";
import useStore from "@/store";

import { getComponentConfigure } from "@/utils/schema";
import { useEvents, getDynamicEvents } from "@/utils/event";
import { subscribe } from "@/utils/drive";

const DragAndDrop = (props) => {
  const { item, children, componentTag } = props;
  const {
    componentName,
    uuid: id,
    name: uniqueClassName,
    props: { className, ...componentProps },
    events,
  } = item;
  const { selectedId: activeId, position } = useStore();

  const configure = getComponentConfigure(componentName);
  const isContainer = configure?.component?.isContainer;

  const {
    attributes,
    listeners,
    setNodeRef: setDraggableNodeRef,
    active,
    over,
    isDragging,
  } = useDraggable({
    id: id,
    data: {
      type: "", // 传递数据 type1
      componentName: componentName,
      from: "stage",
      nestingRule: configure?.component?.nestingRule,
    },
  });

  const {
    setNodeRef: setDroppableNodeRef,
    isOver,
    over: dropOver,
  } = useDroppable({
    id: id,
    // disabled: false, // 禁用放置区域-元素失去碰撞检测
    data: {
      componentName: componentName,
      accepts: [], // 当前只能接收哪些 drag 元素的放置，对应 drag data:{type:"type1"}
      index: props.index,
      isContainer: isContainer, // 注入自定义数据
      nestingRule: configure?.component?.nestingRule,
    },
  });

  useEffect(() => {
    // 在组件挂载时订阅事件
    const subscription = subscribe((data) => {
      const { action } = data;
      if (action.target === id) {
        console.log(`组件${id}接收到:`, action.msg);
      }
    });

    // 返回一个清理函数，在组件卸载时执行
    return () => {
      subscription.unsubscribe(); // 取消订阅
    };
  }, []); // 空数组表示只在组件挂载和卸载时执行

  let overIsContainer = dropOver?.data?.current?.isContainer; // 判断 over 元素是否容器

  let indicator = isOver && active?.id !== over?.id;
  let inside = isOver && overIsContainer && position === "inside";
  let before = position === "before";
  let after = position === "after";

  const Component = componentTag;

  // 事件处理
  // 模拟一些上下文数据
  const contextData = {
    name: "John Doe",
    id: "123456",
    data: item,
  };

  // 使用useEvents Hook，并传入所有事件配置
  const eventActions = useEvents(events || {});

  // 获取动态事件处理器
  const dynamicEvents = getDynamicEvents(eventActions, contextData);

  // 处理 props 上的 name
  delete componentProps[uniqueClassName];

  return (
    <SelectedBar item={item} open={activeId === id && !isDragging}>
      {/* 包裹 div 原因是：单标签组件时，  button 操作句柄等直接渲染到 Component标签内部，会报错 */}
      <span className="drag-drop-span" data-dnd={id}>
        <Component
          id={id}
          className={classNames(
            className,
            "drag-drop",
            uniqueClassName,
            indicator && inside && "outline",
            activeId === id && !isDragging && "selected"
          )}
          {...dynamicEvents}
          {...componentProps}
        >
          {children}
        </Component>
        <button
          {...attributes}
          {...listeners}
          ref={(node) => {
            setDroppableNodeRef(node);
            setDraggableNodeRef(node);
          }}
          className={classNames("handler", activeId === id && "light")}
        >
          <DragOutlined />
          {componentName}
        </button>
        {indicator && (
          <div
            className={classNames(
              "indicator",
              before && "before",
              inside && "inside",
              after && "after"
            )}
          ></div>
        )}
      </span>
    </SelectedBar>
  );
};

export { DragAndDrop };
