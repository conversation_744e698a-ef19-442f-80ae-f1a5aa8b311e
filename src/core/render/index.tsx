import { useEffect, useMemo, useCallback, memo } from "react";
import classNames from "classnames";
import { componentTag } from "@/utils/component";
import { getComponentConfigure } from "@/utils/schema";
import { DragAndDrop } from "@/core/dnd";
import EmptyHint from "@/component/EmptyHint";
import { useEvents, getDynamicEvents } from "@/utils/event";
import { subscribe } from "@/utils/drive";
import {
  convertToCSS,
  createOrUpdateStyleClass,
  updateProps,
} from "@/utils/index";
import useStore from "@/store";

// 使用 memo 优化 RuntimeRender 组件
const RuntimeRender = memo((props) => {
  const {
    ComponentTag,
    itemProps,
    uniqueClassName,
    item,
    children,
    restProps,
  } = props;

  const { uuid: id, events } = item;
  const { jsonSchema, updateJsonSchema } = useStore();

  // 使用 useCallback 优化事件处理函数
  const handleSubscriptionData = useCallback((data) => {
    const { args } = data?.action;
    if (args?.target === id) {
      console.log(`组件${id}接收到:`, args?.msg);
      console.log({ item });
      const newSchema = updateProps(jsonSchema, id, "title", args?.msg || "Bong!");
      console.log({ newSchema });
      updateJsonSchema(newSchema);
    }
  }, [id, item, jsonSchema, updateJsonSchema]);

  useEffect(() => {
    // 在组件挂载时订阅事件
    const subscription = subscribe(handleSubscriptionData);

    // 返回一个清理函数，在组件卸载时执行
    return () => {
      subscription.unsubscribe(); // 取消订阅
    };
  }, [handleSubscriptionData]); // 添加完整的依赖数组

  // 使用 useMemo 缓存处理后的 itemProps
  const processedItemProps = useMemo(() => {
    const props = { ...itemProps };
    delete props[uniqueClassName];
    return props;
  }, [itemProps, uniqueClassName]);

  // 使用 useMemo 缓存上下文数据
  const contextData = useMemo(() => ({
    name: "John Doe",
    id: "123456",
    data: item,
  }), [item]);

  // 使用 useMemo 缓存事件处理器
  const eventActions = useEvents(events || {});
  const dynamicEvents = useMemo(() => 
    getDynamicEvents(eventActions, contextData),
    [eventActions, contextData]
  );

  // 使用 useMemo 缓存 className
  const combinedClassName = useMemo(() => 
    classNames(processedItemProps?.className, uniqueClassName),
    [processedItemProps?.className, uniqueClassName]
  );

  return (
    <ComponentTag
      {...processedItemProps}
      {...restProps}
      {...dynamicEvents}
      id={id}
      className={combinedClassName}
    >
      {children}
    </ComponentTag>
  );
});

RuntimeRender.displayName = 'RuntimeRender';

// 使用 memo 优化 Render 组件
const Render = memo((props) => {
  const { item, children, tense, ...restProps } = props;
  
  const {
    componentName,
    children: childrenElement,
    name: uniqueClassName,
    props: itemProps = {},
  } = item;

  // 使用 useMemo 缓存组件标签和配置
  const ComponentTag = useMemo(() => componentTag(componentName), [componentName]);
  const configure = useMemo(() => getComponentConfigure(componentName), [componentName]);
  const isContainer = configure?.component?.isContainer;

  // 使用 useMemo 缓存 CSS 规则，避免每次渲染都重新计算
  const cssRules = useMemo(() => {
    const styleProps = itemProps[uniqueClassName];
    return styleProps ? convertToCSS(styleProps, uniqueClassName) : '';
  }, [itemProps, uniqueClassName]);

  // 使用 useEffect 来处理样式注入，只在必要时更新
  useEffect(() => {
    if (cssRules) {
      createOrUpdateStyleClass(uniqueClassName, cssRules);
    }
  }, [uniqueClassName, cssRules]);

  // 使用 useMemo 缓存是否显示空提示
  const shouldShowEmptyHint = useMemo(() => 
    isContainer && (!childrenElement || childrenElement.length === 0),
    [isContainer, childrenElement]
  );

  if (tense === "design") {
    return (
      <DragAndDrop item={item} componentTag={ComponentTag}>
        {shouldShowEmptyHint ? <EmptyHint /> : children}
      </DragAndDrop>
    );
  }

  return (
    <RuntimeRender
      ComponentTag={ComponentTag}
      itemProps={itemProps}
      uniqueClassName={uniqueClassName}
      item={item}
      restProps={restProps}
    >
      {children}
    </RuntimeRender>
  );
});

Render.displayName = 'Render';

// 使用 memo 优化 SchemaRender 组件
const SchemaRender = memo((props) => {
  const { schema = [], tense = "design" } = props;
  
  // 使用 useMemo 缓存过滤后的 schema
  const filteredSchema = useMemo(() => 
    schema.filter((item) => item.show !== false),
    [schema]
  );

  // 使用 useMemo 缓存渲染结果
  const renderedItems = useMemo(() => {
    return filteredSchema.map((item) => {
      const { children, uuid: id } = item;
      
      if (children && Array.isArray(children)) {
        return (
          <Render
            key={id}
            item={item}
            tense={tense}
          >
            <SchemaRender schema={children} tense={tense} />
          </Render>
        );
      }
      
      return (
        <Render key={id} item={item} tense={tense}>
          {children}
        </Render>
      );
    });
  }, [filteredSchema, tense]);

  return <>{renderedItems}</>;
});

SchemaRender.displayName = 'SchemaRender';

export { Render, SchemaRender };
export default SchemaRender;
