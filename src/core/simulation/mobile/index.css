.iphone {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-style: solid;
  background-color: #fafafa;
  border-color: #ecdcc8; /* 粉色 #E9C9C5 */
  border-radius: 3em;
  border-width: 0.4em;
  position: relative;
  width: 23em;
  height: 48em;
}

.iphone-ear {
  position: absolute;
  background: #292728;
  top: 2.5em;
  left: 50%;
  margin-left: -2em;
  width: 4em;
  height: 0.5em;
  border-radius: 0.3em;
}

.iphone-home {
  position: absolute;
  background-color: #fafafa;
  bottom: 0.9em;
  left: 50%;
  margin-left: -1.75em;
  width: 3.5em;
  height: 3.5em;
  border-radius: 1.75em;
  border: 0.2em solid #ecdcc8;
}

.iphone-screen {
  position: absolute;
  background: black;
  top: 5em;
  left: 50%;
  margin-left: -10.5em;
  width: 21em;
  height: 37em;
  border: solid 0.2em black;
  border-radius: 0.1em;
  overflow-y: auto;
}
