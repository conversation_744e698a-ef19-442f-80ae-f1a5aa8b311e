// 检测平台
const isMac = /macintosh|mac os x/i.test(navigator.userAgent);
const isWindows = /win32|win64|windows|wince/i.test(navigator.userAgent);

interface Shortcut {
  key: string;
  mac?: string;
  windows?: string;
  description?: string;
  callback: (event: KeyboardEvent) => void;
}

class ShortcutManager {
  private shortcuts: { [name: string]: Shortcut };

  constructor(target: HTMLElement | Document = document) {
    this.shortcuts = {};
    this.init(target);
  }

  init(target: HTMLElement | Document) {
    target.addEventListener("keydown", (event) => this.handleKeydown(event));
  }

  // 注册快捷键
  registerShortcut(
    name: string,
    keys: { [key: string]: string | string[] },
    callback: (event: KeyboardEvent) => void
  ) {
    const normalizedKeys = this.normalizeKeys(keys);
    this.shortcuts[name] = {
      ...normalizedKeys,
      callback: (event: KeyboardEvent) => {
        // 为了防止多次回调， event.isPropagationStopped() === true
        if (event.isPropagationStopped) return;
        event.stopPropagation();
        callback(event);
      },
    };
  }

  // 移除快捷键
  unregisterShortcut(name: string) {
    delete this.shortcuts[name];
  }

  // 标准化快捷键配置
  normalizeKeys(keys: { [key: string]: string | string[] }) {
    return Object.keys(keys).reduce((acc, key) => {
      acc[key] = Array.isArray(keys[key]) ? keys[key].join("+") : keys[key];
      return acc;
    }, {} as { [key: string]: string });
  }

  // 获取所有已注册的快捷键
  getAllShortcuts() {
    return Object.keys(this.shortcuts).map((name) => {
      const shortcut = this.shortcuts[name];
      const combination = this.getShortcutCombination(name);
      return {
        name,
        description: shortcut.description || "",
        combination, // 根据当前系统返回正确的组合键
        callback: shortcut.callback, // 可选，如果你想要查看回调函数
      };
    });
  }

  // 获取当前系统的快捷键组合
  getShortcutCombination(name: string) {
    const shortcut = this.shortcuts[name];
    if (!shortcut) return "";
    return isMac ? shortcut.mac : shortcut.windows;
  }

  // 处理按键事件
  handleKeydown = (event: KeyboardEvent) => {
    Object.values(this.shortcuts).forEach((shortcut) => {
      if (this.isMatch(event, shortcut)) {
        event.preventDefault();
        shortcut.callback(event);
      }
    });
  };

  // 判断是否匹配快捷键
  isMatch(event: KeyboardEvent, shortcut: Shortcut) {
    const { key, mac, windows } = shortcut;
    const systemCombination = isMac ? mac : windows;

    if (!systemCombination) return false;

    const keys = systemCombination.toLowerCase().split("+");
    const needsCtrl = keys.includes("ctrl");
    const needsShift = keys.includes("shift");
    const needsAlt = keys.includes("alt");
    const needsMeta = keys.includes("⌘") || keys.includes("meta");

    const isCmdOrCtrl = isMac ? event.metaKey : event.ctrlKey;
    const isKeyMatch = event.key.toLowerCase() === key.toLowerCase();

    return (
      isKeyMatch &&
      isCmdOrCtrl === needsMeta &&
      event.shiftKey === needsShift &&
      event.altKey === needsAlt &&
      event.ctrlKey === needsCtrl
    );
  }
}

export { ShortcutManager };

// 使用示例：
// const shortcutManager = new ShortcutManager(document.getElementById('myElement'));

// 注册 Ctrl+Shift+A (Windows) 或 Cmd+Shift+A (Mac)
// shortcutManager.registerShortcut(
//   "customAction",
//   {
//     key: "a",
//     mac: ["⌘", "shift"],
//     windows: ["ctrl", "shift"],
//   },
//   () => {
//     console.log("Ctrl/Cmd + Shift + A was pressed!");
//   }
// );

// 注册简单的单键快捷键
// shortcutManager.registerShortcut(
//   "escapeAction",
//   {
//     key: "Escape",
//   },
//   () => {
//     console.log("Escape was pressed!");
//   }
// );

// 注册 Alt + S
// shortcutManager.registerShortcut(
//   "altSAction",
//   {
//     key: "s",
//     mac: ["alt"],
//     windows: ["alt"],
//   },
//   () => {
//     console.log("Alt + S was pressed!");
//   }
// );

// 在 React 组件中使用
// useEffect(() => {
//   const shortcutManager = new ShortcutManager(document.getElementById('myElement'));
//   const cleanup = shortcutManager.registerShortcut(
//     'ctrlAAction',
//     { key: 'a', windows: ['ctrl'], mac: ['⌘'] },
//     () => console.log('Ctrl + A pressed')
//   );

//   return () => shortcutManager.unregisterShortcut('ctrlAAction'); // 组件卸载时清理
// }, []);

// 通用操作
// copy: {
//   key: "c",
//   mac: "⌘+C",
//   windows: "Ctrl+C",
//   description: "复制",
//   action: () => document.execCommand("copy"),
// },
// paste: {
//   key: "v",
//   mac: "⌘+V",
//   windows: "Ctrl+V",
//   description: "粘贴",
//   action: () => document.execCommand("paste"),
// },
// cut: {
//   key: "x",
//   mac: "⌘+X",
//   windows: "Ctrl+X",
//   description: "剪切",
//   action: () => document.execCommand("cut"),
// },
// undo: {
//   key: "z",
//   mac: "⌘+Z",
//   windows: "Ctrl+Z",
//   description: "撤销",
//   action: () => document.execCommand("undo"),
// },
// redo: {
//   key: "y",
//   mac: "⌘+Shift+Z",
//   windows: "Ctrl+Y",
//   description: "重做",
//   action: () => document.execCommand("redo"),
// },
// save: {
//   key: "s",
//   mac: "⌘+S",
//   windows: "Ctrl+S",
//   description: "保存",
//   action: () => console.log("Save"),
// },
// find: {
//   key: "f",
//   mac: "⌘+F",
//   windows: "Ctrl+F",
//   description: "查找",
//   action: () => console.log("Find"),
// },
// // 编辑器特定操作
// delete: {
//   key: "Backspace",
//   mac: "Backspace",
//   windows: "Delete",
//   description: "删除",
//   action: () => console.log("Delete"),
// },
// duplicate: {
//   key: "d",
//   mac: "⌘+D",
//   windows: "Ctrl+D",
//   description: "复制组件",
//   action: () => console.log("Duplicate"),
// },
// preview: {
//   key: "p",
//   mac: "⌘+P",
//   windows: "Ctrl+P",
//   description: "预览",
//   action: () => console.log("Preview"),
// },
// // 视图操作
// zoomIn: {
//   key: "+",
//   mac: "⌘++",
//   windows: "Ctrl++",
//   description: "放大",
//   action: () => console.log("Zoom In"),
// },
// zoomOut: {
//   key: "-",
//   mac: "⌘+-",
//   windows: "Ctrl+-",
//   description: "缩小",
//   action: () => console.log("Zoom Out"),
// },
// resetZoom: {
//   key: "0",
//   mac: "⌘+0",
//   windows: "Ctrl+0",
//   description: "重置缩放",
//   action: () => console.log("Reset Zoom"),
// },
