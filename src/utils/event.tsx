import { useCallback } from "react";
import { message } from "@/store/hooks";
import { publish } from "./drive";
import { getGlobalForm } from './form';

// 自定义Hook: useEvents
const useEvents = (eventConfig: any) => {
  return Object.keys(eventConfig).reduce((acc, eventType) => {
    const config = eventConfig[eventType];

    if (config.actions) {
      acc[eventType] = useCallback(
        (event: any, data: any) => {
          // 确保每次事件触发时都会执行此代码块
          config.actions.forEach((action: any) =>
            executeAction(event, action, data)
          );
        },
        [config.actions] // 只有当actions变化时才更新回调函数
      );
    }
    return acc;
  }, {});
};

// 执行单个动作
function executeAction(event: any, action: any, data: any) {
  console.log({ event, action, data });
  switch (action.actionType) {
    case "message":
      showMessage(event, action, data);
      break;
    case "ajax":
      sendAjaxRequest(event, action.api, action.data, data);
      break;
    case "form":
      formAction(event, action, data);
      break;
    case "dialog":
      openDialog(event, action.dialog, data);
      break;
    case "url":
      navigateToUrl(event, action.args, data);
      break;
    case "publish":
      publishEvent(event, action.args, data);
      break;
    case "component":
      componentAction(event, action, data);
      break;

    default:
      console.warn(`Unsupported action type: ${action.actionType}`);
  }

  // 如果有表达式条件，检查是否满足
  if (action.expression) {
    const expressionResult = evaluateExpression(action.expression, event, data);
    if (!expressionResult) return;
  }
}

// 辅助函数：动态构建事件处理器对象
const getDynamicEvents = (eventsObj, context) => {
  return Object.keys(eventsObj).reduce((acc, eventType) => {
    acc[eventType] = (event) => eventsObj[eventType](event, context);
    return acc;
  }, {});
};

const showMessage = (event, action, data) => {
  const { msgType, args } = action;
  return message[args.msgType](args.content);
};

const sendAjaxRequest = (eventData: any) => { };

const formAction = (event: any, action: any, data: any) => {
  const { nodeType, args } = action;
  const { target } = args
  let values = getGlobalForm(`${target}`)?.getFieldsValue()
  console.log("表单值:",values)
}

const openDialog = (eventData: any) => { };

const navigateToUrl = (eventData: any) => { };

const publishEvent = (event: any, action: any, data: any) => {
  console.log({ action });
  publish({ action, data });
};

const componentAction = (event: any, action: any, data: any) => {
  console.log({ action });
  publish({ action, data });
};

// 获取事件数组
const getEventArray = (events = {}) => {
  return Object.keys(events).map((eventName) => ({
    key: eventName,
    name: eventName,
    description: events[eventName]?.title || "无描述",
    action: events[eventName]?.actions, // actions
    isCustom: events[eventName]?.isCustom || false,
  }));
};

// function executeBrowserAction(action) {
//   const { operation, details } = action.args;

//   if (operation === "navigate") {
//     const { action: navAction, delta, replace } = details;
//     if (navAction === "back" || navAction === "forward") {
//       window.history.go(navAction === "back" ? -delta : delta);
//     }
//   } else if (operation === "refresh") {
//     const { force } = details;
//     window.location.reload(force);
//   }

//   // 如果replace为true，可以结合pushState或replaceState来修改历史记录
//   if (details.replace) {
//     // 根据具体需求实现
//   }
// }

// // 示例调用
// const browserAction = {
//   actionType: "browser",
//   args: {
//     operation: "navigate",
//     details: {
//       action: "back",
//       delta: 1,
//       replace: false,
//     },
//   },
// };
// executeBrowserAction(browserAction);

const eventEnums = [
  {
    label: "点击事件",
    value: "onClick",
  },
  {
    label: "双击事件",
    value: "onDoubleClick",
  },
  {
    label: "鼠标按下",
    value: "onMouseDown",
  },
  {
    label: "鼠标释放",
    value: "onMouseUp",
  },
  {
    label: "鼠标悬浮",
    value: "onHover",
  },
  {
    label: "鼠标移动",
    value: "onMouseMove",
  },
  {
    label: "鼠标移入",
    value: "onMouseOver",
  },
  {
    label: "鼠标移出",
    value: "onMouseOut",
  },
  {
    label: "鼠标进入",
    value: "onMouseEnter",
  },
  {
    label: "鼠标离开",
    value: "onMouseLeave",
  },
  {
    label: "右键点击",
    value: "onContextMenu",
  },
  {
    label: "获得焦点",
    value: "onFocus",
  },
  {
    label: "失去焦点",
    value: "onBlur",
  },
  {
    label: "获得焦点",
    value: "onFocusIn",
  },
  {
    label: "失去焦点",
    value: "onFocusOut",
  },
  {
    label: "键盘按下",
    value: "onKeyDown",
  },
  {
    label: "键盘释放",
    value: "onKeyUp",
  },
  {
    label: "触摸开始",
    value: "onTouchStart",
  },
  {
    label: "触摸移动",
    value: "onTouchMove",
  },
  {
    label: "触摸结束",
    value: "onTouchEnd",
  },
  {
    label: "触摸取消",
    value: "onTouchCancel",
  },
  {
    label: "表单提交",
    value: "onSubmit",
  },
  {
    label: "表单重置",
    value: "onReset",
  },
  {
    label: "表单验证",
    value: "onInvalid",
  },
  {
    label: "表单change",
    value: "onChange",
  },
  {
    label: "表单input",
    value: "onInput",
  },
  {
    label: "表单formdata",
    value: "onFormdata",
  },
];

const nodeTypes = [
  {
    key: "navigate",
    label: "路由跳转",
  },
  {
    key: "fetch",
    label: "数据请求",
  },
  {
    key: "form",
    label: "表单操作",
  },
  {
    key: "component",
    label: "组件控制",
  },
  {
    key: "notice",
    label: "消息提示",
  },
  {
    key: "send",
    label: "消息发送",
  },
  {
    key: "support",
    label: "辅助操作",
  },
  {
    key: "script",
    label: "函数脚本",
  },
  {
    key: "broadcast",
    label: "广播事件",
  },
  {
    key: "browser",
    label: "浏览器",
  },
  {
    type: "divider",
  },
  {
    key: "other",
    label: "其他",
  },
];

const getNodeTypeName = (key) => {
  let label = "";
  nodeTypes.map((item) => {
    if (item.key === key) {
      label = item.label;
    }
  });
  return label;
};

export {
  useEvents,
  getDynamicEvents,
  getEventArray,
  eventEnums,
  nodeTypes,
  getNodeTypeName,
};

// 用户交互事件

// onClick: 当用户点击元素时触发。
// onDoubleClick 或 onDblClick: 当用户双击元素时触发。
// onMouseDown: 当用户的鼠标按键在元素上按下时触发。
// onMouseUp: 当用户的鼠标按键在元素上释放时触发。
// onMouseMove: 当鼠标指针移动到元素上方时触发。
// onMouseOver: 当鼠标指针移到元素上时触发。
// onMouseOut: 当鼠标指针移出元素时触发。
// onMouseEnter: 当鼠标指针进入元素区域时触发（不冒泡）。
// onMouseLeave: 当鼠标指针离开元素区域时触发（不冒泡）。
// onContextMenu: 当用户右键点击元素显示上下文菜单时触发。
// onFocus: 当元素获得焦点时触发。
// onBlur: 当元素失去焦点时触发。
// onFocusIn: 当元素或其子元素获得焦点时触发（冒泡）。
// onFocusOut: 当元素或其子元素失去焦点时触发（冒泡）。
// onKeyDown: 当用户按下键盘上的键时触发。
// onKeyUp: 当用户释放键盘上的键时触发。
// onTouchStart: 当触摸设备上的手指首次接触屏幕时触发。
// onTouchMove: 当触摸设备上的手指在屏幕上移动时触发。
// onTouchEnd: 当触摸设备上的手指离开屏幕时触发。
// onTouchCancel: 当触摸操作被取消时触发。

// 表单事件
// onChange: 当表单元素的值发生改变时触发。
// onInput: 当用户编辑 <input>, <select>, 或 <textarea> 的值时触发。
// onSubmit: 当表单提交时触发。
// onReset: 当表单重置时触发。
// onInvalid: 当表单验证失败时触发。
// onFormdata: 当表单数据收集时触发。
