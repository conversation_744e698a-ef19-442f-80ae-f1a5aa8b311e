import * as antdIcons from "@ant-design/icons";

const iconsMap = {
  ...antdIcons,
};

const routes2menu = (routes) => {
  const result = [];

  for (const route of routes) {
    const { children, key, title, icon, hash_sign, path } = route;

    const Component = icon ? iconsMap[icon] : iconsMap["QuestionOutlined"];

    const menuItem = {
      key,
      icon: <Component />,
      label: title,
      path: hash_sign || path,
    };

    if (children && children.length > 0) {
      const subMenuItems = routes2menu(children);
      if (subMenuItems.length > 0) {
        menuItem.children = subMenuItems;
      }
    }

    result.push(menuItem);
  }

  return result;
};

const genMenuToTree = (items = []) => {
  const result = []; // 存放结果集
  const itemMap = {}; // 存放路径

  // 先转化为map存储，并增加key键
  for (const item of items) {
    item.children = [];
    item.key = item.id; // 增加key键，值为id的值
    itemMap[item.id] = item;
  }

  for (const item of items) {
    const pid = item.pid;

    if (pid === 0) {
      result.push(item);
    } else {
      if (!itemMap[pid]) {
        // 如果父节点不存在，则忽略此节点
        continue;
      }
      itemMap[pid].children.push(item);
    }
  }

  // 对每个层级进行sort排序
  const sortItems = (arr) => {
    arr.sort((a, b) => a.sort - b.sort);
    arr.forEach((item) => {
      if (item.children && item.children.length > 0) {
        sortItems(item.children);
      } else {
        item.isLeaf = true;
      }
    });
  };

  sortItems(result);

  return result;
};

export { genMenuToTree, routes2menu };
