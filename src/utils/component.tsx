import * as win from "@/component/Win"; // 基础组件
import * as chart from "@/component/Chart"; // 图表组件
import * as twin from "@/component/Twin"; // 大屏组件
const componentMap = { ...win, ...chart, ...twin };

const componentTag = (componentName: string) => {
  let TagName = null;
  if (componentName && componentName.indexOf(".") !== -1) {
    const componentNames = componentName.split(".");
    TagName = componentMap[componentNames[0]][componentNames[1]];
  } else {
    TagName = componentMap[componentName];
    if (!TagName) {
      console.error(`${componentName}: 组件类型未设定，请检查！`); // 不中断程序
      return null;
    }
  }

  return TagName;
};

export { componentTag };
