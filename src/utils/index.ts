import { customAlphabet } from "nanoid";
import { Storage } from "@/utils/storage";
import { produce } from "immer";

// 生成唯一ID
const genUniqueId = () => {
  return genAlphabetUnqiueId();
};

// 树形转换
const transformTree = (data = []) => {
  // 检查数据是否为数组，确保我们处理的是期望的数据结构
  if (!Array.isArray(data)) {
    throw new Error("Input data must be an array.");
  }
  return produce(data, (draft) => {
    return draft.map((item) => {
      // 创建一个新的对象，用于存放转换后的数据
      return produce(item, (transformedItem) => {
        // 如果键是'label'，则替换为'title'
        if (transformedItem.hasOwnProperty("label")) {
          transformedItem.title = transformedItem.label;
          delete transformedItem.label;
        }

        transformedItem.key = transformedItem.uuid;
        // 判断当前项是否为叶子节点
        // 假设叶子节点的判断标准是没有children属性，或者children为空数组
        if (
          !transformedItem.hasOwnProperty("children") ||
          (Array.isArray(transformedItem.children) &&
            transformedItem.children.length === 0) ||
          typeof transformedItem.children === "string"
        ) {
          delete transformedItem.children; // 兼容处理 children 为字符串时
          transformedItem.isLeaf = true;
        } else if (Array.isArray(transformedItem.children)) {
          // 如果有children，则递归调用transformData处理子数组
          transformedItem.children = transformTree(transformedItem.children);
        }
      });
    });
  });
};

/* 通过元素的 data-dnd 属性查找 */
const getParentDndElement = (element) => {
  // 从当前元素开始向上查找
  let currentElement = element;
  while (currentElement) {
    // 如果当前元素的 data-dnd 属性存在,则返回该元素
    if (currentElement.getAttribute("data-dnd")) {
      return currentElement;
    }
    // 如果没有找到,则向上移动到父元素
    currentElement = currentElement.parentElement;
  }
  // 如果没有找到匹配的元素,则返回 null
  return null;
};

/* UUID 获取对象元素-在 json-schema 数据上获取，非原组件 schema */
const getObjectById = (data, uuid) => {
  if (!Array.isArray(data)) {
    throw new Error("Input data must be an array.");
  }

  const traverse = (arr) => {
    for (const item of arr) {
      if (item.uuid === uuid) {
        return item;
      }
      if (Array.isArray(item.children)) {
        const result = traverse(item.children);
        if (result) {
          return result;
        }
      }
    }
    return null;
  };

  return traverse(data);
};

/* 根据 UUID 删除对象并返回最新对象 */
const removeElementByUuid = (schema, uuid) => {
  const traverseAndRemove = (elements) => {
    for (let i = 0; i < elements.length; i++) {
      if (elements[i].uuid === uuid) {
        elements.splice(i, 1);
        i--;
      } else if (elements[i].children) {
        traverseAndRemove(elements[i].children);
      }
    }
  };

  const newSchema = produce(schema, (draft) => {
    traverseAndRemove(draft);
  });

  return newSchema;
};

/* 根据 UUID 复制对象-插入点为之前元素的后面-并返回最新数据 */
const copyElementByUuid = (schema, uuid) => {
  // const deepCopyAndModify = (element) => ({
  //   ...element,
  //   uuid: genUniqueId(),
  //   name: genAlphabetFieldId(),
  //   children:
  //     element?.children && typeof element?.children !== "string"
  //       ? element?.children?.map(deepCopyAndModify)
  //       : element?.children,
  // });

  const deepCopyAndModify = (element) => {
    let newProps = { ...element.props };
    let oldPropName = element.name;
    let newPropName = genAlphabetFieldId();
    if (newProps[oldPropName]) {
      newProps[newPropName] = newProps[oldPropName];
      delete newProps[oldPropName];
    }
    if (newProps.className && typeof newProps.className === "string") {
      newProps.className = newProps.className.replace(oldPropName, newPropName);
    }

    let newChildren =
      element?.children && typeof element?.children !== "string"
        ? element?.children?.map(deepCopyAndModify)
        : element?.children;

    return {
      ...element,
      uuid: genUniqueId(),
      name: newPropName,
      props: newProps,
      children: newChildren,
    };
  };

  // 递归查找并插入元素
  const findAndInsertAfter = (array, targetUuid, elementToInsert) => {
    for (let i = 0; i < array.length; i++) {
      const item = array[i];
      if (item?.uuid === targetUuid) {
        // 插入新元素到目标元素之后
        array.splice(i + 1, 0, elementToInsert);
        return true;
      } else if (Array.isArray(item.children)) {
        // 如果当前项有 children 数组，则递归查找
        if (findAndInsertAfter(item.children, targetUuid, elementToInsert)) {
          return true;
        }
      }
    }
    return false;
  };

  // 创建复制的对象
  const originalElement = getObjectById(schema, uuid);
  const copiedElement = deepCopyAndModify(originalElement);

  // 使用 produce 来创建新的 schema，同时保持不可变性
  const newSchema = produce(schema, (draft) => {
    // 递归查找目标 UUID 并插入复制的对象
    findAndInsertAfter(draft, uuid, copiedElement);
  });

  return newSchema;
};

/* 根据 UUID 生成路径数组 */
const getParentPathByUuid = (schema = [], uuid) => {
  const parentPath = [];

  const traverseAndGetParents = (elements, currentPath = []) => {
    for (let i = 0; i < elements.length; i++) {
      const element = elements[i];
      const currentElementPath = [...currentPath, element];

      if (element.uuid === uuid) {
        parentPath.push(...currentElementPath);
        return;
      }

      if (element.children) {
        traverseAndGetParents(element.children, currentElementPath);
      }
    }
  };

  traverseAndGetParents(schema);
  return parentPath;
};

/* 根据 UUID 获取父级元素 */
const getFatherByUuid = (schema, uuid) => {
  let parent = null;

  const traverseAndFindParent = (elements, currentParent = null) => {
    for (let i = 0; i < elements.length; i++) {
      const element = elements[i];

      if (element.uuid === uuid) {
        parent = currentParent;
        return;
      }

      if (element.children) {
        traverseAndFindParent(element.children, element);
      }
    }
  };

  traverseAndFindParent(schema);
  return parent;
};

/* 根据 uuid 优先获取父级元素，没有就获取同级后一个元素，没有就获取同级前一个元素 */
const getFatherOrSiblingByUuid = (schema, uuid) => {
  let result = null;

  const traverseAndFind = (elements, currentParent = null) => {
    for (let i = 0; i < elements.length; i++) {
      const element = elements[i];

      if (element.uuid === uuid) {
        // 优先获取父级元素
        if (currentParent) {
          result = currentParent;
        } else {
          // 没有父级元素就获取同级后一个元素
          result = elements[i + 1];
        }
        return;
      }

      if (element.children) {
        traverseAndFind(element.children, element);
      }
    }

    // 没有父级元素也没有同级后一个元素就获取同级前一个元素
    if (!result) {
      result = elements[elements.length - 1];
    }
  };

  traverseAndFind(schema);
  return result ? result : undefined;
};

/* 查找元素并在前-中-后插入新元素 */
const insertElement = (jsonSchema, uuid, newElement, position) => {
  if (!position) {
    return jsonSchema; // position 不存在时不变更数据
  }
  return produce(jsonSchema, (draft) => {
    const findAndInsert = (arr) => {
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].uuid === uuid) {
          switch (position) {
            case "before":
              arr.splice(i, 0, newElement);
              break;
            case "after":
              arr.splice(i + 1, 0, newElement);
              break;
            case "inside":
              if (arr[i].children) {
                if (Array.isArray(arr[i].children)) {
                  arr[i].children.push(newElement);
                } else {
                  arr[i].children = [newElement];
                }
              } else {
                arr[i].children = [newElement];
              }
              break;
            default:
              throw new Error("Invalid position");
          }
          return;
        }

        if (arr[i].children) {
          findAndInsert(arr[i].children);
        }
      }
    };

    findAndInsert(draft);
  });
};

/* 移动元素并插入 */
function moveElement(JSONSchema, sourceUuid, targetUuid, position) {
  if (!position) {
    return JSONSchema; // position 不存在时不变更数据
  }
  // 1. 找到源元素 数据 sourceSchema
  let sourceSchema = getObjectById(JSONSchema, sourceUuid);
  // 2.将找到的数据缓存后，删除找到的数据节点并返回最新的数据
  let betweenSchema = removeElementByUuid(JSONSchema, sourceUuid);
  // 3. 将找到的源元素插入指定位置并返回最新数据
  let newSchema = insertElement(
    betweenSchema,
    targetUuid,
    sourceSchema,
    position
  );
  return newSchema;
}

/**
 * 修改对象的某个属性值
 * @param data - 要修改的数据
 * @param id - 要修改的对象的 UUID
 * @param key - 要修改的对象的某个属性
 * @param newValue - 新的值
 * @returns - 修改后的数据
 */
const updateProperty = (data, id, key, newValue) => {
  return produce(data, (draft) => {
    const findAndUpdate = (items) => {
      for (const item of items) {
        // 如果找到匹配的 UUID，更新指定属性的值
        if (item.uuid === id) {
          item[key] = newValue; // 我们要修改的属性是 `key`
          return true; // 找到并更新
        }
        // 如果有子项，递归地查找
        if (item.children) {
          const found = findAndUpdate(item.children);
          if (found) return true; // 如果在子项中找到并更新
        }
      }
      return false; // 未找到
    };

    findAndUpdate(draft);
  });
};

/**
 * 修改对象的 name 相关属性
 * 该函数可以修改对象的 name 属性，并且也可以修改 props.name 属性
 * @param data - 要修改的数据
 * @param id - 要修改的对象的 UUID
 * @param newName - 新的值
 * @returns - 修改后的数据
 */
const updateNameByUuid = (dataArray, id, newName) =>
  produce(dataArray, (draft) => {
    // 使用箭头函数定义一个递归函数来处理对象及其children
    const traverseAndRename = (obj) => {
      // 如果当前对象有uuid并且匹配目标uuid，则更新name和检查并更新props.name

      if (obj.uuid === id) {
        let originName = obj.name;

        if (obj && "name" in obj) {
          obj.name = newName;
        }
        if (obj && obj.props && originName in obj.props) {
          let originValue = obj.props[originName];
          obj.props[newName] = originValue;
          delete obj.props[originName];
        }

        // 如果当前对象有children属性，则递归处理每个子对象
        if (Array.isArray(obj?.children)) {
          obj.children.forEach(traverseAndRename);
        }
      }
    };

    // 使用some遍历传入的数组，对每个对象调用traverseAndRename，找到即停止
    draft.some((item) => {
      traverseAndRename(item);
      return item?.uuid === id; // 找到后停止遍历
    });
  });

/* 修改props值 */
const updateProps = (propsArray, uuid, propName, newValue) => {
  return propsArray.map((item) => {
    if (item.uuid === uuid) {
      return Object.assign({}, item, {
        props: Object.assign({}, item.props, {
          [propName]: newValue,
        }),
      });
    } else if (Array.isArray(item.children)) {
      return Object.assign({}, item, {
        children: updateProps(item.children, uuid, propName, newValue),
      });
    }
    return item;
  });
};

// 示例调用
// 假设已经有一个多层级数组 data
// const schema = [/* 你的多层级数组数据 */];
// const uuid = "exampleUuid";
// const event = "onClick";
// const actionId = null;
// const newAction = { actionId: "newActionId", /* 其他属性 */ };
const findAndInsertAction = (
  schema: any[],
  uuid: string,
  event: string,
  actionId: string | undefined,
  newAction: any
) => {
  return produce(schema, (draftSchema) => {
    // 递归遍历草稿对象
    const traverse = (draftArray: any[]): boolean => {
      for (const draftItem of draftArray) {
        if (draftItem.uuid === uuid) {
          // 确保 events 结构存在
          draftItem.events ??= {};
          draftItem.events[event] ??= { actions: [] };

          // 获取 actions 数组引用
          const actions = draftItem.events[event].actions;

          if (actions.length === 0) {
            actions.push(newAction);
          } else if (actionId !== undefined) {
            const index = actions.findIndex(
              (a: any) => a.actionId === actionId
            );
            if (index !== -1) {
              actions.splice(index + 1, 0, newAction);
            }
          } else {
            actions.push(newAction);
          }
          return true; // 找到目标后提前退出
        }

        // 递归遍历子节点
        if (draftItem.children?.length > 0) {
          const found = traverse(draftItem.children);
          if (found) return true;
        }
      }
      return false;
    };

    traverse(draftSchema);
  });
};

// 修改 action
const findAndModifyAction = (
  schema: any[],
  uuid: string,
  event: string,
  actionId: string,
  updatedAction: any
) => {
  return produce(schema, (draftSchema) => {
    // 递归遍历草稿对象
    const traverse = (draftArray: any[]): boolean => {
      for (const draftItem of draftArray) {
        if (draftItem.uuid === uuid) {
          // 确保 events 结构存在
          draftItem.events ??= {};
          draftItem.events[event] ??= { actions: [] };

          // 获取 actions 数组引用
          const actions = draftItem.events[event].actions;

          // 找到要修改的 action 的索引
          const index = actions.findIndex((a: any) => a.actionId === actionId);
          if (index !== -1) {
            // 更新 action
            actions[index] = { ...actions[index], ...updatedAction };
          }
          return true; // 找到目标后提前退出
        }

        // 递归遍历子节点
        if (draftItem.children?.length > 0) {
          const found = traverse(draftItem.children);
          if (found) return true;
        }
      }
      return false;
    };

    traverse(draftSchema);
  });
};

// 删除 action
const findAndDeleteAction = (
  schema: any[],
  uuid: string,
  event: string,
  actionId: string
) => {
  return produce(schema, (draftSchema) => {
    // 递归遍历草稿对象
    const traverse = (draftArray: any[]): boolean => {
      for (const draftItem of draftArray) {
        if (draftItem.uuid === uuid) {
          // 确保 events 结构存在
          draftItem.events ??= {};
          draftItem.events[event] ??= { actions: [] };

          // 获取 actions 数组引用
          const actions = draftItem.events[event].actions;

          // 找到要删除的 action 的索引
          const index = actions.findIndex((a: any) => a.actionId === actionId);
          if (index !== -1) {
            // 删除 action
            actions.splice(index, 1);
          }
          return true; // 找到目标后提前退出
        }

        // 递归遍历子节点
        if (draftItem.children?.length > 0) {
          const found = traverse(draftItem.children);
          if (found) return true;
        }
      }
      return false;
    };

    traverse(draftSchema);
  });
};

const isString = (children) => {
  return typeof children === "string";
};

const apiRoute = (prefix) => {
  let project = "";
  if (prefix === "project") {
    project = `/${Storage.getItem("app")?.uid}`;
  }

  return project;
};

// 生成只包含[小写字母和数字]
const genAlphabetId = (length = 10) => {
  const nanoid = customAlphabet("1234567890abcdefghijklmnopqrstuvwxyz", length);
  return nanoid();
};

// 生成只包含[小写字母]
const genAlphabetMinId = (length = 18) => {
  const nanoid = customAlphabet("abcdefghijklmnopqrstuvwxyz", length);
  return nanoid();
};

// 生成只包含[大小写字母和数字]
const genAlphabetMaxId = (length = 18) => {
  const nanoid = customAlphabet(
    "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",
    length
  );
  return nanoid();
};

// 生成变量名(首字母不能为数字)
const genAlphabetFieldId = (length = 6) => {
  // 分别定义字母和字母加数字的字符集
  const letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
  const allChars = `0123456789${letters}`;

  // 创建两个不同的 alphabet 实例：一个用于首字符，另一个用于其余字符
  const firstChar = customAlphabet(letters, 1);
  const restChars = customAlphabet(allChars, length - 1);
  // 拼接首字符和剩余字符来生成最终的 ID
  return firstChar() + restChars();
};

// 生成uid(只能是小写宽字符字母加宽字符数字，首字母不能为数字)
const genAlphabetUnqiueId = (length = 21) => {
  // 分别定义字母和字母加数字的字符集
  const letters = "abcdefghmnopqsuvwxyz";
  const allChars = `${letters}023456789`;

  // 创建两个不同的 alphabet 实例：一个用于首字符，另一个用于其余字符
  const firstChar = customAlphabet(letters, 1);
  const restChars = customAlphabet(allChars, length - 1);
  // 拼接首字符和剩余字符来生成最终的 ID
  return firstChar() + restChars();
};

/**
 * Analyzes a URL path to determine if it's a form or page route and extracts the relevant parameters
 *
 * @param {string} [url=location.pathname] - The URL path to analyze, defaults to current location pathname
 * @returns {Object} An object containing the type, id, action, and optional dataId
 * @example
 * parsePathParams('/app/xyz/page/123/design')          // returns { type: 'form', id: '123', action: 'design' }
 * parsePathParams('/app/xyz/page/456/runtime')         // returns { type: 'page', id: '456', action: 'runtime' }
 * parsePathParams('/app/xyz/page/789/preview')         // returns { type: 'page', id: '789', action: 'preview' }
 * parsePathParams('/app/xyz/form/123/design')          // returns { type: 'page', id: '123', action: 'design' }
 * parsePathParams('/app/xyz/page/456/data/123456')     // returns { type: 'page', id: '456', action: 'data', dataId: '123456' }
 */
const parsePathParams = (
  url: string = location.pathname
): {
  type: "form" | "page" | "unknown";
  id: string | null;
  action: string | null;
  dataId?: string;
} => {
  // 创建一个 URL 对象以解析传入的 URL 字符串
  const urlObj = new URL(url, "https://example.com");

  // 获取路径名（不包括查询参数或片段标识符）
  const pathname = urlObj.pathname;

  // 定义匹配模式，包含所有可能的路径模式
  const formPattern = /\/app\/[^/]+\/form\/([^/]+)\/([^/]+)/;
  const pagePattern = /\/app\/[^/]+\/page\/([^/]+)\/([^/]+)/;
  const pageDataPattern = /\/app\/[^/]+\/page\/([^/]+)\/data\/([^/]+)/;

  // 测试路径是否符合各种模式
  const formMatch = pathname.match(formPattern);
  const pageMatch = pathname.match(pagePattern);
  const pageDataMatch = pathname.match(pageDataPattern);

  if (formMatch) {
    return { type: "form", id: formMatch[1], action: formMatch[2] };
  } else if (pageDataMatch) {
    return {
      type: "page",
      id: pageDataMatch[1],
      action: "data",
      dataId: pageDataMatch[2],
    };
  } else if (pageMatch) {
    return { type: "page", id: pageMatch[1], action: pageMatch[2] };
  } else {
    return { type: "unknown", id: null, action: null };
  }
};

// 行内样式转字符串对象
const styleToObject = (str) => {
  return str
    .trim()
    .split(";")
    .map((pair) => pair.trim())
    .filter((pair) => pair)
    .reduce((obj, pair) => {
      const [property, value] = pair.split(":").map((item) => item.trim());
      if (property && value) {
        const camelCaseProperty = property.replace(
          /-([a-z])/g,
          (match, letter) => letter.toUpperCase()
        );
        obj[camelCaseProperty] = value;
      }
      return obj;
    }, {});
};

// 字符串对象转行内样式
const objectToStyle = (styleObj = {}) => {
  let inlineString = "";
  for (let key in styleObj) {
    // 确保当前属性是对象自身的属性，而不是继承来的
    if (styleObj.hasOwnProperty(key)) {
      // 将键名转换为连字符连接的形式（例如：backgroundColor 转换为 background-color）
      let formattedKey = key.replace(/([A-Z])/g, "-$1").toLowerCase();
      inlineString += `${formattedKey}:${styleObj[key]};`;
    }
  }
  return inlineString;
};

// 将类名字符串转换为数组
const classNameStr2Arr = (str = "") => {
  // 先使用 match 方法和正则表达式匹配非空字符串
  let parts = str.match(/\S+/g) || [];
  // 将匹配到的非空字符串转换为包含 label 和 value 的对象数组
  let result = parts.map((part) => ({ label: part, value: part }));
  return result;
};

// 类型定义
interface ValueWithUnit {
  number: number;
  unit?: string;
}

interface FontStyle {
  fontFamily?: string;
  fontWeight?: string | number;
  fontSize?: ValueWithUnit;
  color?: string;
  letterSpacing?: ValueWithUnit;
  lineHeight?: ValueWithUnit;
  textAlign?: string;
  textDecoration?: string;
  writingMode?: ValueWithUnit;
  fontStyle?: string;
}

interface BoxShadow {
  color: string;
  xaxis: ValueWithUnit;
  yaxis: ValueWithUnit;
  blur: ValueWithUnit;
  spread: ValueWithUnit;
}

interface Border {
  position: 'all' | 'top' | 'right' | 'bottom' | 'left';
  width: ValueWithUnit;
  line: string;
  color: string;
}

interface BorderRadius {
  selectedValue: 'custom';
  'border-top-right-radius'?: ValueWithUnit;
  'border-top-left-radius'?: ValueWithUnit;
  'border-bottom-right-radius'?: ValueWithUnit;
  'border-bottom-left-radius'?: ValueWithUnit;
}

interface Overflow {
  selectedValue: 'custom';
  'overflow-x'?: string;
  'overflow-y'?: string;
}

interface Background {
  'background-color'?: string;
  'background-image'?: string;
}

interface SpacingValue {
  top?: ValueWithUnit;
  right?: ValueWithUnit;
  bottom?: ValueWithUnit;
  left?: ValueWithUnit;
}

type StyleValue = string | ValueWithUnit | FontStyle | BoxShadow | Border | BorderRadius | Overflow | Background | SpacingValue;

interface StylesObject {
  [state: string]: {
    [property: string]: StyleValue;
  };
}

// 将样式对象转换为 CSS 字符串
const convertToCSS = (stylesObj: StylesObject, name: string): string => {
  if (!stylesObj || !name) return '';
  
  const cssLines: string[] = [];

  // 格式化数值和单位
  const formatValue = (value: ValueWithUnit | string): string => {
    if (typeof value === 'object' && value !== null) {
      return `${value.number ?? 0}${value.unit ?? ''}`;
    }
    return String(value);
  };

  // 处理字体属性
  const handleFont = (font: FontStyle): string[] => {
    const props: Array<[string, any]> = [
      ['font-family', font.fontFamily],
      ['font-weight', font.fontWeight],
      ['font-size', font.fontSize],
      ['color', font.color],
      ['letter-spacing', font.letterSpacing],
      ['line-height', font.lineHeight],
      ['text-align', font.textAlign],
      ['text-decoration', font.textDecoration],
      ['writing-mode', font.writingMode],
      ['font-style', font.fontStyle]
    ];

    return props
      .filter(([, value]) => value !== undefined && value !== null)
      .map(([prop, value]) => {
        const formattedValue = typeof value === 'object' ? formatValue(value) : value;
        return `${prop}: ${formattedValue};`;
      });
  };

  // 处理阴影属性
  const handleBoxShadow = (shadow: BoxShadow): string => {
    const { color, xaxis, yaxis, blur, spread } = shadow;
    if (!color || !xaxis?.number !== undefined || !yaxis?.number !== undefined || 
        !blur?.number !== undefined || !spread?.number !== undefined) {
      return '';
    }
    return `box-shadow: ${formatValue(xaxis)} ${formatValue(yaxis)} ${formatValue(blur)} ${formatValue(spread)} ${color};`;
  };

  // 处理边框属性
  const handleBorder = (border: Border): string[] => {
    if (!border.width?.number !== undefined || !border.line || !border.color) {
      return [];
    }

    const borderValue = `${formatValue(border.width)} ${border.line} ${border.color}`;
    const { position } = border;
    
    if (position === 'all') {
      return [`border: ${borderValue};`];
    }
    
    return [`border-${position}: ${borderValue};`];
  };

  // 处理圆角属性
  const handleRadius = (radius: BorderRadius): string[] => {
    if (radius.selectedValue !== 'custom') return [];

    const sides = [
      'border-top-right-radius',
      'border-top-left-radius', 
      'border-bottom-right-radius',
      'border-bottom-left-radius'
    ] as const;
    
    const values = sides.map(side => radius[side]).filter(Boolean);
    
    if (values.length === 0) return [];
    
    // 检查是否所有值相等
    if (values.every(value => value?.number === values[0]?.number)) {
      return [`border-radius: ${formatValue(values[0]!)};`];
    }
    
    return sides
      .filter(side => radius[side]?.number !== undefined)
      .map(side => `${side}: ${formatValue(radius[side]!)};`);
  };

  // 处理溢出属性
  const handleOverflow = (overflow: Overflow): string[] => {
    if (overflow.selectedValue !== 'custom' || !overflow['overflow-x']) {
      return [];
    }

    const x = overflow['overflow-x'];
    const y = overflow['overflow-y'];
    
    if (x === y) {
      return [`overflow: ${x};`];
    }
    
    return [
      `overflow-x: ${x};`,
      ...(y ? [`overflow-y: ${y};`] : [])
    ];
  };

  // 处理背景属性
  const handleBackground = (background: Background): string[] => {
    const props: string[] = [];
    
    if (background['background-color']) {
      props.push(`background-color: ${background['background-color']};`);
    }
    if (background['background-image']) {
      props.push(`background-image: ${background['background-image']};`);
    }
    
    return props;
  };

  // 处理间距属性 (margin/padding)
  const handleSpacing = (prop: string, value: SpacingValue): string => {
    const sides = ['top', 'right', 'bottom', 'left'] as const;
    const values = sides.map(side => 
      value[side]?.number !== undefined ? formatValue(value[side]!) : null
    );
    
    const nonNullValues = values.filter(Boolean);
    if (nonNullValues.length === 0) return '';
    
    // 标准 CSS 简写规则
    const [top, right, bottom, left] = values;
    
    if (top && right && bottom && left) {
      if (top === right && right === bottom && bottom === left) {
        return `${prop}: ${top};`; // 四边相等
      }
      if (top === bottom && right === left) {
        return `${prop}: ${top} ${right};`; // 上下相等，左右相等
      }
      if (right === left) {
        return `${prop}: ${top} ${right} ${bottom};`; // 左右相等
      }
      return `${prop}: ${top} ${right} ${bottom} ${left};`; // 四边不同
    }
    
    // 处理部分值的情况
    return nonNullValues.length === 1 ? `${prop}: ${nonNullValues[0]};` : '';
  };

  // 属性处理器映射
  const propertyHandlers: Record<string, (value: any) => string | string[]> = {
    font: handleFont,
    'box-shadow': handleBoxShadow,
    border: handleBorder,
    'border-radius': handleRadius,
    overflow: handleOverflow,
    background: handleBackground,
    margin: (value) => handleSpacing('margin', value),
    padding: (value) => handleSpacing('padding', value),
    cursor: (value) => value !== 'default' ? `cursor: ${value};` : ''
  };

  // 处理每个状态的样式
  Object.entries(stylesObj).forEach(([state, styles]) => {
    const className = state === 'default' ? `.${name}` : `.${name}:${state}`;
    const styleLines: string[] = [];

    Object.entries(styles).forEach(([prop, value]) => {
      if (value === undefined || value === null) return;

      const handler = propertyHandlers[prop];
      if (handler) {
        const result = handler(value);
        if (Array.isArray(result)) {
          styleLines.push(...result.filter(Boolean));
        } else if (result) {
          styleLines.push(result);
        }
      } else {
        // 默认处理
        const formattedValue = typeof value === 'object' && 'number' in value 
          ? formatValue(value as ValueWithUnit)
          : String(value);
        styleLines.push(`${prop}: ${formattedValue};`);
      }
    });

    if (styleLines.length > 0) {
      cssLines.push(
        `${className} {`,
        ...styleLines.map(line => `  ${line}`),
        '}'
      );
    }
  });

  return cssLines.join('\n');
};

// css 字符串美化
const formatCSS = (cssString) => {
  // 正则表达式用于匹配选择器和样式规则
  const selectorPattern = /([^{]+)\{([^}]+)\}/g;
  let formattedCSS = "";
  let isFirstSelector = true; // 标记是否是第一个选择器

  // 遍历所有匹配的选择器和样式规则
  let match;
  while ((match = selectorPattern.exec(cssString)) !== null) {
    if (!isFirstSelector) {
      // 每个选择器之间换一行（除了第一个）
      formattedCSS += "\n";
    } else {
      isFirstSelector = false;
    }

    // 添加选择器和左大括号
    formattedCSS += `${match[1].trim()} {\n    `;

    // 处理样式规则，确保每条规则在同一行内，并在结尾换行
    const rules = match[2].trim().split(";").filter(Boolean);
    formattedCSS += rules.join(";\n    ") + ";\n}\n";
  }

  return formattedCSS.trim(); // 移除末尾多余的换行
};

// 创建或者更新 style 标签
const createOrUpdateStyleClass = (className, styleRules) => {
  // 尝试查找已有的 style 元素
  let styleTag = document.getElementById("dynamic-style");
  if (!styleTag) {
    // 如果不存在，创建一个新的 style 元素
    styleTag = document.createElement("style");
    styleTag.type = "text/css";
    styleTag.id = "dynamic-style";
    document.head.appendChild(styleTag);
  }

  // 获取当前 style 元素的内容
  let styleContent = styleTag.innerHTML;

  // 创建一个正则表达式，匹配以 `.className` 开头，包含其所有伪类的样式块
  const pattern = new RegExp(`\\.${className}([^{]*\\{[^}]*\\})`, "g");

  // 使用 replace 方法将匹配到的内容替换为空字符串
  let updatedContent = styleContent.replace(pattern, "");

  // 将新的样式规则添加到更新后的内容中
  updatedContent = `${styleRules}\n${updatedContent.trim()}`;

  // 更新 style 元素的内容，不做多余的格式处理
  styleTag.innerHTML = updatedContent;
};

const removeStyleClass = (className) => {
  let styleTag = document.getElementById("dynamic-style");
  if (styleTag) {
    let styleContent = styleTag.innerHTML;
    let regex = new RegExp(`\\.${className}([^}]*)}`, "g");
    styleContent = styleContent.replace(regex, "");
    styleTag.innerHTML = styleContent;
  }
};

// 物料子分类
const categorizeMaterials = (components) => {
  // 初始化一个对象来保存分类后的组件
  let categorizeMaterials = {};

  // 遍历所有组件
  components.forEach((component) => {
    // 将 group 字段按逗号分割并去除空格
    let categories = component.category
      ? component.category.split(",").map((g) => g.trim())
      : [];

    // 如果没有指定 group，则默认为 'uncategorized'
    if (categories.length === 0) {
      categories = ["uncategorized"];
    }

    // 遍历每个分类
    categories.forEach((category) => {
      // 如果这个分类还没有在结果对象中创建，则初始化为空数组
      if (!categorizeMaterials[category]) {
        categorizeMaterials[category] = [];
      }

      // 将组件添加到对应分类的数据列表中
      categorizeMaterials[category].push(component);
    });
  });

  // 将结果转换为所需的格式
  let result = Object.keys(categorizeMaterials).map((category) => ({
    category, // 分类的键
    data: categorizeMaterials[category],
  }));

  return result;
};

// 用于存储已经加载过的脚本的集合
const loadedStyles = new Set();

const loadStyle = (href, options = {}) => {
  const { timeout = 5000 } = options;
  if (loadedStyles.has(href)) {
    return Promise.resolve();
  }

  return new Promise((resolve, reject) => {
    const link = document.createElement("link");
    link.rel = "stylesheet";
    link.href = href;

    const timeoutId = setTimeout(() => {
      link?.parentNode?.removeChild(link);
      reject(new Error(`Style loading timed out after ${timeout}ms: ${href}`));
    }, timeout);

    const onLoad = () => {
      clearTimeout(timeoutId);
      loadedStyles.add(href);
      resolve(true);
    };

    const onError = () => {
      clearTimeout(timeoutId);
      link?.parentNode?.removeChild(link);
      reject(new Error(`Failed to load style: ${href}`));
    };

    link.onload = onLoad;
    link.onerror = onError;
    document.head.appendChild(link);
  });
};

/**
 * 动态加载外部脚本文件的函数，支持超时处理和配置选项，会检查脚本是否已加载。
 * @param {string} src - 要加载的脚本文件的 URL 地址。
 * @param {Object} [options={}] - 配置选项对象。
 * @param {number} [options.timeout=5000] - 超时时间，单位为毫秒，默认为 5000 毫秒。
 * @returns {Promise} - 一个 Promise 对象，当脚本成功加载时会被 resolve，加载失败或超时会被 reject。
 */
const loadedScripts = new Set();

const loadScript = (src, options = {}) => {
  const { timeout = 5000 } = options;

  if (loadedScripts.has(src)) {
    return Promise.resolve();
  }

  return new Promise((resolve, reject) => {
    const script = document.createElement("script");
    script.src = src;

    const timeoutId = setTimeout(() => {
      script?.parentNode?.removeChild(script);
      reject(new Error(`Script loading timed out after ${timeout}ms: ${src}`));
    }, timeout);

    const onLoad = () => {
      clearTimeout(timeoutId);
      loadedScripts.add(src);
      resolve(true);
    };

    const onError = () => {
      clearTimeout(timeoutId);
      script?.parentNode?.removeChild(script);
      reject(new Error(`Failed to load script: ${src}`));
    };

    script.onload = onLoad;
    script.onerror = onError;

    document.head.appendChild(script);
  });
};


export {
  genUniqueId,
  transformTree,
  getParentDndElement,
  getObjectById,
  getFatherOrSiblingByUuid,
  removeElementByUuid,
  copyElementByUuid,
  getParentPathByUuid,
  getFatherByUuid,
  insertElement,
  moveElement,
  updateNameByUuid,
  updateProperty,
  updateProps,
  findAndInsertAction,
  findAndModifyAction,
  findAndDeleteAction,
  isString,
  apiRoute,
  genAlphabetId,
  genAlphabetMinId,
  genAlphabetMaxId,
  genAlphabetFieldId,
  parsePathParams,
  styleToObject,
  objectToStyle,
  classNameStr2Arr,
  removeStyleClass,
  convertToCSS,
  formatCSS,
  createOrUpdateStyleClass,
  categorizeMaterials,
  loadStyle,
  loadScript
};
