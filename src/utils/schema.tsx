import schemas from "@/help/win";
/**
 * 通过组件名称获取schema (组件原始 schema)
 * @param schemas  schema数组
 * @param componentName  组件名称
 * @returns  该组件的schema
 */
const getComponentSchema = (componentName: any) => {
  return schemas.find((schema) => schema?.componentName === componentName);
};

/**
 * 通过组件名称获取schema configure props
 * @param schemas  schema数组
 * @param componentName  组件名称
 * @returns  该组件的configure props schema
 */
const getComponentConfigurePropsSchema = (componentName: any) => {
  // 通过使用findOnce来提高性能
  const targetSchema = schemas.find(
    (schema) => schema?.componentName === componentName
  );
  return targetSchema?.configure?.props || [];
};

/**
 * 通过组件名称获取schema configure
 * @param componentName  组件名称
 * @returns  该组件的configure props schema
 */
const getComponentConfigure = (componentName: any) => {
  // 通过使用findOnce来提高性能
  const targetSchema = schemas.find(
    (schema) => schema?.componentName === componentName
  );
  return targetSchema?.configure || {};
};

/**
 * 通过组件名称获取schema nestingRule
 * @param componentName  组件名称
 * @returns  该组件的configure props schema
 */
const getComponentNestingRule = (componentName: any) => {
  // 通过使用findOnce来提高性能
  const targetSchema = schemas.find(
    (schema) => schema?.componentName === componentName
  );
  return targetSchema?.configure?.component?.nestingRule || {};
};

/**
 * 通过组件名称获取schema中的props
 * @param schemas  schema数组
 * @param componentName  组件名称
 * @returns  该组件的props
 */
const getComponentProps = (componentName: any) => {
  let props = {};
  const targetSchema = schemas.find(
    (schema) => schema?.componentName === componentName
  );
  if (targetSchema) {
    targetSchema?.props?.forEach((prop: { name: string | number; defaultValue: any; }) => {
      (props as any)[prop?.name] = prop?.defaultValue;
    });
  }
  return props;
};

/**
 * 判断组件是否是容器组件
 * @param {schema} schema  schema 数据
 * @returns {boolean}  是否是容器组件
 */
const isContainerComponent = (schema: any) => {
  return schema?.configure?.component?.isContainer;
};

/**
 * 判断组件是否有 key 属性
 * @param {Schema} props  props 数据
 * @returns {boolean}  是否是容器组件
 */
const haveKeyInComponent = (props = [], key: string) => {
  return props?.some((item: any) => item.name === key) || false;
};

/**
 * 通过组件名称获取组件集合
 * @param name 组件名称
 * @param jsonSchema schema数组
 * @returns 匹配的组件集合
 */
const getComponentsByName = (name: string, jsonSchema: any[]): any[] => {
  const result: any[] = [];

  const traverse = (items: any[]) => {
    items.forEach(item => {
      // 检查当前组件名称是否匹配
      if (item.componentName === name) {
        result.push(item);
      }
      // 递归检查子组件
      if (item.children && Array.isArray(item.children) && item.children.length > 0) {
        traverse(item.children);
      }
    });
  };

  traverse(jsonSchema);
  return result;
};

/**
 * 通过组件ID获取组件
 * @param id 组件ID (uuid)
 * @param jsonSchema schema数组
 * @returns 匹配的组件
 */
const getComponentById = (id: string, jsonSchema: any[]): any | null => {
  let result: any = null;

  const traverse = (items: any[]) => {
    for (const item of items) {
      if (item.uuid === id) {
        result = item;
        return;
      }
      if (item.children && Array.isArray(item.children) && item.children.length > 0) {
        traverse(item.children);
      }
    }
  };

  traverse(jsonSchema);
  return result;
};

/**
 * 将 jsonSchema 转换为树形数据结构
 * @param jsonSchema schema数组
 * @returns 树形数据结构
 */
const getComponentsTreeDataOptions = (jsonSchema: any[]): any[] => {
  const traverse = (items: any[]): any[] => {
    return items.map(item => ({
      value: item.uuid,
      title: item.title,
      children: item.children && Array.isArray(item.children) && item.children.length > 0
        ? traverse(item.children)
        : undefined
    }));
  };

  return traverse(jsonSchema);
};

export {
  getComponentSchema,
  getComponentConfigure,
  getComponentNestingRule,
  getComponentProps,
  isContainerComponent,
  haveKeyInComponent,
  getComponentConfigurePropsSchema,
  getComponentsByName,
  getComponentById,
  getComponentsTreeDataOptions
};
