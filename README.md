<p align="center">
  <a href="https://winyh.github.io/astx/">
    <img width="300" src="https://github.com/winyh/astx/blob/master/public/warrior.svg">
  </a>
</p>

<h1 align="center">astx</h1>

<p align="center">可视化搭建平台 代码自动生成 Less Code Low Code   代号：X-MAN</p>

<div align="center">

![](https://img.shields.io/github/issues/winyh/astx) ![](https://img.shields.io/github/languages/code-size/winyh/astx) ![](https://img.shields.io/github/stars/winyh/astx) ![](https://img.shields.io/github/last-commit/winyh/astx)

</div>

### 项目目标

基于 React + TypeScript + Vite 技术栈构建一个可视化搭建平台，通过拖拽的方式构建中台，生成完整的项目工程 [wiki](https://github.com/winyh/astx/wiki)

### 项目由来

ASTX 含义：AST 代表抽象语法树， X 代表无限拓展可能. 代码自动生成底层技术原理是 AST, DSL 做功能辅助

无代码编程：可视化配置搭建 -> 云编辑器 -> AI 代码自动生成

> 很多大的平台在开始做这样的可视化搭建平台，节约项目开发成本。在目前来看，没有特别好的系统解决方案。微软的 power，阿里云的云凤蝶，金蝉等


### 配置

```
cp .env.example .env
```
配置单体应用的 URL和KEY （如：supabase）


### 安装

```
cnpm i
```

### 启动

```
npm start
```

### 打包

```
npm build
```

### 文档

文档请查看 [wiki](https://github.com/winyh/astx/wiki)

### 在线 demo

[https://winyh.github.io/astx](https://winyh.github.io/astx/)

### 运行预览

![](https://github.com/winyh/astx/blob/master/public/run.png)

### 本地预览
```
npm install -g serve

serve -s dist   
```

### 贡献代码

欢迎贡献代码，代码规范请查看 [wiki](https://github.com/winyh/astx/wiki)

### Todo List

- 用 Typescript 改写
- 可视化拖拽生成 React.jsx 文件构建中台
- 在线生成完整的工程目录，开箱即用

### 参考平台

| 平台                                    | 说明                  |
| --------------------------------------- | --------------------- |
| [Vuegg](https://github.com/vuegg/vuegg) | 拖拽生成 vue 代码工程 |
| [云凤蝶](https://www.yunfengdie.com/)   | 可视化搭建中台        |

### License

astx is [MIT licensed](https://opensource.org/licenses/MIT).

# React + TypeScript + Vite

This template provides a minimal setup to get React working in Vite with HMR and some ESLint rules.

Currently, two official plugins are available:

- [@vitejs/plugin-react](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react/README.md) uses [Babel](https://babeljs.io/) for Fast Refresh
- [@vitejs/plugin-react-swc](https://github.com/vitejs/vite-plugin-react-swc) uses [SWC](https://swc.rs/) for Fast Refresh

## Expanding the ESLint configuration

If you are developing a production application, we recommend updating the configuration to enable type aware lint rules:

- Configure the top-level `parserOptions` property like this:

```js
export default {
  // other rules...
  parserOptions: {
    ecmaVersion: "latest",
    sourceType: "module",
    project: ["./tsconfig.json", "./tsconfig.node.json"],
    tsconfigRootDir: __dirname,
  },
};
```

- Replace `plugin:@typescript-eslint/recommended` to `plugin:@typescript-eslint/recommended-type-checked` or `plugin:@typescript-eslint/strict-type-checked`
- Optionally add `plugin:@typescript-eslint/stylistic-type-checked`
- Install [eslint-plugin-react](https://github.com/jsx-eslint/eslint-plugin-react) and add `plugin:react/recommended` & `plugin:react/jsx-runtime` to the `extends` list
