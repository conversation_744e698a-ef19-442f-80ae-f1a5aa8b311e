(function(t,n){typeof exports=="object"&&typeof module<"u"?n(exports):typeof define=="function"&&define.amd?define(["exports"],n):(t=typeof globalThis<"u"?globalThis:t||self,n(t.remote={}))})(this,function(t){"use strict";var n={exports:{}},u={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var c;function f(){if(c)return u;c=1;var i=Symbol.for("react.transitional.element"),s=Symbol.for("react.fragment");function o(j,e,r){var l=null;if(r!==void 0&&(l=""+r),e.key!==void 0&&(l=""+e.key),"key"in e){r={};for(var d in e)d!=="key"&&(r[d]=e[d])}else r=e;return e=r.ref,{$$typeof:i,type:j,key:l,ref:e!==void 0?e:null,props:r}}return u.Fragment=s,u.jsx=o,u.jsxs=o,u}var p;function a(){return p||(p=1,n.exports=f()),n.exports}var x=a();const R=i=>{const{label:s}=i,o=()=>{console.log("clicked")};return x.jsxs("button",{onClick:o,children:["Click me",s]})},v=i=>{const s=o=>{console.log(o)};return x.jsx("input",{onChange:s})};t.Button=R,t.Input=v,Object.defineProperty(t,Symbol.toStringTag,{value:"Module"})});
