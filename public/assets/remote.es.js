var l = { exports: {} }, o = {};
/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var x;
function p() {
  if (x) return o;
  x = 1;
  var s = Symbol.for("react.transitional.element"), n = Symbol.for("react.fragment");
  function e(a, r, t) {
    var u = null;
    if (t !== void 0 && (u = "" + t), r.key !== void 0 && (u = "" + r.key), "key" in r) {
      t = {};
      for (var i in r)
        i !== "key" && (t[i] = r[i]);
    } else t = r;
    return r = t.ref, {
      $$typeof: s,
      type: a,
      key: u,
      ref: r !== void 0 ? r : null,
      props: t
    };
  }
  return o.Fragment = n, o.jsx = e, o.jsxs = e, o;
}
var c;
function d() {
  return c || (c = 1, l.exports = p()), l.exports;
}
var R = d();
const v = (s) => {
  const { label: n } = s, e = () => {
    console.log("clicked");
  };
  return /* @__PURE__ */ R.jsxs("button", { onClick: e, children: [
    "Click me",
    n
  ] });
}, k = (s) => {
  const n = (e) => {
    console.log(e);
  };
  return /* @__PURE__ */ R.jsx("input", { onChange: n });
};
export {
  v as Button,
  k as Input
};
